# Faz 5.1: Wallet Sayfası Güncelleme (1 gün)

## 📋 Alt Faz Özeti
MyAccount/Wallet sayfasını RZW bakiye detaylarını gösterecek şekilde güncelleme. Kullanılabilir, kilitli ve toplam bakiye ayrımının net bir şekilde gösterilmesi.

## 🎯 Hedefler
- ✅ Wallet.cshtml sayfası güncelleme
- ✅ WalletPageModel güncelleme
- ✅ RZW özel bakiye kartı ekleme
- ✅ Diğer coinler için basit gösterim
- ✅ CSS styling ekleme

## 📊 Yapılacak İşler

### ✅ YAPILACAKLAR

#### 5.1.1 WalletPageModel Güncelleme

**Dosya**: `src/Areas/MyAccount/Pages/Wallet.cshtml.cs`
```csharp
public class WalletModel : PageModel
{
    private readonly IWalletService _walletService;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly ILogger<WalletModel> _logger;

    public WalletModel(
        IWalletService walletService,
        IStringLocalizer<SharedResource> localizer,
        ILogger<WalletModel> logger)
    {
        _walletService = walletService;
        _localizer = localizer;
        _logger = logger;
    }

    public List<WalletBalanceInfo> WalletBalances { get; set; } = new();
    public WalletBalanceInfo? RzwBalance { get; set; }
    public decimal TotalPortfolioValue { get; set; }
    public string PageTitle { get; set; } = string.Empty;

    public async Task<IActionResult> OnGetAsync()
    {
        try
        {
            var userId = HttpContext.User.GetClaimUserId();
            if (!userId.HasValue)
            {
                return RedirectToPage("/Account/Login");
            }

            PageTitle = _localizer["My Wallet"];

            // Tüm wallet bakiye bilgilerini getir
            WalletBalances = await _walletService.GetUserAllBalanceInfoAsync(userId.Value);

            // RZW özel bilgilerini ayır
            RzwBalance = WalletBalances.FirstOrDefault(w => w.IsRzwToken);

            // Toplam portföy değeri hesaplama (opsiyonel)
            await CalculatePortfolioValueAsync();

            return Page();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading wallet page for user");
            TempData["ErrorMessage"] = _localizer["An error occurred while loading your wallet"];
            return Page();
        }
    }

    private async Task CalculatePortfolioValueAsync()
    {
        try
        {
            // Bu metot coin fiyatları ile portföy değeri hesaplayabilir
            // Şimdilik sadece RZW için basit hesaplama
            TotalPortfolioValue = WalletBalances.Sum(w => w.TotalBalance);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error calculating portfolio value");
            TotalPortfolioValue = 0;
        }
    }

    // AJAX endpoint for real-time balance updates
    public async Task<IActionResult> OnGetBalanceUpdateAsync()
    {
        try
        {
            var userId = HttpContext.User.GetClaimUserId();
            if (!userId.HasValue)
            {
                return new JsonResult(new { success = false, message = "Unauthorized" });
            }

            var balances = await _walletService.GetUserAllBalanceInfoAsync(userId.Value);
            var rzwBalance = balances.FirstOrDefault(w => w.IsRzwToken);

            return new JsonResult(new 
            { 
                success = true, 
                balances = balances.Select(b => new
                {
                    coinCode = b.CoinCode,
                    coinName = b.CoinName,
                    availableBalance = b.AvailableBalance.ToString("N8"),
                    lockedBalance = b.LockedBalance.ToString("N8"),
                    totalBalance = b.TotalBalance.ToString("N8"),
                    hasLockedBalance = b.HasLockedBalance,
                    isRzwToken = b.IsRzwToken,
                    lockedInSavings = b.LockedInSavings?.ToString("N8")
                })
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting balance update");
            return new JsonResult(new { success = false, message = "Error updating balances" });
        }
    }
}
```

#### 5.1.2 Wallet.cshtml Güncelleme

**Dosya**: `src/Areas/MyAccount/Pages/Wallet.cshtml`
```html
@page
@model RazeWinComTr.Areas.MyAccount.Pages.WalletModel
@{
    ViewData["Title"] = Model.PageTitle;
    Layout = "~/Areas/MyAccount/Pages/Shared/_Layout.cshtml";
}

<div class="wallet-container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="page-title">
                    <i class="fas fa-wallet text-primary"></i>
                    @Model.PageTitle
                </h2>
                <p class="page-subtitle">@Localizer["Manage your cryptocurrency balances and savings"]</p>
            </div>
            <div class="header-actions">
                <button type="button" class="btn btn-outline-primary" onclick="refreshBalances()">
                    <i class="fas fa-sync-alt"></i> @Localizer["Refresh"]
                </button>
            </div>
        </div>
    </div>

    <!-- RZW Token Özel Kartı -->
    @if (Model.RzwBalance != null)
    {
        <div class="rzw-balance-card mb-4">
            <div class="card-header">
                <div class="coin-info">
                    <img src="@Model.RzwBalance.IconUrl" alt="RZW" class="coin-icon-large" onerror="this.src='/images/markets/default.png'">
                    <div class="coin-details">
                        <h4 class="coin-name">@Model.RzwBalance.CoinName</h4>
                        <span class="coin-code">@Model.RzwBalance.CoinCode</span>
                        <small class="platform-token">@Localizer["Platform Token"]</small>
                    </div>
                </div>
                <div class="total-value">
                    <span class="total-amount" data-coin="RZW" data-type="total">@Model.RzwBalance.TotalBalance.ToString("N8")</span>
                    <span class="currency">RZW</span>
                </div>
            </div>
            
            <div class="balance-breakdown">
                <div class="balance-item available">
                    <div class="balance-label">
                        <i class="fas fa-coins text-success"></i>
                        @Localizer["Available Balance"]
                    </div>
                    <div class="balance-value">
                        <span data-coin="RZW" data-type="available">@Model.RzwBalance.AvailableBalance.ToString("N8")</span> RZW
                    </div>
                    <div class="balance-description">
                        <small class="text-muted">@Localizer["Ready for trading and transfers"]</small>
                    </div>
                </div>
                
                @if (Model.RzwBalance.HasLockedBalance)
                {
                    <div class="balance-item locked">
                        <div class="balance-label">
                            <i class="fas fa-lock text-warning"></i>
                            @Localizer["Locked Balance"]
                        </div>
                        <div class="balance-value">
                            <span data-coin="RZW" data-type="locked">@Model.RzwBalance.LockedBalance.ToString("N8")</span> RZW
                        </div>
                        <div class="balance-description">
                            <small class="text-muted">@Localizer["Locked in various operations"]</small>
                        </div>
                    </div>
                    
                    @if (Model.RzwBalance.LockedInSavings.HasValue && Model.RzwBalance.LockedInSavings > 0)
                    {
                        <div class="balance-item savings">
                            <div class="balance-label">
                                <i class="fas fa-piggy-bank text-info"></i>
                                @Localizer["In Savings Accounts"]
                            </div>
                            <div class="balance-value">
                                <span data-coin="RZW" data-type="savings">@Model.RzwBalance.LockedInSavings.Value.ToString("N8")</span> RZW
                            </div>
                            <div class="balance-description">
                                <small class="text-muted">
                                    @if (Model.RzwBalance.SavingsLocks?.Count > 0)
                                    {
                                        @string.Format(Localizer["In {0} savings accounts"], Model.RzwBalance.SavingsLocks.Count)
                                    }
                                    else
                                    {
                                        @Localizer["Earning interest"]
                                    }
                                </small>
                            </div>
                        </div>
                    }
                }
            </div>
            
            <div class="card-actions">
                <a href="/MyAccount/RzwSavings" class="btn btn-primary">
                    <i class="fas fa-piggy-bank"></i> @Localizer["Savings Accounts"]
                </a>
                <a href="/MyAccount/RzwTransfer" class="btn btn-outline-primary">
                    <i class="fas fa-paper-plane"></i> @Localizer["Transfer RZW"]
                </a>
                <button type="button" class="btn btn-outline-info" onclick="showRzwDetails()">
                    <i class="fas fa-chart-line"></i> @Localizer["Details"]
                </button>
            </div>
        </div>
    }

    <!-- Diğer Coinler -->
    @if (Model.WalletBalances.Any(w => !w.IsRzwToken))
    {
        <div class="other-coins-section">
            <div class="section-header">
                <h5 class="section-title">
                    <i class="fas fa-coins text-secondary"></i>
                    @Localizer["Other Cryptocurrencies"]
                </h5>
                <small class="text-muted">@Localizer["Your other cryptocurrency holdings"]</small>
            </div>
            
            <div class="coins-grid">
                @foreach (var balance in Model.WalletBalances.Where(w => !w.IsRzwToken))
                {
                    <div class="coin-card" data-coin="@balance.CoinCode">
                        <div class="coin-header">
                            <img src="@balance.IconUrl" alt="@balance.CoinCode" class="coin-icon" onerror="this.src='/images/markets/default.png'">
                            <div class="coin-info">
                                <h6 class="coin-name">@balance.CoinName</h6>
                                <span class="coin-code">@balance.CoinCode</span>
                            </div>
                        </div>
                        
                        <div class="coin-balance">
                            <div class="main-balance">
                                <span data-coin="@balance.CoinCode" data-type="available">@balance.AvailableBalance.ToString("N8")</span> @balance.CoinCode
                            </div>
                            <div class="balance-label">@Localizer["Available Balance"]</div>
                            
                            @if (balance.HasLockedBalance)
                            {
                                <div class="locked-info mt-2">
                                    <small class="text-warning">
                                        <i class="fas fa-lock"></i> 
                                        @Localizer["Locked"]: <span data-coin="@balance.CoinCode" data-type="locked">@balance.LockedBalance.ToString("N8")</span> @balance.CoinCode
                                    </small>
                                </div>
                                <div class="total-info">
                                    <small class="text-info">
                                        @Localizer["Total"]: <span data-coin="@balance.CoinCode" data-type="total">@balance.TotalBalance.ToString("N8")</span> @balance.CoinCode
                                    </small>
                                </div>
                            }
                        </div>

                        <div class="coin-actions">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="showCoinDetails('@balance.CoinCode')">
                                <i class="fas fa-eye"></i> @Localizer["Details"]
                            </button>
                        </div>
                    </div>
                }
            </div>
        </div>
    }

    <!-- Empty State -->
    @if (!Model.WalletBalances.Any())
    {
        <div class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-wallet fa-3x text-muted"></i>
            </div>
            <h5 class="empty-title">@Localizer["No Cryptocurrencies Found"]</h5>
            <p class="empty-description">@Localizer["Start by depositing funds or purchasing cryptocurrencies"]</p>
            <div class="empty-actions">
                <a href="/MyAccount/Deposit" class="btn btn-primary">
                    <i class="fas fa-plus"></i> @Localizer["Deposit Funds"]
                </a>
                <a href="/Market" class="btn btn-outline-primary">
                    <i class="fas fa-shopping-cart"></i> @Localizer["Buy Crypto"]
                </a>
            </div>
        </div>
    }
</div>

<!-- RZW Details Modal -->
<div class="modal fade" id="rzwDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-chart-line"></i> @Localizer["RZW Token Details"]
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="rzwDetailsContent">
                    <!-- Content will be loaded via AJAX -->
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/wallet-page.js"></script>
}

@section Styles {
    <link href="~/css/wallet-page.css" rel="stylesheet" />
}
```

#### 5.1.3 CSS Styling

**Dosya**: `src/wwwroot/css/wallet-page.css`
```css
/* Wallet Page Styles */
.wallet-container {
    padding: 20px 0;
}

.page-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
}

.page-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 5px;
}

.page-subtitle {
    color: #6c757d;
    margin-bottom: 0;
}

/* RZW Balance Card */
.rzw-balance-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.rzw-balance-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    pointer-events: none;
}

.rzw-balance-card .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    background: none;
    border: none;
    padding: 0;
}

.coin-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.coin-icon-large {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 3px solid rgba(255,255,255,0.3);
}

.coin-details .coin-name {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.coin-details .coin-code {
    font-size: 1rem;
    opacity: 0.9;
}

.coin-details .platform-token {
    display: block;
    font-size: 0.8rem;
    opacity: 0.7;
    margin-top: 2px;
}

.total-value {
    text-align: right;
}

.total-amount {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
}

.currency {
    font-size: 1rem;
    opacity: 0.9;
}

/* Balance Breakdown */
.balance-breakdown {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 25px 0;
}

.balance-item {
    background: rgba(255,255,255,0.15);
    padding: 20px;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    transition: transform 0.2s ease;
}

.balance-item:hover {
    transform: translateY(-2px);
}

.balance-item.available {
    border-left: 4px solid #28a745;
}

.balance-item.locked {
    border-left: 4px solid #ffc107;
}

.balance-item.savings {
    border-left: 4px solid #17a2b8;
}

.balance-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    margin-bottom: 8px;
}

.balance-value {
    font-size: 1.2rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
    margin-bottom: 5px;
}

.balance-description {
    opacity: 0.8;
}

/* Card Actions */
.card-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-top: 25px;
}

.card-actions .btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 10px 20px;
}

/* Other Coins Section */
.other-coins-section {
    margin-top: 40px;
}

.section-header {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.section-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 5px;
}

.coins-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.coin-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.coin-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.coin-card .coin-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
}

.coin-card .coin-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.coin-card .coin-name {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

.coin-card .coin-code {
    font-size: 0.9rem;
    color: #6c757d;
}

.coin-balance .main-balance {
    font-size: 1.3rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
    color: #2c3e50;
    margin-bottom: 5px;
}

.balance-label {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 10px;
}

.locked-info, .total-info {
    margin-top: 8px;
}

.coin-actions {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    background: #f8f9fa;
    border-radius: 12px;
    margin: 40px 0;
}

.empty-icon {
    margin-bottom: 20px;
}

.empty-title {
    color: #495057;
    margin-bottom: 10px;
}

.empty-description {
    color: #6c757d;
    margin-bottom: 25px;
}

.empty-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
    .wallet-container {
        padding: 15px 0;
    }
    
    .rzw-balance-card {
        padding: 20px;
    }
    
    .rzw-balance-card .card-header {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .balance-breakdown {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .coins-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .card-actions {
        justify-content: center;
    }
    
    .empty-actions {
        flex-direction: column;
        align-items: center;
    }
}

/* Loading States */
.balance-loading {
    opacity: 0.6;
    pointer-events: none;
}

.balance-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
```

## 📋 Kontrol Listesi

### ✅ YAPILACAKLAR
- [ ] WalletModel.cs güncelleme
- [ ] Wallet.cshtml güncelleme
- [ ] wallet-page.css oluşturma
- [ ] wallet-page.js oluşturma (sonraki adımda)
- [ ] Responsive design testleri
- [ ] Cross-browser testleri

### 🔄 YAPILMAKTA OLANLAR
- (Henüz başlanmadı)

### ✅ YAPILMIŞLAR
- (Henüz tamamlanmadı)

## 🧪 Test Kriterleri

### Functional Tests
- [ ] RZW bakiye detayları doğru gösteriliyor
- [ ] Diğer coinler için basit gösterim çalışıyor
- [ ] Real-time balance update çalışıyor
- [ ] Modal popup'lar çalışıyor

### UI/UX Tests
- [ ] Responsive design tüm cihazlarda çalışıyor
- [ ] RZW özel kartı görsel olarak çekici
- [ ] Loading states uygun
- [ ] Error handling doğru

## 📝 Notlar

### Önemli Özellikler
- RZW için özel gradient kart tasarımı
- Kullanılabilir/Kilitli/Toplam bakiye ayrımı
- Real-time balance updates
- Mobile-first responsive design

### Sonraki Adım
Bu adım tamamlandıktan sonra **Faz 5.2: RZW Savings Ana Sayfası** başlayacak.

---
**Tahmini Süre**: 1 gün
**Öncelik**: Yüksek
**Bağımlılıklar**: Faz 1, 2, 3 tamamlanmış olmalı
