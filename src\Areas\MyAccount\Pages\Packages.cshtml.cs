using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels;
using RazeWinComTr.Areas.Admin.ViewModels.Package;
using RazeWinComTr.Areas.Admin.ViewModels.BalanceTransaction;

namespace RazeWinComTr.Areas.MyAccount.Pages
{
    public class PackagesModel(
        PackageService packageService,
        UserPackageService userPackageService,
        UserService userService,
        IStringLocalizer<SharedResource> localizer,
        AppDbContext context,
        ITokenPriceService tokenPriceService,
        ILogger<PackagesModel>? logger = null) : PageModel
    {
        private readonly PackageService _packageService = packageService;
        private readonly UserPackageService _userPackageService = userPackageService;
        private readonly UserService _userService = userService;
        private readonly IStringLocalizer<SharedResource> _localizer = localizer;
        private readonly AppDbContext _context = context;
        private readonly ITokenPriceService _tokenPriceService = tokenPriceService;
        private readonly ILogger<PackagesModel>? _logger = logger;

        public List<PackageWithRewardsViewModel> PackagesWithRewards { get; set; } = [];
        public UserPackageViewModel? CurrentUserPackage { get; set; }
        public SweetAlert2Message? AlertMessage { get; set; }
        public string? ErrorMessage { get; set; }
        public decimal UserBalance { get; set; }
        public int? CurrentPackageOrder { get; set; }
        public decimal RzwBuyPrice { get; set; }

        public async Task<IActionResult> OnGetAsync(int? packageId = null)
        {
            var userId = User.GetClaimUserId();
            if (!userId.HasValue)
                return Unauthorized();

            // Get user's current balance
            var user = await _userService.GetByIdAsync(userId.Value);
            if (user == null)
                return Unauthorized();

            UserBalance = user.Balance;

            // Get all active packages with rewards in a single query
            PackagesWithRewards = await _packageService.GetPackagesWithRewardsAsync(activeOnly: true);

            // Get current RZW buy price
            var rzwTokenInfo = await _tokenPriceService.GetRzwTokenInfoAsync();
            RzwBuyPrice = rzwTokenInfo.BuyPrice;

            // Get user's current package
            var userPackages = await _userPackageService.GetUserPackagesAsync(userId.Value);
            CurrentUserPackage = userPackages.FirstOrDefault(p => p.Status == UserPackageStatus.Active); // Bu zaten memory'de, async gerekmiyor

            // Get the order of the current package (for package hierarchy comparison)
            if (CurrentUserPackage != null)
            {
                var currentPackageWithRewards = PackagesWithRewards.FirstOrDefault(p => p.Package.Id == CurrentUserPackage.PackageId); // Bu zaten memory'de, async gerekmiyor
                if (currentPackageWithRewards != null)
                {
                    CurrentPackageOrder = currentPackageWithRewards.Package.Order;
                }
            }

            // If packageId is provided, show the purchase confirmation modal
            if (packageId.HasValue)
            {
                var packageWithRewards = PackagesWithRewards.FirstOrDefault(p => p.Package.Id == packageId.Value); // Bu zaten memory'de, async gerekmiyor
                if (packageWithRewards != null)
                {
                    var package = packageWithRewards.Package;
                    ViewData["SelectedPackageId"] = package.Id;
                    ViewData["SelectedPackageName"] = package.Name;
                    ViewData["SelectedPackagePrice"] = package.Price;
                    ViewData["ShowPurchaseModal"] = true;
                }
            }

            return Page();
        }

        public async Task<IActionResult> OnPostPurchaseAsync(int packageId)
        {
            var userId = User.GetClaimUserId();
            if (!userId.HasValue)
                return Unauthorized();

            try
            {
                // Get the package
                var package = await _packageService.GetByIdAsync(packageId);
                if (package == null)
                {
                    ErrorMessage = _localizer["Package not found"].Value;
                    return RedirectToPage();
                }

                // Get the user
                var user = await _userService.GetByIdAsync(userId.Value);
                if (user == null)
                {
                    ErrorMessage = _localizer["User not found"].Value;
                    return RedirectToPage();
                }

                // Get user's current package
                var userPackages = await _userPackageService.GetUserPackagesAsync(userId.Value);
                var currentUserPackage = userPackages.FirstOrDefault(p => p.Status == UserPackageStatus.Active); // Bu zaten memory'de, async gerekmiyor

                // Check if user is trying to purchase a lower package than their current one
                if (currentUserPackage != null)
                {
                    var currentPackage = await _packageService.GetByIdAsync(currentUserPackage.PackageId);
                    if (currentPackage != null && currentPackage.Order > package.Order)
                    {
                        ErrorMessage = _localizer["You cannot purchase a lower package than your current one"].Value;
                        return RedirectToPage();
                    }
                }

                // Check if user has enough balance
                if (user.Balance < package.Price)
                {
                    ErrorMessage = _localizer["Insufficient balance to purchase this package"].Value;
                    return RedirectToPage();
                }

                // Use a transaction to ensure all operations are atomic
                using var transaction = await _context.Database.BeginTransactionAsync();
                try
                {
                    // Purchase the package - pass the existing transaction
                    // The balance transaction will be recorded inside the PurchasePackageAsync method
                    var userPackage = await _userPackageService.PurchasePackageAsync(userId.Value, packageId, null, _context);

                    // Commit the transaction
                    await transaction.CommitAsync();

                    // Add a small delay to ensure all database operations are complete
                    await Task.Delay(500);

                    // Log the successful purchase
                    _logger?.LogInformation("User {UserId} purchased package {PackageId} for {Price}. UserPackage ID: {UserPackageId}",
                        userId, packageId, package.Price, userPackage.Id);
                }
                catch (Exception ex)
                {
                    // Rollback the transaction if any operation fails
                    await transaction.RollbackAsync();
                    _logger?.LogError("Error during package purchase: {ErrorMessage}", ex.Message);
                    throw;
                }

                // Get the current RZW price to calculate how many tokens were received
                // Get RZW token information (ID and price) in a single query
                var rzwTokenInfo = await _tokenPriceService.GetRzwTokenInfoAsync();
                decimal rzwBuyPrice = rzwTokenInfo.BuyPrice;

                // Calculate the number of RZW tokens received
                decimal rzwAmount = package.Price / rzwBuyPrice;

                AlertMessage = new SweetAlert2Message
                {
                    Title = _localizer["Success"],
                    Text = _localizer["Package purchased successfully. You have received {0} RZW tokens (at {1} TL/RZW rate).",
                        rzwAmount.ToString("N2"), rzwBuyPrice.ToString("N2")],
                    Icon = "success"
                };

                // Redirect to the same page without the packageId parameter
                return RedirectToPage(new { packageId = (int?)null });
            }
            catch (Exception ex)
            {
                ErrorMessage = ex.Message;
                return RedirectToPage();
            }
        }
    }
}
