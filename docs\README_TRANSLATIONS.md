# Kripto Para Yatırma Çevirileri

Bu dosya, kripto para yatırma işlevselliği için gerekli çeviri anahtarlarını içerir. Bu çevirileri SharedResource.resx ve SharedResource.tr.resx dosyalarına eklemeniz gerekmektedir.

## Çeviri Dosyalarının Konumu

- İngilizce çeviriler: `src\Areas\Admin\Resources\SharedResource.resx`
- Türkçe çeviriler: `src\Areas\Admin\Resources\SharedResource.tr.resx`

## Çeviri Anahtarları

Aşağıdaki çeviri anahtarlarını ilgili dosyalara eklemeniz gerekmektedir:

### İng<PERSON>zce Çeviriler (SharedResource.resx)

```xml
<data name="Cryptocurrency Deposit Methods" xml:space="preserve">
  <value>Cryptocurrency Deposit Methods</value>
  <comment>Title for cryptocurrency deposit methods section</comment>
</data>
<data name="Fiat Currency Deposit Methods" xml:space="preserve">
  <value>Fiat Currency Deposit Methods</value>
  <comment>Title for fiat currency deposit methods section</comment>
</data>
<data name="Important" xml:space="preserve">
  <value>Important</value>
  <comment>Important notice label</comment>
</data>
<data name="Warning" xml:space="preserve">
  <value>Warning</value>
  <comment>Warning label</comment>
</data>
<data name="Please make sure to send your cryptocurrency to the correct network address. Sending to the wrong network may result in permanent loss of funds." xml:space="preserve">
  <value>Please make sure to send your cryptocurrency to the correct network address. Sending to the wrong network may result in permanent loss of funds.</value>
  <comment>Warning message for cryptocurrency deposits</comment>
</data>
<data name="Only send Bitcoin (BTC) to this address. Sending any other cryptocurrency may result in permanent loss." xml:space="preserve">
  <value>Only send Bitcoin (BTC) to this address. Sending any other cryptocurrency may result in permanent loss.</value>
  <comment>Warning message for Bitcoin deposits</comment>
</data>
<data name="Only send USDT on the TRC20 network to this address. Sending USDT on other networks or other cryptocurrencies may result in permanent loss." xml:space="preserve">
  <value>Only send USDT on the TRC20 network to this address. Sending USDT on other networks or other cryptocurrencies may result in permanent loss.</value>
  <comment>Warning message for USDT deposits</comment>
</data>
<data name="Only send BNB on the BEP20 (Smart Chain) network to this address. Sending BNB on other networks or other cryptocurrencies may result in permanent loss." xml:space="preserve">
  <value>Only send BNB on the BEP20 (Smart Chain) network to this address. Sending BNB on other networks or other cryptocurrencies may result in permanent loss.</value>
  <comment>Warning message for BNB deposits</comment>
</data>
<data name="Scan to copy address" xml:space="preserve">
  <value>Scan to copy address</value>
  <comment>QR code instruction</comment>
</data>
<data name="Transaction Hash" xml:space="preserve">
  <value>Transaction Hash</value>
  <comment>Transaction hash label</comment>
</data>
<data name="Sender Address" xml:space="preserve">
  <value>Sender Address</value>
  <comment>Sender address label</comment>
</data>
<data name="Additional Notes (Optional)" xml:space="preserve">
  <value>Additional Notes (Optional)</value>
  <comment>Additional notes label</comment>
</data>
<data name="Submit Deposit Report" xml:space="preserve">
  <value>Submit Deposit Report</value>
  <comment>Submit button text</comment>
</data>
<data name="Minimum deposit amount for {0} is {1}" xml:space="preserve">
  <value>Minimum deposit amount for {0} is {1}</value>
  <comment>Minimum deposit amount message</comment>
</data>
<data name="All Payments" xml:space="preserve">
  <value>All Payments</value>
  <comment>All payments filter option</comment>
</data>
<data name="Fiat Deposits" xml:space="preserve">
  <value>Fiat Deposits</value>
  <comment>Fiat deposits filter option</comment>
</data>
<data name="Cryptocurrency Deposits" xml:space="preserve">
  <value>Cryptocurrency Deposits</value>
  <comment>Cryptocurrency deposits filter option</comment>
</data>
<data name="Note: When you approve a cryptocurrency deposit, the amount will be added to the user's cryptocurrency wallet automatically." xml:space="preserve">
  <value>Note: When you approve a cryptocurrency deposit, the amount will be added to the user's cryptocurrency wallet automatically.</value>
  <comment>Note for admin when approving crypto deposits</comment>
</data>
<data name="Cryptocurrency Type" xml:space="preserve">
  <value>Cryptocurrency Type</value>
  <comment>Cryptocurrency type label</comment>
</data>
<data name="Transaction Hash/ID" xml:space="preserve">
  <value>Transaction Hash/ID</value>
  <comment>Transaction hash/ID label</comment>
</data>
<data name="This is the unique identifier for your transaction on the blockchain." xml:space="preserve">
  <value>This is the unique identifier for your transaction on the blockchain.</value>
  <comment>Transaction hash explanation</comment>
</data>
<data name="Enter the transaction hash/ID from your wallet" xml:space="preserve">
  <value>Enter the transaction hash/ID from your wallet</value>
  <comment>Transaction hash input placeholder</comment>
</data>
<data name="Enter the address you sent from" xml:space="preserve">
  <value>Enter the address you sent from</value>
  <comment>Sender address input placeholder</comment>
</data>
<data name="Any additional information about your deposit" xml:space="preserve">
  <value>Any additional information about your deposit</value>
  <comment>Additional notes input placeholder</comment>
</data>
<data name="Your cryptocurrency deposit request has been received. It will be processed as soon as possible." xml:space="preserve">
  <value>Your cryptocurrency deposit request has been received. It will be processed as soon as possible.</value>
  <comment>Success message for crypto deposit</comment>
</data>
<data name="Cryptocurrency Deposit" xml:space="preserve">
  <value>Cryptocurrency Deposit</value>
  <comment>Cryptocurrency deposit page title</comment>
</data>
<data name="Report Your Deposit" xml:space="preserve">
  <value>Report Your Deposit</value>
  <comment>Report deposit section title</comment>
</data>
<data name="After sending cryptocurrency to one of the addresses above, please fill out this form to report your deposit." xml:space="preserve">
  <value>After sending cryptocurrency to one of the addresses above, please fill out this form to report your deposit.</value>
  <comment>Instructions for reporting deposit</comment>
</data>
<data name="Copied!" xml:space="preserve">
  <value>Copied!</value>
  <comment>Copied notification title</comment>
</data>
<data name="Address copied to clipboard" xml:space="preserve">
  <value>Address copied to clipboard</value>
  <comment>Copied notification message</comment>
</data>
<data name="BTC Address" xml:space="preserve">
  <value>BTC Address</value>
  <comment>BTC address label</comment>
</data>
<data name="USDT Address" xml:space="preserve">
  <value>USDT Address</value>
  <comment>USDT address label</comment>
</data>
<data name="BNB Address" xml:space="preserve">
  <value>BNB Address</value>
  <comment>BNB address label</comment>
</data>
<data name="Bitcoin (BTC) Deposit Address" xml:space="preserve">
  <value>Bitcoin (BTC) Deposit Address</value>
  <comment>Bitcoin deposit address title</comment>
</data>
<data name="USDT (TRC20) Deposit Address" xml:space="preserve">
  <value>USDT (TRC20) Deposit Address</value>
  <comment>USDT deposit address title</comment>
</data>
<data name="BNB (BEP20) Deposit Address" xml:space="preserve">
  <value>BNB (BEP20) Deposit Address</value>
  <comment>BNB deposit address title</comment>
</data>
<data name="Copy" xml:space="preserve">
  <value>Copy</value>
  <comment>Copy button text</comment>
</data>
<data name="Amount ({0})" xml:space="preserve">
  <value>Amount ({0})</value>
  <comment>Amount label with currency</comment>
</data>
<data name="Invalid cryptocurrency type" xml:space="preserve">
  <value>Invalid cryptocurrency type</value>
  <comment>Error message for invalid crypto type</comment>
</data>
<data name="Selected cryptocurrency is not available" xml:space="preserve">
  <value>Selected cryptocurrency is not available</value>
  <comment>Error message for unavailable crypto</comment>
</data>
<data name="Missing cryptocurrency information in payment data" xml:space="preserve">
  <value>Missing cryptocurrency information in payment data</value>
  <comment>Error message for missing crypto info</comment>
</data>
<data name="Invalid cryptocurrency data format" xml:space="preserve">
  <value>Invalid cryptocurrency data format</value>
  <comment>Error message for invalid crypto data format</comment>
</data>
<data name="Could not parse cryptocurrency data" xml:space="preserve">
  <value>Could not parse cryptocurrency data</value>
  <comment>Error message for crypto data parsing failure</comment>
</data>
<data name="Insufficient cryptocurrency balance for reversal" xml:space="preserve">
  <value>Insufficient cryptocurrency balance for reversal</value>
  <comment>Error message for insufficient balance during reversal</comment>
</data>
<data name="Optional" xml:space="preserve">
  <value>Optional</value>
  <comment>Optional field indicator</comment>
</data>
<data name="Cryptocurrency Information" xml:space="preserve">
  <value>Cryptocurrency Information</value>
  <comment>Cryptocurrency information section title</comment>
</data>
```

### Türkçe Çeviriler (SharedResource.tr.resx)

```xml
<data name="Cryptocurrency Deposit Methods" xml:space="preserve">
  <value>Kripto Para Yatırma Yöntemleri</value>
  <comment>Title for cryptocurrency deposit methods section</comment>
</data>
<data name="Fiat Currency Deposit Methods" xml:space="preserve">
  <value>Fiat Para Yatırma Yöntemleri</value>
  <comment>Title for fiat currency deposit methods section</comment>
</data>
<data name="Important" xml:space="preserve">
  <value>Önemli</value>
  <comment>Important notice label</comment>
</data>
<data name="Warning" xml:space="preserve">
  <value>Uyarı</value>
  <comment>Warning label</comment>
</data>
<data name="Please make sure to send your cryptocurrency to the correct network address. Sending to the wrong network may result in permanent loss of funds." xml:space="preserve">
  <value>Lütfen kripto paranızı doğru ağ adresine gönderdiğinizden emin olun. Yanlış ağa gönderim, fonlarınızın kalıcı olarak kaybedilmesine neden olabilir.</value>
  <comment>Warning message for cryptocurrency deposits</comment>
</data>
<data name="Only send Bitcoin (BTC) to this address. Sending any other cryptocurrency may result in permanent loss." xml:space="preserve">
  <value>Bu adrese sadece Bitcoin (BTC) gönderin. Başka bir kripto para göndermek kalıcı kayıplara neden olabilir.</value>
  <comment>Warning message for Bitcoin deposits</comment>
</data>
<data name="Only send USDT on the TRC20 network to this address. Sending USDT on other networks or other cryptocurrencies may result in permanent loss." xml:space="preserve">
  <value>Bu adrese sadece TRC20 ağındaki USDT'yi gönderin. Diğer ağlardaki USDT veya başka kripto paraları göndermek kalıcı kayıplara neden olabilir.</value>
  <comment>Warning message for USDT deposits</comment>
</data>
<data name="Only send BNB on the BEP20 (Smart Chain) network to this address. Sending BNB on other networks or other cryptocurrencies may result in permanent loss." xml:space="preserve">
  <value>Bu adrese sadece BEP20 (Smart Chain) ağındaki BNB'yi gönderin. Diğer ağlardaki BNB veya başka kripto paraları göndermek kalıcı kayıplara neden olabilir.</value>
  <comment>Warning message for BNB deposits</comment>
</data>
<data name="Scan to copy address" xml:space="preserve">
  <value>Adresi kopyalamak için tarayın</value>
  <comment>QR code instruction</comment>
</data>
<data name="Transaction Hash" xml:space="preserve">
  <value>İşlem Hash'i</value>
  <comment>Transaction hash label</comment>
</data>
<data name="Sender Address" xml:space="preserve">
  <value>Gönderen Adresi</value>
  <comment>Sender address label</comment>
</data>
<data name="Additional Notes (Optional)" xml:space="preserve">
  <value>Ek Notlar (İsteğe Bağlı)</value>
  <comment>Additional notes label</comment>
</data>
<data name="Submit Deposit Report" xml:space="preserve">
  <value>Para Yatırma Bildirimini Gönder</value>
  <comment>Submit button text</comment>
</data>
<data name="Minimum deposit amount for {0} is {1}" xml:space="preserve">
  <value>{0} için minimum yatırma tutarı {1}</value>
  <comment>Minimum deposit amount message</comment>
</data>
<data name="All Payments" xml:space="preserve">
  <value>Tüm Ödemeler</value>
  <comment>All payments filter option</comment>
</data>
<data name="Fiat Deposits" xml:space="preserve">
  <value>Fiat Para Yatırmaları</value>
  <comment>Fiat deposits filter option</comment>
</data>
<data name="Cryptocurrency Deposits" xml:space="preserve">
  <value>Kripto Para Yatırmaları</value>
  <comment>Cryptocurrency deposits filter option</comment>
</data>
<data name="Note: When you approve a cryptocurrency deposit, the amount will be added to the user's cryptocurrency wallet automatically." xml:space="preserve">
  <value>Not: Bir kripto para yatırma işlemini onayladığınızda, tutar kullanıcının kripto para cüzdanına otomatik olarak eklenecektir.</value>
  <comment>Note for admin when approving crypto deposits</comment>
</data>
<data name="Cryptocurrency Type" xml:space="preserve">
  <value>Kripto Para Türü</value>
  <comment>Cryptocurrency type label</comment>
</data>
<data name="Transaction Hash/ID" xml:space="preserve">
  <value>İşlem Hash/ID</value>
  <comment>Transaction hash/ID label</comment>
</data>
<data name="This is the unique identifier for your transaction on the blockchain." xml:space="preserve">
  <value>Bu, işleminizin blok zincirindeki benzersiz tanımlayıcısıdır.</value>
  <comment>Transaction hash explanation</comment>
</data>
<data name="Enter the transaction hash/ID from your wallet" xml:space="preserve">
  <value>Cüzdanınızdan işlem hash/ID'sini girin</value>
  <comment>Transaction hash input placeholder</comment>
</data>
<data name="Enter the address you sent from" xml:space="preserve">
  <value>Gönderdiğiniz adresi girin</value>
  <comment>Sender address input placeholder</comment>
</data>
<data name="Any additional information about your deposit" xml:space="preserve">
  <value>Para yatırma işleminiz hakkında ek bilgiler</value>
  <comment>Additional notes input placeholder</comment>
</data>
<data name="Your cryptocurrency deposit request has been received. It will be processed as soon as possible." xml:space="preserve">
  <value>Kripto para yatırma talebiniz alınmıştır. En kısa sürede işleme alınacaktır.</value>
  <comment>Success message for crypto deposit</comment>
</data>
<data name="Cryptocurrency Deposit" xml:space="preserve">
  <value>Kripto Para Yatırma</value>
  <comment>Cryptocurrency deposit page title</comment>
</data>
<data name="Report Your Deposit" xml:space="preserve">
  <value>Para Yatırma İşleminizi Bildirin</value>
  <comment>Report deposit section title</comment>
</data>
<data name="After sending cryptocurrency to one of the addresses above, please fill out this form to report your deposit." xml:space="preserve">
  <value>Yukarıdaki adreslerden birine kripto para gönderdikten sonra, lütfen para yatırma işleminizi bildirmek için bu formu doldurun.</value>
  <comment>Instructions for reporting deposit</comment>
</data>
<data name="Copied!" xml:space="preserve">
  <value>Kopyalandı!</value>
  <comment>Copied notification title</comment>
</data>
<data name="Address copied to clipboard" xml:space="preserve">
  <value>Adres panoya kopyalandı</value>
  <comment>Copied notification message</comment>
</data>
<data name="BTC Address" xml:space="preserve">
  <value>BTC Adresi</value>
  <comment>BTC address label</comment>
</data>
<data name="USDT Address" xml:space="preserve">
  <value>USDT Adresi</value>
  <comment>USDT address label</comment>
</data>
<data name="BNB Address" xml:space="preserve">
  <value>BNB Adresi</value>
  <comment>BNB address label</comment>
</data>
<data name="Bitcoin (BTC) Deposit Address" xml:space="preserve">
  <value>Bitcoin (BTC) Yatırma Adresi</value>
  <comment>Bitcoin deposit address title</comment>
</data>
<data name="USDT (TRC20) Deposit Address" xml:space="preserve">
  <value>USDT (TRC20) Yatırma Adresi</value>
  <comment>USDT deposit address title</comment>
</data>
<data name="BNB (BEP20) Deposit Address" xml:space="preserve">
  <value>BNB (BEP20) Yatırma Adresi</value>
  <comment>BNB deposit address title</comment>
</data>
<data name="Copy" xml:space="preserve">
  <value>Kopyala</value>
  <comment>Copy button text</comment>
</data>
<data name="Amount ({0})" xml:space="preserve">
  <value>Tutar ({0})</value>
  <comment>Amount label with currency</comment>
</data>
<data name="Invalid cryptocurrency type" xml:space="preserve">
  <value>Geçersiz kripto para türü</value>
  <comment>Error message for invalid crypto type</comment>
</data>
<data name="Selected cryptocurrency is not available" xml:space="preserve">
  <value>Seçilen kripto para mevcut değil</value>
  <comment>Error message for unavailable crypto</comment>
</data>
<data name="Missing cryptocurrency information in payment data" xml:space="preserve">
  <value>Ödeme verilerinde kripto para bilgisi eksik</value>
  <comment>Error message for missing crypto info</comment>
</data>
<data name="Invalid cryptocurrency data format" xml:space="preserve">
  <value>Geçersiz kripto para veri formatı</value>
  <comment>Error message for invalid crypto data format</comment>
</data>
<data name="Could not parse cryptocurrency data" xml:space="preserve">
  <value>Kripto para verileri ayrıştırılamadı</value>
  <comment>Error message for crypto data parsing failure</comment>
</data>
<data name="Insufficient cryptocurrency balance for reversal" xml:space="preserve">
  <value>İptal işlemi için yetersiz kripto para bakiyesi</value>
  <comment>Error message for insufficient balance during reversal</comment>
</data>
<data name="Optional" xml:space="preserve">
  <value>İsteğe Bağlı</value>
  <comment>Optional field indicator</comment>
</data>
<data name="Cryptocurrency Information" xml:space="preserve">
  <value>Kripto Para Bilgileri</value>
  <comment>Cryptocurrency information section title</comment>
</data>
```

## Çevirileri Ekleme Adımları

1. Visual Studio'da SharedResource.resx dosyasını açın
2. Yukarıdaki İngilizce çevirileri kopyalayıp SharedResource.resx dosyasına ekleyin
3. Visual Studio'da SharedResource.tr.resx dosyasını açın
4. Yukarıdaki Türkçe çevirileri kopyalayıp SharedResource.tr.resx dosyasına ekleyin
5. Dosyaları kaydedin ve uygulamayı yeniden başlatın

## Not

Çeviriler eklendikten sonra, uygulamanızı test etmeniz ve gerekirse ince ayarlar yapmanız önerilir. Eksik çeviriler olduğunda, sistem varsayılan olarak çeviri anahtarını gösterecektir, bu da hangi çevirilerin eksik olduğunu belirlemenize yardımcı olabilir.
