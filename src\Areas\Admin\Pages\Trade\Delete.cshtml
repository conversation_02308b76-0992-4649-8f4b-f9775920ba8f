@page
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.DbModel
@model DeleteModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = $"{L["Delete"]} {L["Trade"]}";
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

@if (Model.AlertMessage != null)
{
    <script id="scriptMessage" type="text/javascript">
        window.onload = function () {
            Swal.fire({
                title: '@Html.Raw(Model.AlertMessage.Title)',
                text: '@Html.Raw(Model.AlertMessage.Text)',
                icon: '@Html.Raw(Model.AlertMessage.Icon)'
            }).then((result) => {
                var redirectUrl = '@(Model.AlertMessage.RedirectUrl ?? string.Empty)';
                if (result.isConfirmed && redirectUrl !== '') {
                    location.href = redirectUrl;
                }
            });
            $('#scriptMessage').remove();
        }
    </script>
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@($"{L["Delete"]} {L["Trade"]}")</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="/admin/trade">@L["Trades"]</a></li>
                    <li class="breadcrumb-item active">@L["Delete"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <div class="card">
            <div class="card-body">
                @if (Model.Entity != null)
                {
                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-warning">
                                <h5><i class="icon fas fa-exclamation-triangle"></i> @ViewData["WarningTitle"]</h5>
                                <ul class="mb-0">
                                    <li>@ViewData["WarningRecord"]</li>
                                    <li>@ViewData["WarningBalances"]</li>
                                    <li>@ViewData["WarningUnrecoverable"]</li>
                                </ul>
                            </div>

                            <table class="table table-bordered">
                                <tbody>
                                <tr>
                                    <th style="width: 200px">@L["User"]</th>
                                    <td>@Model.Entity.User?.Email</td>
                                </tr>
                                <tr>
                                    <th>@L["Coin"]</th>
                                    <td>@Model.Entity.Coin?.Name (@Model.Entity.Coin?.PairCode)</td>
                                </tr>
                                <tr>
                                    <th>@L["Type"]</th>
                                    <td>
                                        @if (Model.Entity.Type == TradeType.Buy)
                                        {
                                            <span class="badge badge-success">@L["Buy"]</span>
                                        }
                                        else if (Model.Entity.Type == TradeType.Sell)
                                        {
                                            <span class="badge badge-danger">@L["Sell"]</span>
                                        }
                                        else if (Model.Entity.Type == TradeType.PackageBonus)
                                        {
                                            <span class="badge badge-info">@L["Package Bonus"]</span>
                                        }
                                        else if (Model.Entity.Type == TradeType.ReferralReward)
                                        {
                                            <span class="badge badge-warning">@L["Referral Reward"]</span>
                                        }
                                        else if (Model.Entity.Type == TradeType.RzwSavingsDeposit)
                                        {
                                            <span class="badge badge-primary">@L["Savings Deposit"]</span>
                                        }
                                        else if (Model.Entity.Type == TradeType.RzwSavingsWithdrawal)
                                        {
                                            <span class="badge badge-secondary">@L["Savings Withdrawal"]</span>
                                        }
                                        else if (Model.Entity.Type == TradeType.RzwSavingsInterest)
                                        {
                                            <span class="badge badge-success">@L["Savings Interest"]</span>
                                        }
                                        else if (Model.Entity.Type == TradeType.RzwSavingsEarlyWithdrawal)
                                        {
                                            <span class="badge badge-warning">@L["Savings Early Withdrawal"]</span>
                                        }
                                        else if (Model.Entity.Type == TradeType.RzwSavingsMaturity)
                                        {
                                            <span class="badge badge-info">@L["Savings Maturity"]</span>
                                        }
                                        else
                                        {
                                            <span class="badge badge-secondary">@Model.Entity.Type.ToString()</span>
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <th>@L["Amount"]</th>
                                    <td>@Model.Entity.CoinAmount.ToString("N8")</td>
                                </tr>
                                <tr>
                                    <th>@L["Rate"]</th>
                                    <td>@Model.Entity.CoinRate.ToString("N2") @L["Currency_Symbol"]</td>
                                </tr>
                                <tr>
                                    <th>@L["Total"]</th>
                                    <td>@Model.Entity.TryAmount.ToString("N2") @L["Currency_Symbol"]</td>
                                </tr>
                                @if (Model.Entity.ReferralReward != null)
                                {
                                    <tr>
                                        <th>@L["Referral Reward Details"]</th>
                                        <td>
                                            <div class="text-muted small">
                                                <div>@L["Level"]: @Model.Entity.ReferralReward.Level</div>
                                                <div>@L["Referred User"]: @(Model.Entity.ReferralReward.ReferredUser != null ? $"{Model.Entity.ReferralReward.ReferredUser.Name} {Model.Entity.ReferralReward.ReferredUser.Surname}" : "") (@Model.Entity.ReferralReward.ReferredUser?.Email)</div>
                                                <div>@L["Package"]: @Model.Entity.ReferralReward.Package?.Name</div>
                                                <div>@L["Deposit Amount"]: @Model.Entity.ReferralReward.DepositAmount.ToString("N2") @L["Currency_Symbol"]</div>
                                                <div>@L["Reward"]: @Model.Entity.ReferralReward.TlAmount.ToString("N2") @L["Currency_Symbol"] + @Model.Entity.ReferralReward.RzwAmount.ToString("N8") RZW</div>
                                                <div>@L["Rate"]: %@Model.Entity.ReferralReward.TlPercentage.ToString("N2") @L["Currency_Symbol"] + %@Model.Entity.ReferralReward.RzwPercentage.ToString("N2") RZW</div>
                                                <div>@L["Date"]: @Model.Entity.ReferralReward.CreatedDate.ToLocalTime().ToString("g")</div>
                                            </div>
                                        </td>
                                    </tr>
                                }
                                <tr>
                                    <th>@L["Date"]</th>
                                    <td>@Model.Entity.CreatedDate.ToLocalTime().ToString("g")</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                }

                @if (!string.IsNullOrEmpty(Model.ErrorMessage))
                {
                    <div class="alert alert-danger mt-3">
                        <h5><i class="icon fas fa-ban"></i> @L["Error"]</h5>
                        @Model.ErrorMessage
                    </div>
                }

                <form method="post">
                    <input type="hidden" asp-for="Id"/>
                    <div class="mt-3">
                        <button type="submit" class="btn btn-danger">@L["Delete"]</button>
                        <a href="/admin/trade" class="btn btn-default">@L["Cancel"]</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

@section Styles {
    <style>
        .text-muted.small {
            white-space: normal;
            line-height: 1.4;
        }
    </style>
}
