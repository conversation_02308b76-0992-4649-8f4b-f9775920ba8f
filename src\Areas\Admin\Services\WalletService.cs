using Microsoft.EntityFrameworkCore;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.Wallet;
using RazeWinComTr.Models;

namespace RazeWinComTr.Areas.Admin.Services;

public class WalletService : IWalletService
{
    private readonly AppDbContext _context;

    public WalletService(AppDbContext context)
    {
        _context = context;
    }

    public async Task<Wallet?> GetByIdAsync(int id)
    {
        return await _context.Wallets
            .Include(w => w.User)
            .Include(w => w.Coin)
            .FirstOrDefaultAsync(w => w.Id == id);
    }

    public async Task<List<Wallet>> GetByUserIdAsync(int userId)
    {
        return await _context.Wallets
            .Include(w => w.Coin)
            .Where(w => w.UserId == userId)
            .ToListAsync();
    }

    public async Task<List<Wallet>> GetTopNByUserIdAsync(int userId, int topN)
    {
        return await _context.Wallets
            .Include(w => w.Coin)
            .Where(w => w.UserId == userId)
            .OrderByDescending(w => (double)w.Balance) // Cast decimal to double for SQLite
            .Take(topN)
            .ToListAsync();
    }

    public async Task<Wallet?> GetByUserIdAndCoinIdAsync(int userId, int coinId, AppDbContext? existingContext = null)
    {
        var contextToUse = existingContext ?? _context;
        return await contextToUse.Wallets
            .Include(w => w.Coin)
            .FirstOrDefaultAsync(w => w.UserId == userId && w.CoinId == coinId);
    }

    public async Task<List<WalletViewModel>> GetListAsync()
    {
        return await _context.Wallets
            .Include(w => w.User)
            .Include(w => w.Coin)
            .Select(w => new WalletViewModel
            {
                Id = w.Id,
                UserId = w.UserId,
                UserEmail = w.User.Email,
                CoinId = w.CoinId,
                CoinName = w.Coin.Name,
                CoinCode = w.Coin != null ? w.Coin.PairCode : string.Empty,
                Balance = w.Balance,
                CreatedDate = w.CreatedDate,
                ModifiedDate = w.ModifiedDate
            })
            .OrderByDescending(w => w.CreatedDate)
            .ToListAsync();
    }



    public async Task<Wallet> CreateAsync(Wallet wallet, AppDbContext? existingContext = null)
    {
        if (existingContext == null)
        {
            _context.Wallets.Add(wallet);
            await _context.SaveChangesAsync();
            return wallet;
        }
        else
        {
            existingContext.Wallets.Add(wallet);
            await existingContext.SaveChangesAsync();
            return wallet;
        }
    }

    public async Task UpdateAsync(Wallet wallet, AppDbContext? existingContext = null)
    {
        var contextToUse = existingContext ?? _context;
        wallet.ModifiedDate = DateTime.UtcNow;
        contextToUse.Entry(wallet).State = EntityState.Modified;
        await contextToUse.SaveChangesAsync();
    }

    public async Task DeleteAsync(int id)
    {
        var wallet = await _context.Wallets.FindAsync(id);
        if (wallet != null)
        {
            _context.Wallets.Remove(wallet);
            await _context.SaveChangesAsync();
        }
    }

    //public async Task<Wallet> AddBalanceAsync(int userId, int coinId, decimal amount)
    //{
    //    var wallet = await GetByUserIdAndCoinIdAsync(userId, coinId);

    //    if (wallet == null)
    //    {
    //        // Create new wallet if it doesn't exist
    //        wallet = new Wallet
    //        {
    //            UserId = userId,
    //            CoinId = coinId,
    //            Balance = amount,
    //            CreatedDate = DateTime.UtcNow
    //        };
    //        return await CreateAsync(wallet);
    //    }
    //    else
    //    {
    //        // Update existing wallet
    //        wallet.Balance += amount;
    //        wallet.ModifiedDate = DateTime.UtcNow;
    //        await UpdateAsync(wallet);
    //        return wallet;
    //    }
    //}
    public async Task<Wallet> AddAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, TradeType tradeType, AppDbContext? existingContext = null)
    {
        var contextToUse = existingContext ?? _context;

        var wallet = await GetByUserIdAndCoinIdAsync(userId, rzwTokenInfo.TokenId, contextToUse);

        if (wallet == null)
        {
            // Create new wallet if it doesn't exist
            wallet = new Wallet
            {
                UserId = userId,
                CoinId = rzwTokenInfo.TokenId,
                Balance = amount,
                CreatedDate = DateTime.UtcNow
            };
            return await CreateAsync(wallet, contextToUse);
        }
        else
        {
            // Update existing wallet
            wallet.Balance += amount;
            wallet.ModifiedDate = DateTime.UtcNow;
            await UpdateAsync(wallet, contextToUse);
            return wallet;
        }


    }
    public async Task<bool> DeductAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, AppDbContext? existingContext = null)
    {
        var wallet = await GetByUserIdAndCoinIdAsync(userId, rzwTokenInfo.TokenId, existingContext);

        if (wallet == null || wallet.Balance < amount)
        {
            return false; // Insufficient balance
        }

        wallet.Balance -= amount;
        wallet.ModifiedDate = DateTime.UtcNow;
        await UpdateAsync(wallet, existingContext);
        return true;
    }

    // NEW METHODS - Available Balance
    public async Task<decimal> GetUserAvailableBalanceAsync(int userId, int coinId)
    {
        var wallet = await GetByUserIdAndCoinIdAsync(userId, coinId);
        return wallet?.Balance ?? 0;
    }

    // NEW METHODS - Locked Balance
    public async Task<decimal> GetUserLockedBalanceAsync(int userId, int coinId)
    {
        var wallet = await GetByUserIdAndCoinIdAsync(userId, coinId);
        return wallet?.LockedBalance ?? 0;
    }

    public async Task<bool> LockBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount)
    {
        var result = await LockBalanceAsync(userId, rzwTokenInfo, amount, _context);
        return result;
    }

    public async Task<bool> LockBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, AppDbContext? existingContext = null)
    {
        var contextToUse = existingContext ?? _context;
        var wallet = await GetByUserIdAndCoinIdAsync(userId, rzwTokenInfo.TokenId, contextToUse);

        if (wallet == null || wallet.Balance < amount)
        {
            return false; // Insufficient available balance
        }

        var previousAvailableBalance = wallet.Balance;
        var previousLockedBalance = wallet.LockedBalance;

        // Move from available to locked
        wallet.Balance -= amount;
        wallet.LockedBalance += amount;
        wallet.ModifiedDate = DateTime.UtcNow;
        await UpdateAsync(wallet, contextToUse);
        return true;
    }

    public async Task<bool> UnlockBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, AppDbContext? existingContext = null)
    {
        var contextToUse = existingContext ?? _context;
        var wallet = await GetByUserIdAndCoinIdAsync(userId, rzwTokenInfo.TokenId, contextToUse);

        if (wallet == null || wallet.LockedBalance < amount)
        {
            return false; // Insufficient locked balance
        }

        var previousAvailableBalance = wallet.Balance;
        var previousLockedBalance = wallet.LockedBalance;

        // Move from locked to available
        wallet.LockedBalance -= amount;
        wallet.Balance += amount;
        wallet.ModifiedDate = DateTime.UtcNow;
        await UpdateAsync(wallet, contextToUse);
        return true;
    }

    public async Task<Wallet> AddLockedBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, AppDbContext? existingContext = null)
    {
        var contextToUse = existingContext ?? _context;

        var wallet = await GetByUserIdAndCoinIdAsync(userId, rzwTokenInfo.TokenId, contextToUse);
        var previousLockedBalance = wallet?.LockedBalance ?? 0;

        if (wallet == null)
        {
            // Create new wallet if it doesn't exist
            wallet = new Wallet
            {
                UserId = userId,
                CoinId = rzwTokenInfo.TokenId,
                Balance = 0,
                LockedBalance = amount,
                CreatedDate = DateTime.UtcNow
            };
            wallet = await CreateAsync(wallet, contextToUse);
        }
        else
        {
            // Update existing wallet
            wallet.LockedBalance += amount;
            wallet.ModifiedDate = DateTime.UtcNow;
            await UpdateAsync(wallet, contextToUse);
        }
        return wallet;
    }

    public async Task<bool> DeductLockedBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, AppDbContext? existingContext = null)
    {
        var contextToUse = existingContext ?? _context;

        var wallet = await GetByUserIdAndCoinIdAsync(userId, rzwTokenInfo.TokenId, contextToUse);

        if (wallet == null || wallet.LockedBalance < amount)
        {
            return false; // Insufficient locked balance
        }

        var previousLockedBalance = wallet.LockedBalance;
        wallet.LockedBalance -= amount;
        wallet.ModifiedDate = DateTime.UtcNow;
        await UpdateAsync(wallet, contextToUse);
        return true;
    }

    // NEW METHODS - Total Balance
    public async Task<decimal> GetUserTotalBalanceAsync(int userId, int coinId)
    {
        var wallet = await GetByUserIdAndCoinIdAsync(userId, coinId);
        return wallet?.TotalBalance ?? 0;
    }

    // NEW METHODS - Balance Info
    public async Task<WalletBalanceInfo> GetWalletBalanceInfoAsync(int userId, int coinId)
    {
        var wallet = await _context.Wallets
            .Include(w => w.Coin)
            .Include(w => w.User)
                .ThenInclude(u => u.RzwSavingsAccounts.Where(s => s.Status == RzwSavingsStatus.Active))
            .FirstOrDefaultAsync(w => w.UserId == userId && w.CoinId == coinId);

        if (wallet == null)
        {
            // Return empty balance info if wallet doesn't exist
            var coin = await _context.Markets.FindAsync(coinId);
            return new WalletBalanceInfo
            {
                UserId = userId,
                CoinId = coinId,
                CoinCode = coin?.PairCode ?? string.Empty,
                CoinName = coin?.Name ?? string.Empty,
                IconUrl = coin?.IconUrl ?? string.Empty,
                AvailableBalance = 0,
                LockedBalance = 0,
                ActiveSavingsAccountCount = 0
            };
        }

        var balanceInfo = new WalletBalanceInfo
        {
            UserId = userId,
            CoinId = coinId,
            CoinCode = wallet.Coin?.PairCode ?? string.Empty,
            CoinName = wallet.Coin?.Name ?? string.Empty,
            IconUrl = wallet.Coin?.IconUrl ?? string.Empty,
            AvailableBalance = wallet.Balance,
            LockedBalance = wallet.LockedBalance
        };

        // Add RZW-specific information if this is the RZW token
        if (wallet.Coin?.PairCode == "RZW" || wallet.Coin?.Coin == "RZW")
        {
            balanceInfo.ActiveSavingsAccountCount = wallet.User?.RzwSavingsAccounts?.Count ?? 0;
        }

        return balanceInfo;
    }

    public async Task<List<WalletBalanceInfo>> GetUserAllBalanceInfoAsync(int userId)
    {
        var wallets = await _context.Wallets
            .Include(w => w.Coin)
            .Include(w => w.User)
                .ThenInclude(u => u.RzwSavingsAccounts.Where(s => s.Status == RzwSavingsStatus.Active))
            .Where(w => w.UserId == userId)
            .ToListAsync();

        var balanceInfoList = new List<WalletBalanceInfo>();

        foreach (var wallet in wallets)
        {
            var balanceInfo = new WalletBalanceInfo
            {
                UserId = userId,
                CoinId = wallet.CoinId,
                CoinCode = wallet.Coin?.PairCode ?? string.Empty,
                CoinName = wallet.Coin?.Name ?? string.Empty,
                IconUrl = wallet.Coin?.IconUrl ?? string.Empty,
                AvailableBalance = wallet.Balance,
                LockedBalance = wallet.LockedBalance
            };

            // Add RZW-specific information if this is the RZW token
            if (wallet.Coin?.PairCode == "RZW" || wallet.Coin?.Coin == "RZW")
            {
                balanceInfo.ActiveSavingsAccountCount = wallet.User?.RzwSavingsAccounts?.Count ?? 0;
            }

            balanceInfoList.Add(balanceInfo);
        }

        return balanceInfoList;
    }

    //private async Task CreateTradeRecordAsync(int userId, RzwTokenInfo rzwTokenInfo, TradeType tradeType, decimal coinAmount, decimal tryAmount,
    //decimal previousBalance, decimal newBalance, decimal previousLockedCoinBalance, decimal newLockedCoinBalance, AppDbContext? existingContext = null)
    //{
    //    var contextToUse = existingContext ?? _context;

    //    var trade = new Trade
    //    {
    //        UserId = userId,
    //        CoinId = rzwTokenInfo.TokenId,
    //        Type = tradeType,
    //        CoinAmount = coinAmount,
    //        TryAmount = tryAmount,
    //        CoinRate = rzwTokenInfo.BuyPrice,
    //        PreviousBalance = previousBalance,
    //        NewBalance = newBalance,
    //        PreviousCoinBalance = 0, // Not applicable for wallet operations
    //        NewCoinBalance = 0, // Not applicable for wallet operations
    //        PreviousWalletBalance = previousBalance + previousLockedCoinBalance,
    //        NewWalletBalance = newBalance + newLockedCoinBalance,
    //        CreatedDate = DateTime.UtcNow,
    //        IsActive = true,
    //    };
    //    await _tradeService.CreateAsync(trade, contextToUse);
    //}
}
