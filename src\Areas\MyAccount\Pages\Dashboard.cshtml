@page
@model RazeWinComTr.Areas.MyAccount.Pages.DashboardModel
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.Helpers
@inject IStringLocalizer<SharedResource> Localizer
@{
    ViewData["Title"] = Localizer["Dashboard"];
}

<!-- Main Content -->
<div class="fw homeSpreadAll">
    <div class="container">

        <!-- Recent Trades Section -->
        <div class="fw homeSpread">
            <div class="fw simpleTitle wow fadeInUp" style="visibility: visible; animation-name: fadeInUp;">
                <ul class="sul">
                    <li class="subTitle"><span class="subTitleX">RAZE<strong>WIN</strong></span></li>
                    <li class="title">@Localizer["Recent Trades"] <small>@string.Format(Localizer["Last {0} transactions"], 5) <a href="/MyAccount/TradeHistory">@Localizer["View All"]</a></small> </li>
                </ul>
            </div>
            <div class="fw homeSpreadBox wow fadeInUp" style="visibility: visible; animation-name: fadeInUp;">
                <div class="fw homeStatsTable">
                    <div class="tab-content" style="overflow-y: scroll;">
                        <table class="table" style="background-color: white;">
                            <thead>
                                <tr>
                                    <th style="width:10%;">@Localizer["Market"]</th>
                                    <th style="width:10%;">@Localizer["Type"]</th>
                                    <th style="width:10%;" class="text-right">@Localizer["Rate"]</th>
                                    <th style="width:15%;" class="text-right">@Localizer["Amount"]</th>
                                    <th style="width:15%;" class="text-right">@Localizer["Previous Balance"]</th>
                                    <th style="width:15%;" class="text-right">@Localizer["New Balance"]</th>
                                    <th style="width:25%;">@Localizer["Date and Time"]</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (Model.RecentTrades.Any())
                                {
                                    @foreach (var trade in Model.RecentTrades)
                                    {
                                        <tr>
                                            <td>@trade.CoinCode</td>
                                            <td>
                                                @if (trade.Type == RazeWinComTr.Areas.Admin.DbModel.TradeType.Buy)
                                                {
                                                    <span class="badge badge-success">@Localizer["Purchase"]</span>
                                                }
                                                else if (trade.Type == RazeWinComTr.Areas.Admin.DbModel.TradeType.Sell)
                                                {
                                                    <span class="badge badge-danger">@Localizer["Sale"]</span>
                                                }
                                                else if (trade.Type == RazeWinComTr.Areas.Admin.DbModel.TradeType.PackageBonus)
                                                {
                                                    <span class="badge badge-info">@Localizer["Package Bonus"]</span>
                                                }
                                                else if (trade.Type == RazeWinComTr.Areas.Admin.DbModel.TradeType.ReferralReward)
                                                {
                                                    <span class="badge badge-warning">@Localizer["Referral Reward"]</span>
                                                }
                                                else if (trade.Type == RazeWinComTr.Areas.Admin.DbModel.TradeType.RzwSavingsDeposit)
                                                {
                                                    <span class="badge badge-primary">@Localizer["Savings Deposit"]</span>
                                                }
                                                else if (trade.Type == RazeWinComTr.Areas.Admin.DbModel.TradeType.RzwSavingsWithdrawal)
                                                {
                                                    <span class="badge badge-secondary">@Localizer["Savings Withdrawal"]</span>
                                                }
                                                else if (trade.Type == RazeWinComTr.Areas.Admin.DbModel.TradeType.RzwSavingsInterest)
                                                {
                                                    <span class="badge badge-success">@Localizer["Savings Interest"]</span>
                                                }
                                                else if (trade.Type == RazeWinComTr.Areas.Admin.DbModel.TradeType.RzwSavingsEarlyWithdrawal)
                                                {
                                                    <span class="badge badge-warning">@Localizer["Savings Early Withdrawal"]</span>
                                                }
                                                else if (trade.Type == RazeWinComTr.Areas.Admin.DbModel.TradeType.RzwSavingsMaturity)
                                                {
                                                    <span class="badge badge-info">@Localizer["Savings Maturity"]</span>
                                                }
                                                else
                                                {
                                                    <span class="badge badge-secondary">@trade.Type.ToString()</span>
                                                }
                                            </td>
                                            <td class="text-right">@trade.CoinRate.ToString("N2") ₺</td>
                                            <td class="text-right">@trade.CoinAmount.ToString("N8")</td>
                                            <td class="text-right">@trade.PreviousBalance.ToString("N2") ₺</td>
                                            <td class="text-right">@trade.NewBalance.ToString("N2") ₺</td>
                                            <td>@trade.CreatedDate.ToLocalTime().ToString("dd.MM.yyyy HH:mm")</td>
                                        </tr>
                                    }
                                }
                                else
                                {
                                    <tr>
                                        <td colspan="8" class="text-center">@Localizer["No trades found"]</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Wallet Balances Section -->
        <div class="fw homeSpread pt-5">
            <div class="fw simpleTitle wow fadeInUp" style="visibility: visible; animation-name: fadeInUp;">
                <ul class="sul">
                    <li class="subTitle"><span class="subTitleX">RAZE<strong>WIN</strong></span></li>
                    <li class="title">@Localizer["My Wallet"] <small>@string.Format(Localizer["Top {0} balances"], 5) <a href="/MyAccount/Wallet">@Localizer["View All"]</a></small></li>
                </ul>
            </div>
            <div class="fw homeSpreadBox wow fadeInUp" style="visibility: visible; animation-name: fadeInUp;">
                <div class="fw homeStatsTable">
                    <div class="tab-content" style="overflow-y: scroll;">
                        <table class="table" style="background-color: white;">
                            <thead>
                                <tr>
                                    <th style="width:16%;">@Localizer["Currency"]</th>
                                    <th style="width:16%;" class="text-right">@Localizer["Balance"]</th>
                                    <th style="width:16%;" class="text-right">@Localizer["Locked Balance"]</th>
                                    <th style="width:16%;" class="text-right">@Localizer["Pending Orders"]</th>
                                    <th style="width:16%;" class="text-right">@Localizer["Available Balance"]</th>
                                    <th style="width:20%;" class="text-right">@Localizer["TRY Value (Approx.)"]</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (Model.WalletBalances.Any())
                                {
                                    @foreach (var wallet in Model.WalletBalances)
                                    {
                                        <tr>
                                            <td>@wallet.CoinName (@wallet.CoinCode)</td>
                                            <td class="text-right">@NumberFormatHelper.FormatDecimal(wallet.Balance)</td>
                                            <td class="text-right @(wallet.LockedBalance > 0 ? "text-danger" : "")">@NumberFormatHelper.FormatDecimal(wallet.LockedBalance)</td>
                                            <td class="text-right">0</td>
                                            <td class="text-right">@NumberFormatHelper.FormatDecimal(wallet.Balance)</td>
                                            <td class="text-right">@((wallet.Balance * wallet.CoinPrice).ToString("N2")) ₺</td>
                                        </tr>
                                    }
                                }
                                else
                                {
                                    <tr>
                                        <td colspan="6" class="text-center">@Localizer["No wallet balances found"]</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

