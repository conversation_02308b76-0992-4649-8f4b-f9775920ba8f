# Adım 5.2.4: Aktif Vadeli Hesaplar Listesi (2-3 saat)

## 📋 Adım Özeti
RZW Savings ana sayfasında aktif vadeli hesapların listelenmesi, durum göstergeleri ve hızlı işlem butonlarının implementasyonu.

## 🎯 Hedefler
- ✅ Aktif vadeli hesaplar tablosu oluşturma
- ✅ Durum göstergeleri (progress bar, badge'ler)
- ✅ Hızlı işlem butonları
- ✅ Empty state handling
- ✅ Responsive table design

## 📊 Yapılacak İşler

### ✅ YAPILACAKLAR

#### 5.2.4.1 Aktif Hesaplar Tablosu HTML

**Dosya**: `src/Areas/MyAccount/Pages/RzwSavings/Index.cshtml` (Active Accounts Section güncelleme)
```html
<!-- Active Accounts Section -->
<div class="active-accounts-section">
    @if (Model.ViewModel.HasActiveAccounts)
    {
        <div class="section-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="section-title">
                        <i class="fas fa-list-alt text-primary"></i>
                        @Localizer["Active Savings Accounts"]
                    </h5>
                    <small class="text-muted">@Localizer["Manage your current savings investments"]</small>
                </div>
                <div class="section-actions">
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="refreshAccounts()">
                        <i class="fas fa-sync-alt"></i> @Localizer["Refresh"]
                    </button>
                </div>
            </div>
        </div>

        <div class="accounts-table-container">
            <!-- Desktop Table -->
            <div class="table-responsive d-none d-lg-block">
                <table class="table accounts-table">
                    <thead>
                        <tr>
                            <th>@Localizer["Plan"]</th>
                            <th>@Localizer["Amount"]</th>
                            <th>@Localizer["Interest Rate"]</th>
                            <th>@Localizer["Progress"]</th>
                            <th>@Localizer["Earned"]</th>
                            <th>@Localizer["Status"]</th>
                            <th>@Localizer["Actions"]</th>
                        </tr>
                    </thead>
                    <tbody id="accountsTableBody">
                        @foreach (var account in Model.ViewModel.ActiveAccounts)
                        {
                            <tr class="account-row" data-account-id="@account.Id">
                                <td>
                                    <div class="plan-info">
                                        <div class="plan-name">@account.PlanName</div>
                                        <small class="plan-term">@account.TermDisplayText</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="amount-info">
                                        <span class="amount-value">@account.FormattedAmount</span>
                                        <small class="amount-currency">RZW</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="interest-rate">@account.FormattedInterestRate</span>
                                </td>
                                <td>
                                    <div class="progress-info">
                                        <div class="progress progress-sm">
                                            <div class="progress-bar @(account.IsNearMaturity ? "bg-warning" : "bg-primary")" 
                                                 role="progressbar" 
                                                 style="width: @account.ProgressPercentage.ToString("N1")%"
                                                 data-progress="@account.Id">
                                            </div>
                                        </div>
                                        <small class="progress-text">
                                            @if (account.IsMatured)
                                            {
                                                <span class="text-success">@Localizer["Matured"]</span>
                                            }
                                            else if (account.IsNearMaturity)
                                            {
                                                <span class="text-warning">@account.DaysRemaining @Localizer["days left"]</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">@account.DaysRemaining @Localizer["days left"]</span>
                                            }
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <div class="earned-info">
                                        <span class="earned-value">@account.FormattedTotalEarned</span>
                                        <small class="earned-currency">RZW</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge @account.StatusBadgeClass">
                                        @account.StatusDisplayText
                                    </span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="/MyAccount/RzwSavings/Details/@account.Id" 
                                           class="btn btn-sm btn-outline-primary" 
                                           title="@Localizer["View Details"]">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @if (account.IsMatured)
                                        {
                                            <button type="button" 
                                                    class="btn btn-sm btn-success" 
                                                    onclick="withdrawMatured(@account.Id)"
                                                    title="@Localizer["Withdraw"]">
                                                <i class="fas fa-download"></i>
                                            </button>
                                        }
                                        else
                                        {
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-warning" 
                                                    onclick="earlyWithdraw(@account.Id)"
                                                    title="@Localizer["Early Withdrawal"]">
                                                <i class="fas fa-exclamation-triangle"></i>
                                            </button>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            <!-- Mobile Cards -->
            <div class="mobile-accounts d-lg-none">
                @foreach (var account in Model.ViewModel.ActiveAccounts)
                {
                    <div class="account-card" data-account-id="@account.Id">
                        <div class="card-header">
                            <div class="account-plan">
                                <h6 class="plan-name">@account.PlanName</h6>
                                <small class="plan-term">@account.TermDisplayText</small>
                            </div>
                            <div class="account-status">
                                <span class="badge @account.StatusBadgeClass">
                                    @account.StatusDisplayText
                                </span>
                            </div>
                        </div>
                        
                        <div class="card-body">
                            <div class="account-details">
                                <div class="detail-row">
                                    <span class="detail-label">@Localizer["Amount"]:</span>
                                    <span class="detail-value">@account.FormattedAmount RZW</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">@Localizer["Interest Rate"]:</span>
                                    <span class="detail-value">@account.FormattedInterestRate</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">@Localizer["Earned"]:</span>
                                    <span class="detail-value">@account.FormattedTotalEarned RZW</span>
                                </div>
                            </div>
                            
                            <div class="progress-section">
                                <div class="progress-header">
                                    <span class="progress-label">@Localizer["Progress"]</span>
                                    <span class="progress-percentage">@account.ProgressPercentage.ToString("N1")%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar @(account.IsNearMaturity ? "bg-warning" : "bg-primary")" 
                                         role="progressbar" 
                                         style="width: @account.ProgressPercentage.ToString("N1")%">
                                    </div>
                                </div>
                                <small class="progress-info">
                                    @if (account.IsMatured)
                                    {
                                        <span class="text-success">@Localizer["Matured - Ready to withdraw"]</span>
                                    }
                                    else if (account.IsNearMaturity)
                                    {
                                        <span class="text-warning">@account.DaysRemaining @Localizer["days remaining"]</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">@account.DaysRemaining @Localizer["days remaining"]</span>
                                    }
                                </small>
                            </div>
                        </div>
                        
                        <div class="card-footer">
                            <div class="card-actions">
                                <a href="/MyAccount/RzwSavings/Details/@account.Id" 
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye"></i> @Localizer["Details"]
                                </a>
                                @if (account.IsMatured)
                                {
                                    <button type="button" 
                                            class="btn btn-success btn-sm" 
                                            onclick="withdrawMatured(@account.Id)">
                                        <i class="fas fa-download"></i> @Localizer["Withdraw"]
                                    </button>
                                }
                                else
                                {
                                    <button type="button" 
                                            class="btn btn-outline-warning btn-sm" 
                                            onclick="earlyWithdraw(@account.Id)">
                                        <i class="fas fa-exclamation-triangle"></i> @Localizer["Early Withdrawal"]
                                    </button>
                                }
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    }
    else
    {
        <!-- Empty State -->
        <div class="empty-state-section">
            <div class="empty-state-card">
                <div class="empty-icon">
                    <i class="fas fa-piggy-bank fa-4x text-muted"></i>
                </div>
                <div class="empty-content">
                    <h5 class="empty-title">@Localizer["No Active Savings Accounts"]</h5>
                    <p class="empty-description">
                        @Localizer["You don't have any active savings accounts yet. Start earning guaranteed interest by creating your first savings account."]
                    </p>
                    <div class="empty-stats">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="stat-item">
                                    <div class="stat-value">11%</div>
                                    <div class="stat-label">@Localizer["Daily APY"]</div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item">
                                    <div class="stat-value">12%</div>
                                    <div class="stat-label">@Localizer["Monthly APY"]</div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item">
                                    <div class="stat-value">15%</div>
                                    <div class="stat-label">@Localizer["Yearly APY"]</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="empty-actions">
                    @if (Model.ViewModel.HasAvailableBalance)
                    {
                        <a href="/MyAccount/RzwSavings/Create" class="btn btn-primary btn-lg">
                            <i class="fas fa-plus-circle"></i> @Localizer["Create First Savings Account"]
                        </a>
                        <a href="/MyAccount/Wallet" class="btn btn-outline-secondary">
                            <i class="fas fa-wallet"></i> @Localizer["View Wallet"]
                        </a>
                    }
                    else
                    {
                        <div class="no-balance-message">
                            <p class="text-muted mb-3">
                                <i class="fas fa-info-circle"></i>
                                @Localizer["You need RZW tokens to create a savings account"]
                            </p>
                            <a href="/Market" class="btn btn-primary">
                                <i class="fas fa-shopping-cart"></i> @Localizer["Buy RZW Tokens"]
                            </a>
                            <a href="/MyAccount/Deposit" class="btn btn-outline-primary">
                                <i class="fas fa-plus"></i> @Localizer["Deposit Funds"]
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    }
</div>
```

#### 5.2.4.2 Accounts Table CSS

**Dosya**: `src/wwwroot/css/rzw-savings-index.css` (Accounts table styles ekleme)
```css
/* Active Accounts Section */
.active-accounts-section {
    margin-bottom: 30px;
}

.section-header {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.section-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Accounts Table */
.accounts-table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    overflow: hidden;
}

.accounts-table {
    margin: 0;
    border: none;
}

.accounts-table thead th {
    background: #f8f9fa;
    border: none;
    font-weight: 600;
    color: #495057;
    padding: 15px;
    font-size: 0.9rem;
}

.accounts-table tbody td {
    padding: 15px;
    border-top: 1px solid #e9ecef;
    vertical-align: middle;
}

.account-row {
    transition: background-color 0.2s ease;
}

.account-row:hover {
    background-color: #f8f9fa;
}

/* Table Cell Styles */
.plan-info .plan-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 2px;
}

.plan-info .plan-term {
    color: #6c757d;
    font-size: 0.85rem;
}

.amount-info {
    text-align: right;
}

.amount-value {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #2c3e50;
    font-size: 1.1rem;
}

.amount-currency {
    color: #6c757d;
    font-size: 0.85rem;
    display: block;
}

.interest-rate {
    font-weight: 600;
    color: #28a745;
    font-size: 1rem;
}

.progress-info {
    min-width: 120px;
}

.progress-sm {
    height: 6px;
    margin-bottom: 5px;
}

.progress-text {
    font-size: 0.8rem;
}

.earned-info {
    text-align: right;
}

.earned-value {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #28a745;
    font-size: 1rem;
}

.earned-currency {
    color: #6c757d;
    font-size: 0.85rem;
    display: block;
}

.action-buttons {
    display: flex;
    gap: 5px;
}

.action-buttons .btn {
    padding: 5px 8px;
}

/* Mobile Account Cards */
.mobile-accounts {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.account-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    overflow: hidden;
    border: 1px solid #e9ecef;
}

.account-card .card-header {
    background: #f8f9fa;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e9ecef;
}

.account-plan .plan-name {
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    font-size: 1rem;
}

.account-plan .plan-term {
    color: #6c757d;
    font-size: 0.85rem;
}

.account-card .card-body {
    padding: 15px;
}

.account-details {
    margin-bottom: 15px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.detail-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.detail-value {
    font-weight: 600;
    color: #2c3e50;
    font-family: 'Courier New', monospace;
}

.progress-section {
    margin-bottom: 15px;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.progress-label {
    font-size: 0.9rem;
    color: #6c757d;
}

.progress-percentage {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
}

.progress-info {
    font-size: 0.85rem;
    margin-top: 5px;
}

.account-card .card-footer {
    background: #f8f9fa;
    padding: 15px;
    border-top: 1px solid #e9ecef;
}

.card-actions {
    display: flex;
    gap: 10px;
    justify-content: space-between;
}

.card-actions .btn {
    flex: 1;
    font-size: 0.85rem;
}

/* Empty State */
.empty-state-section {
    margin: 40px 0;
}

.empty-state-card {
    background: white;
    border-radius: 15px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
}

.empty-icon {
    margin-bottom: 25px;
    opacity: 0.6;
}

.empty-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 15px;
}

.empty-description {
    color: #6c757d;
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 25px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.empty-stats {
    margin: 30px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
}

.empty-stats .stat-item {
    padding: 10px;
}

.empty-stats .stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #28a745;
    margin-bottom: 5px;
}

.empty-stats .stat-label {
    font-size: 0.85rem;
    color: #6c757d;
}

.empty-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.no-balance-message {
    text-align: center;
}

.no-balance-message .btn {
    margin: 0 5px;
}

/* Badge Styles */
.badge-success {
    background-color: #28a745;
}

.badge-warning {
    background-color: #ffc107;
    color: #212529;
}

.badge-info {
    background-color: #17a2b8;
}

.badge-secondary {
    background-color: #6c757d;
}

.badge-danger {
    background-color: #dc3545;
}

/* Responsive Design */
@media (max-width: 768px) {
    .section-header {
        margin-bottom: 20px;
    }
    
    .section-title {
        font-size: 1.1rem;
    }
    
    .empty-state-card {
        padding: 30px 20px;
    }
    
    .empty-title {
        font-size: 1.2rem;
    }
    
    .empty-description {
        font-size: 1rem;
    }
    
    .empty-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .empty-actions .btn {
        width: 100%;
        max-width: 300px;
    }
}

@media (max-width: 576px) {
    .account-card .card-header,
    .account-card .card-body,
    .account-card .card-footer {
        padding: 12px;
    }
    
    .card-actions {
        flex-direction: column;
        gap: 8px;
    }
    
    .empty-state-card {
        padding: 25px 15px;
    }
    
    .empty-stats {
        padding: 15px;
    }
    
    .empty-stats .stat-value {
        font-size: 1.3rem;
    }
}

/* Loading States */
.accounts-loading {
    opacity: 0.6;
    pointer-events: none;
}

.accounts-loading .account-row,
.accounts-loading .account-card {
    background: #f8f9fa;
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}
```

## 📋 Kontrol Listesi

### ✅ YAPILACAKLAR
- [ ] Aktif hesaplar tablosu HTML oluşturma
- [ ] Mobile card layout oluşturma
- [ ] Empty state design oluşturma
- [ ] CSS styling ekleme
- [ ] Progress bar'lar ekleme
- [ ] Action button'ları ekleme
- [ ] Responsive design testleri

### 🔄 YAPILMAKTA OLANLAR
- (Henüz başlanmadı)

### ✅ YAPILMIŞLAR
- (Henüz tamamlanmadı)

## 🧪 Test Kriterleri

### Table Tests
- [ ] Desktop table doğru görüntüleniyor
- [ ] Mobile cards doğru görüntüleniyor
- [ ] Progress bar'lar doğru çalışıyor
- [ ] Action button'ları çalışıyor

### Empty State Tests
- [ ] Empty state doğru görüntüleniyor
- [ ] CTA button'ları çalışıyor
- [ ] Conditional content doğru

## 📝 Notlar

### Önemli Özellikler
- Responsive table/card design
- Progress visualization
- Status badges
- Quick actions
- Empty state with CTAs

### Status Badge Colors
- **Active**: Green (success)
- **Near Maturity**: Yellow (warning)
- **Matured**: Blue (info)
- **Withdrawn**: Gray (secondary)
- **Cancelled**: Red (danger)

### Sonraki Adım
Bu adım tamamlandıktan sonra **Faz 5.3: Yeni Vadeli Hesap Sayfası** başlayacak.

---
**Tahmini Süre**: 2-3 saat
**Öncelik**: Yüksek
**Bağımlılıklar**: Adım 5.2.1, 5.2.2, 5.2.3 tamamlanmış olmalı
