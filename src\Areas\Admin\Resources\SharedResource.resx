﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="(filtered from _MAX_ total records)" xml:space="preserve">
    <value>(filtered from _MAX_ total records)</value>
    <comment>Filtered records info text</comment>
  </data>
  <data name="0.0.0.0" xml:space="preserve">
    <value>0.0.0.0</value>
    <comment>Default IP address</comment>
  </data>
  <data name="24/7 customer support" xml:space="preserve">
    <value>24/7 customer support</value>
    <comment>Benefit 4</comment>
  </data>
  <data name="A percentage for this package and level already exists. Please edit the existing one." xml:space="preserve">
    <value>A percentage for this package and level already exists. Please edit the existing one.</value>
    <comment>Error message for duplicate package reward percentage</comment>
  </data>
  <data name="A record of each reward will be stored in the system." xml:space="preserve">
    <value>A record of each reward will be stored in the system.</value>
    <comment>Instruction for reward distribution</comment>
  </data>
  <data name="A setting with this key already exists" xml:space="preserve">
    <value>A setting with this key already exists</value>
    <comment>Error message when trying to create a setting with an existing key</comment>
  </data>
  <data name="A wallet already exists for this user and coin" xml:space="preserve">
    <value>A wallet already exists for this user and coin</value>
    <comment>Error message when trying to create a wallet that already exists</comment>
  </data>
  <data name="About RazeWin" xml:space="preserve">
    <value>About RazeWin</value>
    <comment>About page title</comment>
  </data>
  <data name="About Us" xml:space="preserve">
    <value>About Us</value>
  </data>
  <data name="Access Denied" xml:space="preserve">
    <value>Access Denied</value>
    <comment>Error message when access is denied</comment>
  </data>
  <data name="Account Creation Text" xml:space="preserve">
    <value>You must register with your real identity information to use our platform. You are responsible for ensuring that all information you provide is accurate and up-to-date.</value>
    <comment>Text for account creation section in user agreement</comment>
  </data>
  <data name="Account Creation Title" xml:space="preserve">
    <value>Account Creation</value>
    <comment>Title for account creation section in user agreement</comment>
  </data>
  <data name="Account Holder" xml:space="preserve">
    <value>Account Holder</value>
    <comment>Account holder label</comment>
  </data>
  <data name="Account holder name cannot exceed 100 characters" xml:space="preserve">
    <value>Account holder name cannot exceed 100 characters</value>
    <comment>Validation message for account holder length</comment>
  </data>
  <data name="Account Holder Name Too Long" xml:space="preserve">
    <value>Account holder name cannot exceed 100 characters</value>
    <comment>Validation message for account holder length</comment>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>Actions</value>
    <comment>Actions column header</comment>
  </data>
  <data name="Active" xml:space="preserve">
    <value>Active</value>
    <comment>Active status</comment>
  </data>
  <data name="Active Savings Accounts" xml:space="preserve">
    <value>Active Savings Accounts</value>
    <comment>Number of active savings accounts</comment>
  </data>
  <data name="Additional Notes (Optional)" xml:space="preserve">
    <value>Additional Notes (Optional)</value>
    <comment>Label for optional notes field</comment>
  </data>
  <data name="Address" xml:space="preserve">
    <value>Address</value>
    <comment>Address label</comment>
  </data>
  <data name="Address copied to clipboard" xml:space="preserve">
    <value>Address copied to clipboard</value>
    <comment>Message shown when an address is copied to clipboard</comment>
  </data>
  <data name="Admin" xml:space="preserve">
    <value>Admin</value>
    <comment>Admin label</comment>
  </data>
  <data name="Admin Adjustment" xml:space="preserve">
    <value>Admin Adjustment</value>
    <comment>Transaction type for admin adjustments</comment>
  </data>
  <data name="Admin Panel" xml:space="preserve">
    <value>Admin Panel</value>
  </data>
  <data name="After sending cryptocurrency to one of the addresses above, please fill out this form to report your deposit." xml:space="preserve">
    <value>After sending cryptocurrency to one of the addresses above, please fill out this form to report your deposit.</value>
    <comment>Instructions for reporting cryptocurrency deposits</comment>
  </data>
  <data name="Agreement Acceptance Text" xml:space="preserve">
    <value>By accepting this agreement, you declare that you have read and understood all of the above terms.</value>
    <comment>Text for agreement acceptance in user agreement</comment>
  </data>
  <data name="All Accounts" xml:space="preserve">
    <value>All Accounts</value>
    <comment>All accounts option in filter</comment>
  </data>
  <data name="All Payments" xml:space="preserve">
    <value>All Payments</value>
    <comment>Label for all payments section</comment>
  </data>
  <data name="All Trades" xml:space="preserve">
    <value>All Trades</value>
    <comment>Label for all trades in trade history</comment>
  </data>
  <data name="Amount" xml:space="preserve">
    <value>Amount</value>
    <comment>Amount field label</comment>
  </data>
  <data name="Amount (Cents)" xml:space="preserve">
    <value>Amount (Cents)</value>
    <comment>Amount cents label</comment>
  </data>
  <data name="Amount (RZW)" xml:space="preserve">
    <value>Amount (RZW)</value>
    <comment>Label for amount in RZW</comment>
  </data>
  <data name="Amount (TRY)" xml:space="preserve">
    <value>Amount (TRY)</value>
    <comment>Label for amount in Turkish Lira</comment>
  </data>
  <data name="Amount Cannot Be Negative" xml:space="preserve">
    <value>Amount cannot be negative</value>
    <comment>Validation message for negative amount</comment>
  </data>
  <data name="Amount cannot exceed 1,000,000" xml:space="preserve">
    <value>Amount cannot exceed 1,000,000</value>
    <comment>Validation message for maximum amount</comment>
  </data>
  <data name="Amount must be at least 0.00000001" xml:space="preserve">
    <value>Amount must be at least 0.00000001</value>
    <comment>Validation message for minimum amount</comment>
  </data>
  <data name="Amount must be between" xml:space="preserve">
    <value>Amount must be between</value>
    <comment>Validation message for amount limits</comment>
  </data>
  <data name="Amount must be between {0} and {1} RZW" xml:space="preserve">
    <value>Amount must be between {0} and {1} RZW</value>
    <comment>Validation message with plan limits</comment>
  </data>
  <data name="Amount Required" xml:space="preserve">
    <value>Amount is required</value>
    <comment>Amount required validation message</comment>
  </data>
  <data name="Amount to buy" xml:space="preserve">
    <value>Amount to buy</value>
    <comment>Label for buy amount input</comment>
  </data>
  <data name="Amount to Receive" xml:space="preserve">
    <value>Amount to Receive</value>
    <comment>Amount to Receive in trading form</comment>
  </data>
  <data name="Amount to sell" xml:space="preserve">
    <value>Amount to Sell</value>
    <comment>Amount to sell in trading form</comment>
  </data>
  <data name="Amount to spend" xml:space="preserve">
    <value>Amount to Spend</value>
    <comment>Amount to spend in trading form</comment>
  </data>
  <data name="Amount:" xml:space="preserve">
    <value>Amount:</value>
    <comment>Amount label</comment>
  </data>
  <data name="An error occurred during early withdrawal. Please try again." xml:space="preserve">
    <value>An error occurred during early withdrawal. Please try again.</value>
    <comment>Error message for early withdrawal failure</comment>
  </data>
  <data name="An error occurred while creating the savings account. Please try again." xml:space="preserve">
    <value>An error occurred while creating the savings account. Please try again.</value>
    <comment>Error message for account creation failure</comment>
  </data>
  <data name="An error occurred while distributing rewards: {0}" xml:space="preserve">
    <value>An error occurred while distributing rewards: {0}</value>
    <comment>Error message for reward distribution</comment>
  </data>
  <data name="An error occurred: {0}" xml:space="preserve">
    <value>An error occurred: {0}</value>
    <comment>Generic error message with parameter</comment>
  </data>
  <data name="and" xml:space="preserve">
    <value>and</value>
    <comment>Conjunction word for amount limits</comment>
  </data>
  <data name="and their referrals." xml:space="preserve">
    <value>and their referrals.</value>
    <comment>Used in referral tree page</comment>
  </data>
  <data name="Anti Money Laundering Policy" xml:space="preserve">
    <value>Anti Money Laundering Policy</value>
    <comment>Title for Anti Money Laundering Policy page</comment>
  </data>
  <data name="Any additional information about your deposit" xml:space="preserve">
    <value>Any additional information about your deposit</value>
    <comment>Label for additional deposit information field</comment>
  </data>
  <data name="API" xml:space="preserve">
    <value>API</value>
    <comment>API column header</comment>
  </data>
  <data name="API Pair" xml:space="preserve">
    <value>API Pair</value>
    <comment>Label for API pair selection</comment>
  </data>
  <data name="API Service" xml:space="preserve">
    <value>API Service</value>
    <comment>API service column header</comment>
  </data>
  <data name="Apply" xml:space="preserve">
    <value>Apply</value>
  </data>
  <data name="Approved" xml:space="preserve">
    <value>Approved</value>
    <comment>Approved status</comment>
  </data>
  <data name="Approved Deposits" xml:space="preserve">
    <value>Approved Deposits</value>
    <comment>Title for approved deposits list</comment>
  </data>
  <data name="Approved or rejected payments cannot be edited." xml:space="preserve">
    <value>Approved or rejected payments cannot be edited.</value>
    <comment>Error message when trying to edit approved/rejected payments</comment>
  </data>
  <data name="Approved or rejected withdrawals cannot be edited." xml:space="preserve">
    <value>Approved or rejected withdrawals cannot be edited.</value>
    <comment>Error message when trying to edit approved/rejected withdrawals</comment>
  </data>
  <data name="Are you sure you want to" xml:space="preserve">
    <value>Are you sure you want to</value>
    <comment>Start of confirmation message</comment>
  </data>
  <data name="Are you sure you want to delete this user?" xml:space="preserve">
    <value>Are you sure you want to delete this user?</value>
    <comment>Confirmation message for user deletion</comment>
  </data>
  <data name="Are you sure you want to purchase the" xml:space="preserve">
    <value>Are you sure you want to purchase the</value>
    <comment>Confirmation message part 1</comment>
  </data>
  <data name="Are you sure you want to withdraw early? This action cannot be undone." xml:space="preserve">
    <value>Are you sure you want to withdraw early? This action cannot be undone.</value>
    <comment>Early withdrawal confirmation message</comment>
  </data>
  <data name="Attract your customers' attention with vibrant and eye-catching visuals." xml:space="preserve">
    <value>Attract your customers' attention with vibrant and eye-catching visuals.</value>
  </data>
  <data name="Auto Generated Description For Tracking" xml:space="preserve">
    <value>This description has been automatically generated and will be used to track your deposit transaction.</value>
    <comment>Description field help text</comment>
  </data>
  <data name="Auto Refresh" xml:space="preserve">
    <value>Auto Refresh</value>
    <comment>Label for auto refresh toggle</comment>
  </data>
  <data name="Auto Renew" xml:space="preserve">
    <value>Auto Renew</value>
    <comment>Label for auto renew checkbox</comment>
  </data>
  <data name="Automatically renew when maturity date is reached" xml:space="preserve">
    <value>Automatically renew when maturity date is reached</value>
    <comment>Description for auto renew option</comment>
  </data>
  <data name="Available Balance" xml:space="preserve">
    <value>Available Balance</value>
    <comment>Available balance label in trading form</comment>
  </data>
  <data name="Available for new savings accounts" xml:space="preserve">
    <value>Available for new savings accounts</value>
    <comment>Description for available balance</comment>
  </data>
  <data name="Available Packages" xml:space="preserve">
    <value>Available Packages</value>
    <comment>Section heading for available packages</comment>
  </data>
  <data name="Available Savings Plans" xml:space="preserve">
    <value>Available Savings Plans</value>
    <comment>Available savings plans section title</comment>
  </data>
  <data name="Available Withdrawal Limit" xml:space="preserve">
    <value>Available Withdrawal Limit</value>
    <comment>Available withdrawal limit label</comment>
  </data>
  <data name="Average Per Payment" xml:space="preserve">
    <value>Average Per Payment</value>
    <comment>Average amount per payment</comment>
  </data>
  <data name="Back to List" xml:space="preserve">
    <value>Back to List</value>
    <comment>Label for back to list button</comment>
  </data>
  <data name="Back to My Savings" xml:space="preserve">
    <value>Back to My Savings</value>
    <comment>Button to go back to savings list</comment>
  </data>
  <data name="Background Services Status" xml:space="preserve">
    <value>Background Services Status</value>
    <comment>Title for background services status section</comment>
  </data>
  <data name="Balance" xml:space="preserve">
    <value>Balance</value>
  </data>
  <data name="Balance After Interest" xml:space="preserve">
    <value>Balance After Interest</value>
    <comment>Balance after interest column header</comment>
  </data>
  <data name="Balance History" xml:space="preserve">
    <value>Balance History</value>
    <comment>Page title for balance history</comment>
  </data>
  <data name="Balance Sufficiency Check" xml:space="preserve">
    <value>Balance Sufficiency Check</value>
    <comment>Check item for balance sufficiency</comment>
  </data>
  <data name="Balance Transactions" xml:space="preserve">
    <value>Balance Transactions</value>
    <comment>Title for the Balance Transactions list</comment>
  </data>
  <data name="Bank Account" xml:space="preserve">
    <value>Bank Account</value>
    <comment>Label for bank account</comment>
  </data>
  <data name="Bank Information" xml:space="preserve">
    <value>Bank Information</value>
    <comment>Bank information section header</comment>
  </data>
  <data name="Bank Name" xml:space="preserve">
    <value>Bank Name</value>
    <comment>Label for bank name</comment>
  </data>
  <data name="Bank Transfer" xml:space="preserve">
    <value>Bank Transfer</value>
    <comment>Bank transfer deposit method</comment>
  </data>
  <data name="Bank:" xml:space="preserve">
    <value>Bank:</value>
    <comment>Bank label</comment>
  </data>
  <data name="Bank: {0}, IBAN: {1}, Transfer Time: {2}, Customer Note: {3}" xml:space="preserve">
    <value>Bank: {0}, IBAN: {1}, Transfer Time: {2}, Customer Note: {3}</value>
    <comment>Bank transfer details format</comment>
  </data>
  <data name="Bank: {0}, Reference: {1}" xml:space="preserve">
    <value>Bank: {0}, Reference: {1}</value>
    <comment>Bank reference format</comment>
  </data>
  <data name="Banks" xml:space="preserve">
    <value>Banks</value>
    <comment>Banks menu item</comment>
  </data>
  <data name="Be part of the RazeWin community and experience the future of cryptocurrency trading." xml:space="preserve">
    <value>Be part of the RazeWin community and experience the future of cryptocurrency trading.</value>
    <comment>Community section description</comment>
  </data>
  <data name="Benefits" xml:space="preserve">
    <value>Benefits</value>
    <comment>Label for benefits</comment>
  </data>
  <data name="Birth Date" xml:space="preserve">
    <value>Birth Date</value>
    <comment>Birth date field label</comment>
  </data>
  <data name="Bitcoin (BTC) Deposit Address" xml:space="preserve">
    <value>Bitcoin (BTC) Deposit Address</value>
    <comment>Label for Bitcoin deposit address</comment>
  </data>
  <data name="Blockchain Powered Trading Experience" xml:space="preserve">
    <value>Carry your earnings to the future with a blockchain-powered trading experience</value>
    <comment>Description for the second slider</comment>
  </data>
  <data name="BNB (BEP20) Deposit Address" xml:space="preserve">
    <value>BNB (BEP20) Deposit Address</value>
    <comment>Label for BNB deposit address</comment>
  </data>
  <data name="BNB Address" xml:space="preserve">
    <value>BNB Address</value>
    <comment>Label for BNB address</comment>
  </data>
  <data name="Bonus" xml:space="preserve">
    <value>Bonus</value>
    <comment>Bonus label for package purchase</comment>
  </data>
  <data name="Browse Markets" xml:space="preserve">
    <value>Browse Markets</value>
    <comment>Label for browse markets button</comment>
  </data>
  <data name="BTC Address" xml:space="preserve">
    <value>BTC Address</value>
    <comment>Label for BTC address</comment>
  </data>
  <data name="Button not found for element" xml:space="preserve">
    <value>Button not found for element</value>
    <comment>Error message when button for copying is not found</comment>
  </data>
  <data name="Buy" xml:space="preserve">
    <value>Buy</value>
    <comment>Buy button text</comment>
  </data>
  <data name="Buy Coin" xml:space="preserve">
    <value>Buy Coin</value>
    <comment>Label for buy coin button</comment>
  </data>
  <data name="Buy Price" xml:space="preserve">
    <value>Buy Price</value>
    <comment>Buy price column header</comment>
  </data>
  <data name="Call Me Back" xml:space="preserve">
    <value>Call Me Back?</value>
    <comment>Call me back link text</comment>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
    <comment>Cancel button text</comment>
  </data>
  <data name="Cancelled" xml:space="preserve">
    <value>Cancelled</value>
    <comment>Label for cancelled status</comment>
  </data>
  <data name="Cannot delete package because it has reward percentages. Please remove reward percentages first." xml:space="preserve">
    <value>Cannot delete package because it has reward percentages. Please remove reward percentages first.</value>
    <comment>Error message when trying to delete a package that has reward percentages</comment>
  </data>
  <data name="Cannot delete package because it is assigned to users. Please remove user assignments first." xml:space="preserve">
    <value>Cannot delete package because it is assigned to users. Please remove user assignments first.</value>
    <comment>Error message when trying to delete a package that is assigned to users</comment>
  </data>
  <data name="Cannot delete user package because it has associated referral rewards. Please remove referral rewards first." xml:space="preserve">
    <value>Cannot delete user package because it has associated referral rewards. Please remove referral rewards first.</value>
    <comment>Error message when trying to delete a user package that has associated referral rewards</comment>
  </data>
  <data name="Cannot determine when the deposit was approved" xml:space="preserve">
    <value>Cannot determine when the deposit was approved</value>
    <comment>Error message for deposit approval date</comment>
  </data>
  <data name="Cannot Reverse" xml:space="preserve">
    <value>Cannot Reverse</value>
    <comment>Button text for deposits that cannot be reversed</comment>
  </data>
  <data name="Cannot reverse deposit because rewards have been distributed" xml:space="preserve">
    <value>Cannot reverse deposit because rewards have been distributed</value>
    <comment>Error message for reward distribution</comment>
  </data>
  <data name="Cannot reverse deposit because user has made trades after this deposit was approved" xml:space="preserve">
    <value>Cannot reverse deposit because user has made trades after this deposit was approved</value>
    <comment>Error message for trade activity</comment>
  </data>
  <data name="Cannot reverse deposit because user has made withdrawals after this deposit was approved" xml:space="preserve">
    <value>Cannot reverse deposit because user has made withdrawals after this deposit was approved</value>
    <comment>Error message for withdrawal activity</comment>
  </data>
  <data name="Cannot reverse deposit because user has purchased packages after this deposit was approved" xml:space="preserve">
    <value>Cannot reverse deposit because user has purchased packages after this deposit was approved</value>
    <comment>Error message for package purchase</comment>
  </data>
  <data name="Cannot reverse deposit because user's balance is insufficient" xml:space="preserve">
    <value>Cannot reverse deposit because user's balance is insufficient</value>
    <comment>Error message for balance sufficiency</comment>
  </data>
  <data name="Change 24h" xml:space="preserve">
    <value>Change 24h</value>
    <comment>24-hour change column header</comment>
  </data>
  <data name="Change Password" xml:space="preserve">
    <value>Change Password</value>
    <comment>Change password tab title</comment>
  </data>
  <data name="Changes Text" xml:space="preserve">
    <value>This privacy policy may be updated from time to time. Important changes will be notified to you.</value>
    <comment>Text for changes section in privacy agreement</comment>
  </data>
  <data name="Changes Title" xml:space="preserve">
    <value>Changes</value>
    <comment>Title for changes section in privacy agreement</comment>
  </data>
  <data name="Chart" xml:space="preserve">
    <value>Chart</value>
    <comment>Chart page title</comment>
  </data>
  <data name="Choose Your Referral Package" xml:space="preserve">
    <value>Choose Your Referral Package</value>
    <comment>Package selection page heading</comment>
  </data>
  <data name="Clear" xml:space="preserve">
    <value>Clear</value>
    <comment>Clear filters button text</comment>
  </data>
  <data name="Coin" xml:space="preserve">
    <value>Coin</value>
    <comment>Coin label</comment>
  </data>
  <data name="Coin Symbol" xml:space="preserve">
    <value>Coin Symbol</value>
    <comment>Label for coin symbol</comment>
  </data>
  <data name="Comments" xml:space="preserve">
    <value>Comments</value>
    <comment>Comments section title</comment>
  </data>
  <data name="Company" xml:space="preserve">
    <value>Company</value>
    <comment>Label for company</comment>
  </data>
  <data name="Competitive interest rates" xml:space="preserve">
    <value>Competitive interest rates</value>
    <comment>Benefit 1</comment>
  </data>
  <data name="Completed" xml:space="preserve">
    <value>Completed</value>
    <comment>Completed status</comment>
  </data>
  <data name="Confirm Password" xml:space="preserve">
    <value>Confirm Password</value>
    <comment>Confirm password field label</comment>
  </data>
  <data name="Confirm Purchase" xml:space="preserve">
    <value>Confirm Purchase</value>
    <comment>Button text to confirm purchase</comment>
  </data>
  <data name="Confirm Purchase Package Message" xml:space="preserve">
    <value>Are you sure you want to purchase the {0} package for {1}?</value>
    <comment>Confirmation message for package purchase with package name and price</comment>
  </data>
  <data name="Confirm Upgrade" xml:space="preserve">
    <value>Confirm Upgrade</value>
    <comment>Modal title for package upgrade confirmation</comment>
  </data>
  <data name="Confirm Upgrade Package Message" xml:space="preserve">
    <value>Are you sure you want to upgrade to {0} package for {1}?</value>
    <comment>Confirmation message for package upgrade with package name and price</comment>
  </data>
  <data name="Contact" xml:space="preserve">
    <value>Contact</value>
    <comment>Contact link text</comment>
  </data>
  <data name="Contact Us" xml:space="preserve">
    <value>Contact Us</value>
    <comment>Contact tab title</comment>
  </data>
  <data name="Cookies And Tracking Text" xml:space="preserve">
    <value>Our website uses cookies to improve the user experience. You can control cookie usage from your browser settings.</value>
    <comment>Text for cookies and tracking section in privacy agreement</comment>
  </data>
  <data name="Cookies And Tracking Title" xml:space="preserve">
    <value>Cookies and Tracking</value>
    <comment>Title for cookies and tracking section in privacy agreement</comment>
  </data>
  <data name="Copied" xml:space="preserve">
    <value>Copied!</value>
    <comment>Copied notification text</comment>
  </data>
  <data name="Copied!" xml:space="preserve">
    <value>Copied!</value>
    <comment>Message shown when something is copied to clipboard</comment>
  </data>
  <data name="Copy" xml:space="preserve">
    <value>Copy</value>
    <comment>Copy button text</comment>
  </data>
  <data name="Could not find the balance transaction record for this deposit" xml:space="preserve">
    <value>Could not find the balance transaction record for this deposit</value>
    <comment>Error message for missing balance transaction</comment>
  </data>
  <data name="Could not parse cryptocurrency data" xml:space="preserve">
    <value>Could not parse cryptocurrency data</value>
    <comment>Error message when cryptocurrency data cannot be parsed</comment>
  </data>
  <data name="Count" xml:space="preserve">
    <value>Count</value>
    <comment>Count label</comment>
  </data>
  <data name="Countries" xml:space="preserve">
    <value>Countries</value>
    <comment>Countries label</comment>
  </data>
  <data name="Create" xml:space="preserve">
    <value>Create</value>
    <comment>Label for create button</comment>
  </data>
  <data name="Create a New" xml:space="preserve">
    <value>Create a New</value>
    <comment>Create a New</comment>
  </data>
  <data name="Create a New Market" xml:space="preserve">
    <value>Create a New Market</value>
    <comment>Title for create market page</comment>
  </data>
  <data name="Create a New Payment" xml:space="preserve">
    <value>Create a New Payment</value>
    <comment>Title for create payment page</comment>
  </data>
  <data name="Create a New Setting" xml:space="preserve">
    <value>Create a New Setting</value>
    <comment>Title for create setting page</comment>
  </data>
  <data name="Create a New Trade" xml:space="preserve">
    <value>Create a New Trade</value>
    <comment>Title for create trade page</comment>
  </data>
  <data name="Create a New Wallet" xml:space="preserve">
    <value>Create a New Wallet</value>
    <comment>Title for create wallet page</comment>
  </data>
  <data name="Create a New Withdrawal" xml:space="preserve">
    <value>Create a New Withdrawal</value>
    <comment>Title for create withdrawal page</comment>
  </data>
  <data name="Create Account" xml:space="preserve">
    <value>Create Account</value>
    <comment>Create account button text</comment>
  </data>
  <data name="Create Account to Start Earning" xml:space="preserve">
    <value>Create Account to Start Earning</value>
    <comment>Create account button</comment>
  </data>
  <data name="Create New Savings Account" xml:space="preserve">
    <value>Create New Savings Account</value>
    <comment>Button to create new savings account</comment>
  </data>
  <data name="Create Savings Account" xml:space="preserve">
    <value>Create Savings Account</value>
    <comment>Button text for creating savings account</comment>
  </data>
  <data name="Create Your Account" xml:space="preserve">
    <value>Create Your Account!</value>
    <comment>Create account step title</comment>
  </data>
  <data name="Created by admin" xml:space="preserve">
    <value>Created by admin</value>
    <comment>Created by admin text</comment>
  </data>
  <data name="Created Date" xml:space="preserve">
    <value>Created Date</value>
    <comment>Label for created date</comment>
  </data>
  <data name="Creation Date" xml:space="preserve">
    <value>Creation Date</value>
  </data>
  <data name="Credit Card" xml:space="preserve">
    <value>Credit Card</value>
    <comment>Credit card payment method</comment>
  </data>
  <data name="Cross Rates" xml:space="preserve">
    <value>Cross Rates</value>
    <comment>Cross Rates page title</comment>
  </data>
  <data name="Cryptocurrencies" xml:space="preserve">
    <value>Cryptocurrencies</value>
    <comment>Cryptocurrencies label</comment>
  </data>
  <data name="Cryptocurrency Deposit" xml:space="preserve">
    <value>Cryptocurrency Deposit</value>
    <comment>Label for cryptocurrency deposit option</comment>
  </data>
  <data name="Cryptocurrency Deposit Methods" xml:space="preserve">
    <value>Cryptocurrency Deposit Methods</value>
    <comment>Header for cryptocurrency deposit methods section</comment>
  </data>
  <data name="Cryptocurrency Deposits" xml:space="preserve">
    <value>Cryptocurrency Deposits</value>
    <comment>Label for cryptocurrency deposits section</comment>
  </data>
  <data name="Cryptocurrency Information" xml:space="preserve">
    <value>Cryptocurrency Information</value>
    <comment>Label for cryptocurrency information section</comment>
  </data>
  <data name="Cryptocurrency Type" xml:space="preserve">
    <value>Cryptocurrency Type</value>
    <comment>Label for cryptocurrency type</comment>
  </data>
  <data name="Currency" xml:space="preserve">
    <value>Currency</value>
    <comment>Label for currency</comment>
  </data>
  <data name="Currency_Symbol" xml:space="preserve">
    <value>TRY</value>
    <comment>Currency_Symbol</comment>
  </data>
  <data name="Current" xml:space="preserve">
    <value>Current</value>
    <comment>Label for current package</comment>
  </data>
  <data name="Current Balance" xml:space="preserve">
    <value>Current Balance</value>
    <comment>Current balance label</comment>
  </data>
  <data name="Current Culture" xml:space="preserve">
    <value>Current Culture</value>
    <comment>Label for current culture</comment>
  </data>
  <data name="Current Dealers" xml:space="preserve">
    <value>Current Dealers</value>
  </data>
  <data name="Current Invites" xml:space="preserve">
    <value>Current Invites</value>
    <comment>Label for current number of invites</comment>
  </data>
  <data name="Current Package" xml:space="preserve">
    <value>Current Package</value>
    <comment>Button text for current package</comment>
  </data>
  <data name="Current Password" xml:space="preserve">
    <value>Current Password</value>
    <comment>Current password field label</comment>
  </data>
  <data name="Current password is incorrect" xml:space="preserve">
    <value>Current password is incorrect</value>
    <comment>Error message for incorrect current password</comment>
  </data>
  <data name="Current status" xml:space="preserve">
    <value>Current status</value>
    <comment>Current status label</comment>
  </data>
  <data name="Current UI Culture" xml:space="preserve">
    <value>Current UI Culture</value>
    <comment>Label for current UI culture</comment>
  </data>
  <data name="Customer Login" xml:space="preserve">
    <value>Customer Login</value>
    <comment>Customer login title on login page</comment>
  </data>
  <data name="Daily Interest" xml:space="preserve">
    <value>Daily Interest</value>
    <comment>Label for daily interest amount</comment>
  </data>
  <data name="Daily Interest Rate" xml:space="preserve">
    <value>Daily Interest Rate</value>
    <comment>Label for daily interest rate</comment>
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value>Dashboard</value>
  </data>
  <data name="Data Collection Text" xml:space="preserve">
    <value>RAZEWIN collects your personal information such as identity information, contact information, financial information, and transaction data to provide its services.</value>
    <comment>Text for data collection section in privacy agreement</comment>
  </data>
  <data name="Data Collection Title" xml:space="preserve">
    <value>Data Collection</value>
    <comment>Title for data collection section in privacy agreement</comment>
  </data>
  <data name="Data Retention Text" xml:space="preserve">
    <value>Your personal data will be stored for as long as your account is active and for the period necessary to fulfill our legal obligations.</value>
    <comment>Text for data retention section in privacy agreement</comment>
  </data>
  <data name="Data Retention Title" xml:space="preserve">
    <value>Data Retention</value>
    <comment>Title for data retention section in privacy agreement</comment>
  </data>
  <data name="Data Security Text" xml:space="preserve">
    <value>RAZEWIN implements industry-standard security measures to protect your personal data. Your data is protected with encryption technologies.</value>
    <comment>Text for data security section in privacy agreement</comment>
  </data>
  <data name="Data Security Title" xml:space="preserve">
    <value>Data Security</value>
    <comment>Title for data security section in privacy agreement</comment>
  </data>
  <data name="Data Sharing Text" xml:space="preserve">
    <value>Your personal information is not shared with third parties without your explicit consent. However, it may be shared with authorized institutions in case of legal obligation.</value>
    <comment>Text for data sharing section in privacy agreement</comment>
  </data>
  <data name="Data Sharing Title" xml:space="preserve">
    <value>Data Sharing</value>
    <comment>Title for data sharing section in privacy agreement</comment>
  </data>
  <data name="Data Usage Text" xml:space="preserve">
    <value>The information we collect is used to manage your account, execute your transactions, fulfill legal obligations, and improve our services.</value>
    <comment>Text for data usage section in privacy agreement</comment>
  </data>
  <data name="Data Usage Title" xml:space="preserve">
    <value>Data Usage</value>
    <comment>Title for data usage section in privacy agreement</comment>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Date</value>
    <comment>Date column header</comment>
  </data>
  <data name="Date and Time" xml:space="preserve">
    <value>Date and Time</value>
    <comment>Label for date and time</comment>
  </data>
  <data name="Date Created" xml:space="preserve">
    <value>Date Created</value>
  </data>
  <data name="Days" xml:space="preserve">
    <value>Days</value>
    <comment>Days label</comment>
  </data>
  <data name="Days Elapsed" xml:space="preserve">
    <value>Days Elapsed</value>
    <comment>Days elapsed column header</comment>
  </data>
  <data name="Days Remaining" xml:space="preserve">
    <value>Days Remaining</value>
    <comment>Days remaining until maturity</comment>
  </data>
  <data name="Dealer" xml:space="preserve">
    <value>Dealer</value>
  </data>
  <data name="Dealer Network" xml:space="preserve">
    <value>Dealer Network</value>
  </data>
  <data name="Dealers" xml:space="preserve">
    <value>Dealers</value>
  </data>
  <data name="Decimal Places" xml:space="preserve">
    <value>Decimal Places</value>
    <comment>Label for decimal places</comment>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Delete Balance Transaction" xml:space="preserve">
    <value>Delete Balance Transaction</value>
    <comment>Title for deleting a balance transaction</comment>
  </data>
  <data name="Delete Package" xml:space="preserve">
    <value>Delete Package</value>
    <comment>Title for package delete page</comment>
  </data>
  <data name="Delete Package Reward Percentage" xml:space="preserve">
    <value>Delete Package Reward Percentage</value>
    <comment>Title for package reward percentage delete page</comment>
  </data>
  <data name="Delete User Package" xml:space="preserve">
    <value>Delete User Package</value>
    <comment>Title for user package delete page</comment>
  </data>
  <data name="Deleting this trade will not update user balances" xml:space="preserve">
    <value>Deleting this trade will not update user balances</value>
    <comment>Warning message when deleting a trade</comment>
  </data>
  <data name="Deletion was successful" xml:space="preserve">
    <value>Deletion was successful</value>
    <comment>Deletion was successful</comment>
  </data>
  <data name="Demo account is expired. You should buy a paid license" xml:space="preserve">
    <value>Demo account is expired. You should buy a paid license</value>
  </data>
  <data name="Deposit" xml:space="preserve">
    <value>Deposit</value>
    <comment>Deposit page title</comment>
  </data>
  <data name="Deposit Amount" xml:space="preserve">
    <value>Deposit Amount</value>
    <comment>Deposit amount label</comment>
  </data>
  <data name="Deposit Approval Transaction Check" xml:space="preserve">
    <value>Deposit Approval Transaction Check</value>
    <comment>Check item for deposit approval transaction</comment>
  </data>
  <data name="Deposit can be reversed" xml:space="preserve">
    <value>Deposit can be reversed</value>
    <comment>Status message for eligible deposits</comment>
  </data>
  <data name="Deposit Funds" xml:space="preserve">
    <value>Deposit Funds</value>
    <comment>Label for deposit funds button</comment>
  </data>
  <data name="Deposit has been successfully reversed" xml:space="preserve">
    <value>Deposit has been successfully reversed</value>
    <comment>Success message for deposit reversal</comment>
  </data>
  <data name="Deposit History" xml:space="preserve">
    <value>Deposit History</value>
    <comment>Deposit history section title</comment>
  </data>
  <data name="Deposit is not in approved state" xml:space="preserve">
    <value>Deposit is not in approved state</value>
    <comment>Error message for deposit status</comment>
  </data>
  <data name="Deposit must be in Approved state" xml:space="preserve">
    <value>Deposit must be in Approved state</value>
    <comment>Error message for deposit status check</comment>
  </data>
  <data name="Deposit reversal: {0}" xml:space="preserve">
    <value>Deposit reversal: {0}</value>
    <comment>Description for deposit reversal transaction</comment>
  </data>
  <data name="Deposit Status Check" xml:space="preserve">
    <value>Deposit Status Check</value>
    <comment>Check item for deposit status</comment>
  </data>
  <data name="Deposit to Your Account" xml:space="preserve">
    <value>Deposit to Your Account!</value>
    <comment>Deposit step title</comment>
  </data>
  <data name="Deposit Transaction - User ID:" xml:space="preserve">
    <value>Deposit Transaction - User ID:</value>
    <comment>Deposit transaction description part</comment>
  </data>
  <data name="Deposit Transaction - User ID: {0}, User Name: {1}. Transaction Date: {2}" xml:space="preserve">
    <value>Deposit Transaction - User ID: {0}, User Name: {1}. Transaction Date: {2}</value>
    <comment>Deposit transaction description format</comment>
  </data>
  <data name="Deposit was approved on {0}" xml:space="preserve">
    <value>Deposit was approved on {0}</value>
    <comment>Status message for deposit approval date</comment>
  </data>
  <data name="Deposit your crypto assets and earn interest with flexible terms." xml:space="preserve">
    <value>Deposit your crypto assets and earn interest with flexible terms.</value>
    <comment>Savings description</comment>
  </data>
  <data name="Deposit your RZW tokens and earn interest with flexible terms." xml:space="preserve">
    <value>Deposit your RZW tokens and earn interest with flexible terms.</value>
    <comment>Savings description for RZW</comment>
  </data>
  <data name="Deposit: {0}" xml:space="preserve">
    <value>Deposit: {0}</value>
    <comment>Description for deposit transaction</comment>
  </data>
  <data name="Deposits" xml:space="preserve">
    <value>Deposits</value>
    <comment>Used for deposit management in admin panel</comment>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Description</value>
    <comment>Description field label</comment>
  </data>
  <data name="Details" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="Device" xml:space="preserve">
    <value>Device</value>
  </data>
  <data name="Device Brand" xml:space="preserve">
    <value>Device Brand</value>
  </data>
  <data name="DigiSign. All rights reserved." xml:space="preserve">
    <value>DigiSign. All rights reserved.</value>
  </data>
  <data name="Distribute Rewards" xml:space="preserve">
    <value>Distribute Rewards</value>
    <comment>Button text for distributing rewards</comment>
  </data>
  <data name="Distributed" xml:space="preserve">
    <value>Distributed</value>
    <comment>Status label for distributed rewards</comment>
  </data>
  <data name="Distributed Rewards" xml:space="preserve">
    <value>Distributed Rewards</value>
    <comment>Title for distributed rewards section</comment>
  </data>
  <data name="Download for Android" xml:space="preserve">
    <value>Download for Android</value>
    <comment>Android app download button</comment>
  </data>
  <data name="Download for iOS" xml:space="preserve">
    <value>Download for iOS</value>
    <comment>iOS app download button</comment>
  </data>
  <data name="Download Razewin App and Start Earning RZW Tokens While Traveling" xml:space="preserve">
    <value>Download the Razewin app and start earning RZW Tokens while traveling! Use AI-powered navigation to travel efficiently and maximize your earnings.</value>
    <comment>Drive to earn slider description</comment>
  </data>
  <data name="Drive to Earn" xml:space="preserve">
    <value>DRIVE TO EARN</value>
    <comment>Drive to earn slider title</comment>
  </data>
  <data name="Dynamic Content" xml:space="preserve">
    <value>Dynamic Content</value>
  </data>
  <data name="Early Withdrawal Available" xml:space="preserve">
    <value>Early Withdrawal Available</value>
    <comment>Title for early withdrawal section</comment>
  </data>
  <data name="Early withdrawal completed successfully. Amount withdrawn: {0} RZW" xml:space="preserve">
    <value>Early withdrawal completed successfully. Amount withdrawn: {0} RZW</value>
    <comment>Success message for early withdrawal</comment>
  </data>
  <data name="Early Withdrawn" xml:space="preserve">
    <value>Early Withdrawn</value>
    <comment>Early withdrawal status</comment>
  </data>
  <data name="Earn Money" xml:space="preserve">
    <value>Earn Money</value>
    <comment>Earn money button</comment>
  </data>
  <data name="Earn Money with Crypto" xml:space="preserve">
    <value>Earn Money with Crypto</value>
    <comment>Earn money page title</comment>
  </data>
  <data name="Earn Money with Cryptocurrency" xml:space="preserve">
    <value>Earn Money with Cryptocurrency</value>
    <comment>Earn money section title</comment>
  </data>
  <data name="Earn passive income by holding and staking your cryptocurrencies." xml:space="preserve">
    <value>Earn passive income by holding and staking your cryptocurrencies.</value>
    <comment>Staking description</comment>
  </data>
  <data name="Earn passive income by holding and staking your RZW tokens." xml:space="preserve">
    <value>Earn passive income by holding and staking your RZW tokens.</value>
    <comment>Staking description for RZW</comment>
  </data>
  <data name="Earn While Driving" xml:space="preserve">
    <value>EARN WHILE DRIVING!</value>
    <comment>Earn while driving slider heading</comment>
  </data>
  <data name="EARN WHILE TRAVELING" xml:space="preserve">
    <value>EARN WHILE TRAVELING</value>
    <comment>RZW Token tagline</comment>
  </data>
  <data name="Earnings Cap" xml:space="preserve">
    <value>Earnings Cap</value>
    <comment>Label for earnings cap</comment>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Edit</value>
    <comment>Edit button text</comment>
  </data>
  <data name="Edit Balance Transaction" xml:space="preserve">
    <value>Edit Balance Transaction</value>
    <comment>Title for editing a balance transaction</comment>
  </data>
  <data name="Edit Bank" xml:space="preserve">
    <value>Edit Bank</value>
    <comment>Title for edit bank page</comment>
  </data>
  <data name="Edit Market" xml:space="preserve">
    <value>Edit Market</value>
    <comment>Title for edit market page</comment>
  </data>
  <data name="Edit Package" xml:space="preserve">
    <value>Edit Package</value>
    <comment>Title for package edit page</comment>
  </data>
  <data name="Edit Package Reward Percentage" xml:space="preserve">
    <value>Edit Package Reward Percentage</value>
    <comment>Title for package reward percentage edit page</comment>
  </data>
  <data name="Edit Payment" xml:space="preserve">
    <value>Edit Payment</value>
    <comment>Edit payment section title</comment>
  </data>
  <data name="Edit Setting" xml:space="preserve">
    <value>Edit Setting</value>
    <comment>Title for edit setting page</comment>
  </data>
  <data name="Edit Trade" xml:space="preserve">
    <value>Edit Trade</value>
    <comment>Title for edit trade page</comment>
  </data>
  <data name="Edit User Package" xml:space="preserve">
    <value>Edit User Package</value>
    <comment>Title for user package edit page</comment>
  </data>
  <data name="Edit Wallet" xml:space="preserve">
    <value>Edit Wallet</value>
    <comment>Title for edit wallet page</comment>
  </data>
  <data name="Edit Withdrawal" xml:space="preserve">
    <value>Edit Withdrawal</value>
    <comment>Title for edit withdrawal page</comment>
  </data>
  <data name="Effective APY" xml:space="preserve">
    <value>Effective APY</value>
    <comment>Label for effective annual percentage yield</comment>
  </data>
  <data name="Element not found" xml:space="preserve">
    <value>Element not found</value>
    <comment>Error message when element is not found</comment>
  </data>
  <data name="Eligible for Reversal" xml:space="preserve">
    <value>Eligible for Reversal</value>
    <comment>Status text for deposits eligible for reversal</comment>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Email</value>
    <comment>Email label</comment>
  </data>
  <data name="Email Address" xml:space="preserve">
    <value>Email Address</value>
    <comment>Email address field label</comment>
  </data>
  <data name="Enter a valid IBAN" xml:space="preserve">
    <value>Enter a valid IBAN</value>
    <comment>Validation message for IBAN</comment>
  </data>
  <data name="Enter account holder name" xml:space="preserve">
    <value>Enter account holder name</value>
    <comment>Enter account holder name placeholder</comment>
  </data>
  <data name="Enter amount" xml:space="preserve">
    <value>Enter amount</value>
    <comment>Enter amount placeholder</comment>
  </data>
  <data name="Enter amount to buy" xml:space="preserve">
    <value>Enter amount to buy</value>
    <comment>Placeholder for amount to buy input</comment>
  </data>
  <data name="Enter amount to sell" xml:space="preserve">
    <value>Enter amount to sell</value>
    <comment>Placeholder for amount to sell input</comment>
  </data>
  <data name="Enter amount to spend" xml:space="preserve">
    <value>Enter amount to spend</value>
    <comment>Placeholder for amount to spend input</comment>
  </data>
  <data name="Enter Bank Transfer Time" xml:space="preserve">
    <value>Enter the time when you made the bank transfer.</value>
    <comment>Transfer time field help text</comment>
  </data>
  <data name="Enter benefits as JSON array of features" xml:space="preserve">
    <value>Enter benefits as JSON array of features</value>
    <comment>Label for enter benefits as JSON array of features</comment>
  </data>
  <data name="Enter decimal part" xml:space="preserve">
    <value>Enter decimal part</value>
    <comment>Placeholder for decimal part input</comment>
  </data>
  <data name="Enter Full Name" xml:space="preserve">
    <value>Enter full name</value>
    <comment>Full name placeholder</comment>
  </data>
  <data name="Enter IBAN" xml:space="preserve">
    <value>Enter IBAN</value>
    <comment>Enter IBAN placeholder</comment>
  </data>
  <data name="Enter integer part" xml:space="preserve">
    <value>Enter integer part</value>
    <comment>Placeholder for integer part input</comment>
  </data>
  <data name="Enter the address you sent from" xml:space="preserve">
    <value>Enter the address you sent from</value>
    <comment>Label for sender address field</comment>
  </data>
  <data name="Enter the amount and transaction reference in the form." xml:space="preserve">
    <value>Enter the amount and transaction reference in the form.</value>
    <comment>Instructions for entering transaction details</comment>
  </data>
  <data name="Enter the amount you wish to withdraw." xml:space="preserve">
    <value>Enter the amount you wish to withdraw.</value>
    <comment>Withdrawal step 1</comment>
  </data>
  <data name="Enter the transaction hash/ID from your wallet" xml:space="preserve">
    <value>Enter the transaction hash/ID from your wallet</value>
    <comment>Label for transaction hash field</comment>
  </data>
  <data name="Enter transaction reference" xml:space="preserve">
    <value>Enter transaction reference</value>
    <comment>Placeholder for transaction reference field</comment>
  </data>
  <data name="Enter Valid Format" xml:space="preserve">
    <value>Please enter a valid format</value>
    <comment>Validation message for format</comment>
  </data>
  <data name="Enter Valid IBAN" xml:space="preserve">
    <value>Please enter a valid IBAN</value>
    <comment>Validation message for IBAN format</comment>
  </data>
  <data name="Enter your bank account IBAN for withdrawals (e.g., TR00 0000 0000 0000 0000 0000 00)" xml:space="preserve">
    <value>Enter your bank account IBAN for withdrawals (e.g., TR00 0000 0000 0000 0000 0000 00)</value>
    <comment>Placeholder for IBAN field in withdrawal form</comment>
  </data>
  <data name="Enter Your Full Name" xml:space="preserve">
    <value>Enter your full name</value>
    <comment>Sender full name placeholder</comment>
  </data>
  <data name="Entities" xml:space="preserve">
    <value>Entities</value>
    <comment>Entities section header</comment>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Error</value>
    <comment>Error title for alerts</comment>
  </data>
  <data name="Error changing password" xml:space="preserve">
    <value>Error changing password</value>
    <comment>Error message for password change</comment>
  </data>
  <data name="Error copying to clipboard" xml:space="preserve">
    <value>Error copying to clipboard</value>
    <comment>Error message when copying to clipboard fails</comment>
  </data>
  <data name="Error loading pairs" xml:space="preserve">
    <value>Error loading pairs</value>
    <comment>Error message when loading trading pairs fails</comment>
  </data>
  <data name="Error submitting payment: {0}" xml:space="preserve">
    <value>Error submitting payment: {0}</value>
    <comment>Error message for payment submission with parameter</comment>
  </data>
  <data name="Error submitting withdrawal: {0}" xml:space="preserve">
    <value>Error submitting withdrawal: {0}</value>
    <comment>Error message for withdrawal submission with parameter</comment>
  </data>
  <data name="Error updating profile" xml:space="preserve">
    <value>Error updating profile</value>
    <comment>Error message for profile update</comment>
  </data>
  <data name="Error validating IBAN" xml:space="preserve">
    <value>Error validating IBAN</value>
    <comment>Error message when IBAN validation fails</comment>
  </data>
  <data name="Example Time Format" xml:space="preserve">
    <value>Example: 14:30</value>
    <comment>Transfer time field placeholder</comment>
  </data>
  <data name="Exit" xml:space="preserve">
    <value>Exit</value>
    <comment>Exit button text</comment>
  </data>
  <data name="Expired" xml:space="preserve">
    <value>Expired</value>
    <comment>Label for expired status</comment>
  </data>
  <data name="Expiry Date" xml:space="preserve">
    <value>Expiry Date</value>
    <comment>Label for expiry date</comment>
  </data>
  <data name="Explore Our Solutions" xml:space="preserve">
    <value>Explore Our Solutions</value>
  </data>
  <data name="Export CSV" xml:space="preserve">
    <value>Export CSV</value>
    <comment>Export to CSV button text</comment>
  </data>
  <data name="Export Excel" xml:space="preserve">
    <value>Export Excel</value>
    <comment>Export to Excel button text</comment>
  </data>
  <data name="Extra Data" xml:space="preserve">
    <value>Extra Data</value>
    <comment>Extra data label</comment>
  </data>
  <data name="Failed" xml:space="preserve">
    <value>Failed</value>
    <comment>Failed status</comment>
  </data>
  <data name="Failed to copy" xml:space="preserve">
    <value>Failed to copy</value>
    <comment>Error message when copying to clipboard fails</comment>
  </data>
  <data name="Failed to copy text" xml:space="preserve">
    <value>Failed to copy text</value>
    <comment>Error message when copying text fails</comment>
  </data>
  <data name="Failed to reverse deposit" xml:space="preserve">
    <value>Failed to reverse deposit</value>
    <comment>Error message for failed deposit reversal</comment>
  </data>
  <data name="Failed to update payment status" xml:space="preserve">
    <value>Failed to update payment status</value>
    <comment>Error message when payment status update fails</comment>
  </data>
  <data name="Failed to update withdrawal status" xml:space="preserve">
    <value>Failed to update withdrawal status</value>
    <comment>Error message when withdrawal status update fails</comment>
  </data>
  <data name="Failure Count" xml:space="preserve">
    <value>Failure Count</value>
    <comment>Number of failed executions of background service</comment>
  </data>
  <data name="Fallback copy error" xml:space="preserve">
    <value>Fallback copy error</value>
    <comment>Error message when fallback copy method fails</comment>
  </data>
  <data name="Fallback copy failed" xml:space="preserve">
    <value>Fallback copy failed</value>
    <comment>Error message when fallback copy method fails</comment>
  </data>
  <data name="Fast Withdrawals" xml:space="preserve">
    <value>FAST WITHDRAWALS</value>
    <comment>Fast withdrawals text</comment>
  </data>
  <data name="Features" xml:space="preserve">
    <value>Features</value>
  </data>
  <data name="Fees Text" xml:space="preserve">
    <value>Certain fees are charged for transactions performed on the platform. You can access current fee information on our website.</value>
    <comment>Text for fees section in user agreement</comment>
  </data>
  <data name="Fees Title" xml:space="preserve">
    <value>Fees</value>
    <comment>Title for fees section in user agreement</comment>
  </data>
  <data name="Fiat Currency Deposit Methods" xml:space="preserve">
    <value>Fiat Currency Deposit Methods</value>
    <comment>Header for fiat currency deposit methods section</comment>
  </data>
  <data name="Fiat Deposits" xml:space="preserve">
    <value>Fiat Deposits</value>
    <comment>Label for fiat currency deposits section</comment>
  </data>
  <data name="File Width" xml:space="preserve">
    <value>File Width</value>
  </data>
  <data name="Files Count" xml:space="preserve">
    <value>Files Count</value>
    <comment>Files Count</comment>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>Filter</value>
    <comment>Filter button text</comment>
  </data>
  <data name="Filtered from _MAX_ total entries" xml:space="preserve">
    <value>Filtered from _MAX_ total entries</value>
    <comment>DataTables info filtered text</comment>
  </data>
  <data name="Filtered Results" xml:space="preserve">
    <value>Filtered Results</value>
    <comment>Number of filtered results</comment>
  </data>
  <data name="Final Amount" xml:space="preserve">
    <value>Final Amount</value>
    <comment>Label for final amount after interest</comment>
  </data>
  <data name="First" xml:space="preserve">
    <value>First</value>
    <comment>First page button text</comment>
  </data>
  <data name="First Deposit Bonus" xml:space="preserve">
    <value>FIRST DEPOSIT BONUS!</value>
    <comment>First deposit bonus title</comment>
  </data>
  <data name="First Name" xml:space="preserve">
    <value>First Name</value>
    <comment>First name field label</comment>
  </data>
  <data name="Flexible terms and withdrawal options" xml:space="preserve">
    <value>Flexible terms and withdrawal options</value>
    <comment>Benefit 3</comment>
  </data>
  <data name="For each referrer with an active package, the system will calculate a reward based on the package's reward percentage." xml:space="preserve">
    <value>For each referrer with an active package, the system will calculate a reward based on the package's reward percentage.</value>
    <comment>Instruction for reward distribution</comment>
  </data>
  <data name="From Date" xml:space="preserve">
    <value>From Date</value>
    <comment>From date filter label</comment>
  </data>
  <data name="Full Name" xml:space="preserve">
    <value>Full Name</value>
    <comment>Full name label</comment>
  </data>
  <data name="Full Name Required" xml:space="preserve">
    <value>Full name is required</value>
    <comment>Full name validation message</comment>
  </data>
  <data name="Future Investment Begins" xml:space="preserve">
    <value>Future Investment Begins</value>
    <comment>Title for the second slider</comment>
  </data>
  <data name="General Increase" xml:space="preserve">
    <value>General Increase</value>
    <comment>Label for general increase in statistics</comment>
  </data>
  <data name="Get More Information" xml:space="preserve">
    <value>Get More Information</value>
    <comment>Button text for getting more information</comment>
  </data>
  <data name="Go to Home Page" xml:space="preserve">
    <value>Go to Home Page</value>
    <comment>Link text for going to home page</comment>
  </data>
  <data name="Go To Papara" xml:space="preserve">
    <value>Go to Papara</value>
    <comment>Go to Papara button text</comment>
  </data>
  <data name="Group" xml:space="preserve">
    <value>Group</value>
    <comment>Label for group field</comment>
  </data>
  <data name="Happy Customers" xml:space="preserve">
    <value>Happy Customers</value>
  </data>
  <data name="Heat Map" xml:space="preserve">
    <value>Heat Map</value>
    <comment>Heat Map page title</comment>
  </data>
  <data name="Home" xml:space="preserve">
    <value>Home</value>
    <comment>Home link text</comment>
  </data>
  <data name="I forgot my password" xml:space="preserve">
    <value>I forgot my password</value>
    <comment>Link text for password recovery</comment>
  </data>
  <data name="I Made The Deposit" xml:space="preserve">
    <value>I Have Made The Deposit</value>
    <comment>Deposit completion button text</comment>
  </data>
  <data name="IBAN" xml:space="preserve">
    <value>IBAN</value>
    <comment>Label for IBAN field</comment>
  </data>
  <data name="IBAN cannot exceed 50 characters" xml:space="preserve">
    <value>IBAN cannot exceed 50 characters</value>
    <comment>Validation message for IBAN length</comment>
  </data>
  <data name="IBAN Too Long" xml:space="preserve">
    <value>IBAN cannot exceed 50 characters</value>
    <comment>Validation message for IBAN length</comment>
  </data>
  <data name="IBAN will be automatically formatted" xml:space="preserve">
    <value>IBAN will be automatically formatted</value>
    <comment>Help text for IBAN input</comment>
  </data>
  <data name="Icon" xml:space="preserve">
    <value>Icon</value>
    <comment>Label for icon field</comment>
  </data>
  <data name="Icon Image" xml:space="preserve">
    <value>Icon Image</value>
    <comment>Label for icon image field</comment>
  </data>
  <data name="Identity already exists" xml:space="preserve">
    <value>Identity already exists</value>
    <comment>Error message when identity already exists</comment>
  </data>
  <data name="Identity Number" xml:space="preserve">
    <value>Identity Number</value>
    <comment>Identity number field label</comment>
  </data>
  <data name="If checked, a balance transaction will be recorded for this purchase." xml:space="preserve">
    <value>If checked, a balance transaction will be recorded for this purchase.</value>
    <comment>Help text for balance transaction checkbox</comment>
  </data>
  <data name="If checked, a balance transaction will be recorded for this purchase. Note: Referral rewards for package purchases are disabled." xml:space="preserve">
    <value>If checked, a balance transaction will be recorded for this purchase. Note: Referral rewards for package purchases are disabled.</value>
    <comment>Help text for balance transaction checkbox</comment>
  </data>
  <data name="If checked, referral rewards will be processed for this purchase" xml:space="preserve">
    <value>If checked, referral rewards will be processed for this purchase</value>
    <comment>Label for if checked, referral rewards will be processed for this purchase</comment>
  </data>
  <data name="If this withdrawal was approved deleting it will not refund the users balance" xml:space="preserve">
    <value>If this withdrawal was approved, deleting it will not refund the user's balance</value>
    <comment>Warning message when deleting an approved withdrawal</comment>
  </data>
  <data name="If this withdrawal was approved, deleting it will not refund the user's balance" xml:space="preserve">
    <value>If this withdrawal was approved, deleting it will not refund the user's balance</value>
    <comment>Warning message when deleting an approved withdrawal</comment>
  </data>
  <data name="If you believe this is an error, please contact the site administrator" xml:space="preserve">
    <value>If you believe this is an error, please contact the site administrator</value>
    <comment>Message for users encountering errors</comment>
  </data>
  <data name="Important" xml:space="preserve">
    <value>IMPORTANT</value>
    <comment>Important notice header</comment>
  </data>
  <data name="Important Note" xml:space="preserve">
    <value>Important Note</value>
    <comment>Important note label</comment>
  </data>
  <data name="Important!" xml:space="preserve">
    <value>Important!</value>
    <comment>Important notice title</comment>
  </data>
  <data name="Inactive" xml:space="preserve">
    <value>Inactive</value>
    <comment>Inactive status</comment>
  </data>
  <data name="Increased Visibility" xml:space="preserve">
    <value>Increased Visibility</value>
  </data>
  <data name="Initial Amount" xml:space="preserve">
    <value>Initial Amount</value>
    <comment>Label for initial investment amount</comment>
  </data>
  <data name="Instant Account Credit" xml:space="preserve">
    <value>Instant Account Credit</value>
    <comment>Instant account credit text</comment>
  </data>
  <data name="Insufficient Balance" xml:space="preserve">
    <value>Insufficient Balance</value>
    <comment>Button text when user has insufficient balance</comment>
  </data>
  <data name="Insufficient balance for this withdrawal" xml:space="preserve">
    <value>Insufficient balance for this withdrawal</value>
    <comment>Error message for insufficient balance</comment>
  </data>
  <data name="Insufficient Balance For Withdrawal" xml:space="preserve">
    <value>Insufficient balance for this withdrawal. Your available limit is {0} {1}</value>
    <comment>Error message when user tries to withdraw more than available</comment>
  </data>
  <data name="Insufficient balance to purchase this package" xml:space="preserve">
    <value>Insufficient balance to purchase this package</value>
    <comment>Error message for insufficient balance</comment>
  </data>
  <data name="Insufficient cryptocurrency balance for reversal" xml:space="preserve">
    <value>Insufficient cryptocurrency balance for reversal</value>
    <comment>Error message when user doesn't have enough balance for a reversal</comment>
  </data>
  <data name="Interest Amount" xml:space="preserve">
    <value>Interest Amount</value>
    <comment>Interest amount column header</comment>
  </data>
  <data name="Interest History" xml:space="preserve">
    <value>Interest History</value>
    <comment>Interest payment history</comment>
  </data>
  <data name="Interest Payment History" xml:space="preserve">
    <value>Interest Payment History</value>
    <comment>Interest payment history section title</comment>
  </data>
  <data name="Interest Preview" xml:space="preserve">
    <value>Interest Preview</value>
    <comment>Section title for interest calculation preview</comment>
  </data>
  <data name="Interest Rate" xml:space="preserve">
    <value>Interest Rate</value>
    <comment>Interest rate label</comment>
  </data>
  <data name="Invalid cryptocurrency data format" xml:space="preserve">
    <value>Invalid cryptocurrency data format</value>
    <comment>Error message for invalid cryptocurrency data format</comment>
  </data>
  <data name="Invalid cryptocurrency type" xml:space="preserve">
    <value>Invalid cryptocurrency type</value>
    <comment>Error message for invalid cryptocurrency type</comment>
  </data>
  <data name="Invalid login attempt" xml:space="preserve">
    <value>Invalid login attempt</value>
    <comment>Error message for invalid login attempt</comment>
  </data>
  <data name="Invalid referral code" xml:space="preserve">
    <value>Invalid referral code</value>
    <comment>Error message for invalid referral code</comment>
  </data>
  <data name="Invest with RazeWin" xml:space="preserve">
    <value>Invest with RazeWin</value>
    <comment>Description for live price stream section</comment>
  </data>
  <data name="Investment Amount" xml:space="preserve">
    <value>Investment Amount</value>
    <comment>Section title for amount input</comment>
  </data>
  <data name="Invite Limit" xml:space="preserve">
    <value>Invite Limit</value>
    <comment>Label for invite limit</comment>
  </data>
  <data name="Invite Limit Information" xml:space="preserve">
    <value>Invite Limit Information</value>
    <comment>Section title for invite limit information</comment>
  </data>
  <data name="Invite Usage" xml:space="preserve">
    <value>Invite Usage</value>
    <comment>Label for invite usage progress bar</comment>
  </data>
  <data name="Is Active" xml:space="preserve">
    <value>Is Active</value>
    <comment>Label for active status field</comment>
  </data>
  <data name="Is API" xml:space="preserve">
    <value>Is API</value>
    <comment>Label for API status field</comment>
  </data>
  <data name="Jet Speed Withdrawal" xml:space="preserve">
    <value>JET SPEED WITHDRAWAL!</value>
    <comment>Jet speed withdrawal title</comment>
  </data>
  <data name="Join Now" xml:space="preserve">
    <value>Join Now</value>
    <comment>Join now button</comment>
  </data>
  <data name="Join Our Growing Community" xml:space="preserve">
    <value>Join Our Growing Community</value>
    <comment>Community section title</comment>
  </data>
  <data name="Join our referral program and start earning rewards by inviting others to RazeWin." xml:space="preserve">
    <value>Join our referral program and start earning rewards by inviting others to RazeWin.</value>
    <comment>Package page description</comment>
  </data>
  <data name="JSON Format" xml:space="preserve">
    <value>JSON Format</value>
    <comment>Label for JSON format</comment>
  </data>
  <data name="Key" xml:space="preserve">
    <value>Key</value>
    <comment>Label for key field</comment>
  </data>
  <data name="Last" xml:space="preserve">
    <value>Last</value>
    <comment>Last page button text</comment>
  </data>
  <data name="Last {0} transactions" xml:space="preserve">
    <value>Last {0} transactions</value>
    <comment>Parameterized message for showing last N transactions</comment>
  </data>
  <data name="Last Error" xml:space="preserve">
    <value>Last Error</value>
    <comment>Last error message from background service</comment>
  </data>
  <data name="Last Name" xml:space="preserve">
    <value>Last Name</value>
    <comment>Last name field label</comment>
  </data>
  <data name="Last Online Date" xml:space="preserve">
    <value>Last Online Date</value>
    <comment>Label for last online date field</comment>
  </data>
  <data name="Last Price Update" xml:space="preserve">
    <value>Last Price Update</value>
    <comment>Last price update column header</comment>
  </data>
  <data name="Last Success" xml:space="preserve">
    <value>Last Success</value>
    <comment>Last successful run time of background service</comment>
  </data>
  <data name="Last updated" xml:space="preserve">
    <value>Last updated</value>
    <comment>Label for last updated field</comment>
  </data>
  <data name="Learn more about our company" xml:space="preserve">
    <value>Learn more about our company</value>
    <comment>About page subtitle</comment>
  </data>
  <data name="Leave empty for unlimited" xml:space="preserve">
    <value>Leave empty for unlimited</value>
    <comment>Label for leave empty for unlimited</comment>
  </data>
  <data name="Legal Compliance Text" xml:space="preserve">
    <value>Users are obligated to comply with all local laws and regulations in cryptocurrency transactions. RAZEWIN reserves the right to provide information to legal authorities when necessary.</value>
    <comment>Text for legal compliance section in user agreement</comment>
  </data>
  <data name="Legal Compliance Title" xml:space="preserve">
    <value>Legal Compliance</value>
    <comment>Title for legal compliance section in user agreement</comment>
  </data>
  <data name="Lend your crypto assets to earn interest while helping others." xml:space="preserve">
    <value>Lend your crypto assets to earn interest while helping others.</value>
    <comment>Lending description</comment>
  </data>
  <data name="Lend your RZW tokens to earn interest while helping others." xml:space="preserve">
    <value>Lend your RZW tokens to earn interest while helping others.</value>
    <comment>Lending description for RZW</comment>
  </data>
  <data name="Lending" xml:space="preserve">
    <value>Lending</value>
    <comment>Lending title</comment>
  </data>
  <data name="Level" xml:space="preserve">
    <value>Level</value>
    <comment>Label for level</comment>
  </data>
  <data name="Level {0} percentage ({1}%) cannot be greater than Level {2} percentage ({3}%)" xml:space="preserve">
    <value>Level {0} percentage ({1}%) cannot be greater than Level {2} percentage ({3}%)</value>
    <comment>Error message for package reward percentage validation</comment>
  </data>
  <data name="Level 1 (Direct Referral)" xml:space="preserve">
    <value>Level 1 (Direct Referral)</value>
    <comment>Label for level 1 (direct referral)</comment>
  </data>
  <data name="Level 1 (Direct)" xml:space="preserve">
    <value>Level 1 (Direct)</value>
    <comment>Label for level 1 (direct referral)</comment>
  </data>
  <data name="Level 10" xml:space="preserve">
    <value>Level 10</value>
    <comment>Level 10 for package reward percentages</comment>
  </data>
  <data name="Level 2 (Indirect Referral)" xml:space="preserve">
    <value>Level 2 (Indirect Referral)</value>
    <comment>Label for level 2 (indirect referral)</comment>
  </data>
  <data name="Level 2 (Indirect)" xml:space="preserve">
    <value>Level 2 (Indirect)</value>
    <comment>Label for level 2 (indirect referral)</comment>
  </data>
  <data name="Level 3" xml:space="preserve">
    <value>Level 3</value>
    <comment>Level 3 for package reward percentages</comment>
  </data>
  <data name="Level 4" xml:space="preserve">
    <value>Level 4</value>
    <comment>Level 4 for package reward percentages</comment>
  </data>
  <data name="Level 5" xml:space="preserve">
    <value>Level 5</value>
    <comment>Level 5 for package reward percentages</comment>
  </data>
  <data name="Level 6" xml:space="preserve">
    <value>Level 6</value>
    <comment>Level 6 for package reward percentages</comment>
  </data>
  <data name="Level 7" xml:space="preserve">
    <value>Level 7</value>
    <comment>Level 7 for package reward percentages</comment>
  </data>
  <data name="Level 8" xml:space="preserve">
    <value>Level 8</value>
    <comment>Level 8 for package reward percentages</comment>
  </data>
  <data name="Level 9" xml:space="preserve">
    <value>Level 9</value>
    <comment>Level 9 for package reward percentages</comment>
  </data>
  <data name="Live Price Stream" xml:space="preserve">
    <value>Live Price Stream</value>
    <comment>Title for live price stream section</comment>
  </data>
  <data name="Live Support" xml:space="preserve">
    <value>Live Support</value>
    <comment>Live support link text</comment>
  </data>
  <data name="Loading..." xml:space="preserve">
    <value>Loading...</value>
    <comment>Text shown during loading operations</comment>
  </data>
  <data name="Locked Balance" xml:space="preserve">
    <value>Locked Balance</value>
    <comment>Locked balance label</comment>
  </data>
  <data name="Locked In Savings" xml:space="preserve">
    <value>Locked In Savings</value>
    <comment>Amount locked in savings accounts</comment>
  </data>
  <data name="Log in" xml:space="preserve">
    <value>Log in</value>
    <comment>Log in button text</comment>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Login</value>
    <comment>Login button/link text</comment>
  </data>
  <data name="Login As Selected User" xml:space="preserve">
    <value>Login As Selected User</value>
    <comment>Button text for logging in as selected user</comment>
  </data>
  <data name="Login As User" xml:space="preserve">
    <value>Login As User</value>
    <comment>Title for the Login As User page</comment>
  </data>
  <data name="Login Now" xml:space="preserve">
    <value>Login Now!</value>
    <comment>Login now title</comment>
  </data>
  <data name="Login to My Account" xml:space="preserve">
    <value>Login to My Account</value>
    <comment>Login button text</comment>
  </data>
  <data name="Login To Papara Account To Make Deposit" xml:space="preserve">
    <value>You can make your deposit to our current Papara Account shown on the screen by logging into your Papara Account</value>
    <comment>Papara deposit instructions</comment>
  </data>
  <data name="Login to Purchase" xml:space="preserve">
    <value>Login to Purchase</value>
    <comment>Button text for non-logged in users</comment>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>Logout</value>
    <comment>Logout button text</comment>
  </data>
  <data name="Lower Package" xml:space="preserve">
    <value>Lower Package</value>
    <comment>Used for packages lower than the user's current package</comment>
  </data>
  <data name="Main Agreement" xml:space="preserve">
    <value>Main Agreement</value>
    <comment>Main agreement title</comment>
  </data>
  <data name="Main Agreement Read" xml:space="preserve">
    <value>Main Agreement Read</value>
    <comment>Main agreement checkbox label</comment>
  </data>
  <data name="Make Deposit Via Bank Transfer" xml:space="preserve">
    <value>You can make your deposit by making a bank transfer/EFT to our bank accounts</value>
    <comment>Bank transfer deposit instructions</comment>
  </data>
  <data name="Make Deposit With" xml:space="preserve">
    <value>make a deposit with</value>
    <comment>Text for deposit method</comment>
  </data>
  <data name="Management Panel" xml:space="preserve">
    <value>Management Panel</value>
    <comment>Management panel title</comment>
  </data>
  <data name="Market" xml:space="preserve">
    <value>Market</value>
    <comment>Label for market field</comment>
  </data>
  <data name="Market deletion is disabled" xml:space="preserve">
    <value>Market deletion is disabled</value>
    <comment>Message when trying to delete a market</comment>
  </data>
  <data name="Markets" xml:space="preserve">
    <value>Markets</value>
    <comment>Markets page title</comment>
  </data>
  <data name="Matured" xml:space="preserve">
    <value>Matured</value>
    <comment>Status when savings account has matured</comment>
  </data>
  <data name="Maturity Date" xml:space="preserve">
    <value>Maturity Date</value>
    <comment>Savings account maturity date</comment>
  </data>
  <data name="Max" xml:space="preserve">
    <value>Max</value>
    <comment>Maximum amount label</comment>
  </data>
  <data name="Maximum Buy" xml:space="preserve">
    <value>Maximum Buy</value>
    <comment>Label for maximum buy amount field</comment>
  </data>
  <data name="Maximum Sell" xml:space="preserve">
    <value>Maximum Sell</value>
    <comment>Label for maximum sell amount field</comment>
  </data>
  <data name="Meet Our Global User Network!" xml:space="preserve">
    <value>Meet Our Global User Network!</value>
    <comment>Global user network text</comment>
  </data>
  <data name="Member Since" xml:space="preserve">
    <value>Member Since</value>
    <comment>Label for member registration date</comment>
  </data>
  <data name="Min" xml:space="preserve">
    <value>Min</value>
    <comment>Minimum amount label</comment>
  </data>
  <data name="Minimum Buy" xml:space="preserve">
    <value>Minimum Buy</value>
    <comment>Label for minimum buy amount field</comment>
  </data>
  <data name="Minimum Sell" xml:space="preserve">
    <value>Minimum Sell</value>
    <comment>Label for minimum sell amount field</comment>
  </data>
  <data name="Missing cryptocurrency information in payment data" xml:space="preserve">
    <value>Missing cryptocurrency information in payment data</value>
    <comment>Error message for missing cryptocurrency information</comment>
  </data>
  <data name="Mission" xml:space="preserve">
    <value>Mission</value>
    <comment>Mission title</comment>
  </data>
  <data name="Mission &amp; Vision" xml:space="preserve">
    <value>Mission &amp; Vision</value>
    <comment>Label for mission and vision section</comment>
  </data>
  <data name="Modified Date" xml:space="preserve">
    <value>Modified Date</value>
    <comment>Label for modified date field</comment>
  </data>
  <data name="Monthly Breakdown" xml:space="preserve">
    <value>Monthly Breakdown</value>
    <comment>Monthly breakdown section title</comment>
  </data>
  <data name="More Info" xml:space="preserve">
    <value>More Info</value>
    <comment>More info link text</comment>
  </data>
  <data name="more users with your current package." xml:space="preserve">
    <value>more users with your current package.</value>
    <comment>Part of message showing remaining invites</comment>
  </data>
  <data name="Multiple ways to grow your digital assets with RazeWin" xml:space="preserve">
    <value>Multiple ways to grow your digital assets with RazeWin</value>
    <comment>Earn money section description</comment>
  </data>
  <data name="My Account" xml:space="preserve">
    <value>My Account</value>
    <comment>Label for my account section</comment>
  </data>
  <data name="My Balances" xml:space="preserve">
    <value>My Balances</value>
    <comment>Header for user balances section</comment>
  </data>
  <data name="My Packages" xml:space="preserve">
    <value>My Packages</value>
    <comment>Navigation menu item</comment>
  </data>
  <data name="My Payment" xml:space="preserve">
    <value>My Payment</value>
    <comment>My Payment page title</comment>
  </data>
  <data name="My Profile" xml:space="preserve">
    <value>My Profile</value>
    <comment>My Profile page title</comment>
  </data>
  <data name="My Referral Code" xml:space="preserve">
    <value>My Referral Code</value>
    <comment>Title for user's personal referral code section</comment>
  </data>
  <data name="My Savings" xml:space="preserve">
    <value>My Savings</value>
    <comment>My savings page title</comment>
  </data>
  <data name="My Trade History" xml:space="preserve">
    <value>My Trade History</value>
    <comment>My Trade History page title</comment>
  </data>
  <data name="My Wallet" xml:space="preserve">
    <value>My Wallet</value>
    <comment>My Wallet page title</comment>
  </data>
  <data name="My Withdrawals" xml:space="preserve">
    <value>My Withdrawals</value>
    <comment>My Withdrawals page title</comment>
  </data>
  <data name="N/A" xml:space="preserve">
    <value>N/A</value>
    <comment>Not applicable label</comment>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Name</value>
    <comment>Label for name field</comment>
  </data>
  <data name="New" xml:space="preserve">
    <value>New</value>
    <comment>Label for new items</comment>
  </data>
  <data name="New Balance" xml:space="preserve">
    <value>New Balance</value>
    <comment>New balance label</comment>
  </data>
  <data name="New Balance Transaction" xml:space="preserve">
    <value>New Balance Transaction</value>
    <comment>Title for creating a new balance transaction</comment>
  </data>
  <data name="New Password" xml:space="preserve">
    <value>New Password</value>
    <comment>New password field label</comment>
  </data>
  <data name="New Wallet Balance" xml:space="preserve">
    <value>New Wallet Balance</value>
    <comment>New wallet balance label</comment>
  </data>
  <data name="Next" xml:space="preserve">
    <value>Next</value>
    <comment>Next page button text</comment>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
    <comment>No text</comment>
  </data>
  <data name="No additional data" xml:space="preserve">
    <value>No additional data</value>
    <comment>Default text for empty additional data</comment>
  </data>
  <data name="No available balance for new savings" xml:space="preserve">
    <value>No available balance for new savings</value>
    <comment>Message when user has no available balance</comment>
  </data>
  <data name="No background services found" xml:space="preserve">
    <value>No background services found</value>
    <comment>Message when no background services are found</comment>
  </data>
  <data name="No data available in table" xml:space="preserve">
    <value>No data available in table</value>
    <comment>DataTables empty table text</comment>
  </data>
  <data name="No entries available" xml:space="preserve">
    <value>No entries available</value>
    <comment>DataTable empty info text</comment>
  </data>
  <data name="No Expiry" xml:space="preserve">
    <value>No Expiry</value>
    <comment>Label for no expiry</comment>
  </data>
  <data name="No hidden fees" xml:space="preserve">
    <value>No hidden fees</value>
    <comment>Benefit 5</comment>
  </data>
  <data name="No interest payments found" xml:space="preserve">
    <value>No interest payments found</value>
    <comment>Message when no payments found</comment>
  </data>
  <data name="No interest payments yet. Interest will be paid daily starting from tomorrow." xml:space="preserve">
    <value>No interest payments yet. Interest will be paid daily starting from tomorrow.</value>
    <comment>Message when no interest payments exist</comment>
  </data>
  <data name="No limits, no waiting at Razewin! Experience the difference with jet-speed deposit and withdrawal operations, including weekends." xml:space="preserve">
    <value>No limits, no waiting at Razewin! Experience the difference with jet-speed deposit and withdrawal operations, including weekends.</value>
    <comment>Fast withdrawals slider description</comment>
  </data>
  <data name="No matching records found" xml:space="preserve">
    <value>No matching records found</value>
    <comment>No matching records found message</comment>
  </data>
  <data name="No Package" xml:space="preserve">
    <value>No Package</value>
    <comment>Used when user has no active package</comment>
  </data>
  <data name="No packages have been purchased after this deposit was approved" xml:space="preserve">
    <value>No packages have been purchased after this deposit was approved</value>
    <comment>Status message for package purchase check</comment>
  </data>
  <data name="No pairs found" xml:space="preserve">
    <value>No pairs found</value>
    <comment>Message when no trading pairs are found</comment>
  </data>
  <data name="No records available" xml:space="preserve">
    <value>No records available</value>
    <comment>No records available message</comment>
  </data>
  <data name="No records found" xml:space="preserve">
    <value>No records found</value>
    <comment>DataTables zero records text</comment>
  </data>
  <data name="No results found" xml:space="preserve">
    <value>No results found</value>
    <comment>Text shown when no results are found in Select2 dropdown</comment>
  </data>
  <data name="No Rewards" xml:space="preserve">
    <value>No Rewards</value>
    <comment>Status label for no rewards</comment>
  </data>
  <data name="No Rewards Distributed" xml:space="preserve">
    <value>No Rewards Distributed</value>
    <comment>Title for no rewards distributed section</comment>
  </data>
  <data name="No rewards have been distributed yet" xml:space="preserve">
    <value>No rewards have been distributed yet</value>
    <comment>Status message for reward distribution check</comment>
  </data>
  <data name="No Rewards to Distribute" xml:space="preserve">
    <value>No Rewards to Distribute</value>
    <comment>Title for the message when there are no rewards to distribute</comment>
  </data>
  <data name="No rewards were distributed for this payment. This could be because:" xml:space="preserve">
    <value>No rewards were distributed for this payment. This could be because:</value>
    <comment>Explanation for no rewards distributed</comment>
  </data>
  <data name="No rewards were distributed. There may be no eligible users in the referral chain." xml:space="preserve">
    <value>No rewards were distributed. There may be no eligible users in the referral chain.</value>
    <comment>Message for no rewards distributed</comment>
  </data>
  <data name="No trades found" xml:space="preserve">
    <value>No trades found</value>
    <comment>Message when no trades are found</comment>
  </data>
  <data name="No trades have been made after this deposit was approved" xml:space="preserve">
    <value>No trades have been made after this deposit was approved</value>
    <comment>Status message for trade activity check</comment>
  </data>
  <data name="No transactions found" xml:space="preserve">
    <value>No transactions found</value>
    <comment>Message when no transactions are found</comment>
  </data>
  <data name="No wallet balances found" xml:space="preserve">
    <value>No wallet balances found</value>
    <comment>Message when user has no wallet balances</comment>
  </data>
  <data name="No withdrawals have been made after this deposit was approved" xml:space="preserve">
    <value>No withdrawals have been made after this deposit was approved</value>
    <comment>Status message for withdrawal activity check</comment>
  </data>
  <data name="None" xml:space="preserve">
    <value>None</value>
    <comment>Used in referral tree page</comment>
  </data>
  <data name="Not Available" xml:space="preserve">
    <value>Not Available</value>
    <comment>Text shown when a package is not available for purchase (e.g., lower than current package)</comment>
  </data>
  <data name="Not Eligible for Reversal" xml:space="preserve">
    <value>Not Eligible for Reversal</value>
    <comment>Status text for deposits not eligible for reversal</comment>
  </data>
  <data name="Not specified" xml:space="preserve">
    <value>Not specified</value>
    <comment>Default text for unspecified values</comment>
  </data>
  <data name="Not Started" xml:space="preserve">
    <value>Not Started</value>
    <comment>Not started status</comment>
  </data>
  <data name="Note When you approve a cryptocurrency deposit the amount will be added to the users cryptocurrency wallet automatically" xml:space="preserve">
    <value>Note: When you approve a cryptocurrency deposit, the amount will be added to the user's cryptocurrency wallet automatically.</value>
    <comment>Note for admin when approving cryptocurrency deposits</comment>
  </data>
  <data name="Note When you approve a payment the amount will be added to the users balance automatically" xml:space="preserve">
    <value>Note: When you approve a payment, the amount will be added to the user's balance automatically.</value>
    <comment>Note for admin when approving payments</comment>
  </data>
  <data name="Note When you approve a withdrawal the amount will be deducted from the users balance automatically" xml:space="preserve">
    <value>Note: When you approve a withdrawal, the amount will be deducted from the user's balance automatically.</value>
    <comment>Note for admin when approving withdrawals</comment>
  </data>
  <data name="Note: When you approve a cryptocurrency deposit, the amount will be added to the user's cryptocurrency wallet automatically." xml:space="preserve">
    <value>Note: When you approve a cryptocurrency deposit, the amount will be added to the user's cryptocurrency wallet automatically.</value>
    <comment>Note for admin when approving cryptocurrency deposits</comment>
  </data>
  <data name="Note: When you approve a payment, the amount will be added to the user's balance automatically." xml:space="preserve">
    <value>Note: When you approve a payment, the amount will be added to the user's balance automatically.</value>
    <comment>Note for admin when approving payments</comment>
  </data>
  <data name="Note: When you approve a withdrawal, the amount will be deducted from the user's balance automatically." xml:space="preserve">
    <value>Note: When you approve a withdrawal, the amount will be deducted from the user's balance automatically.</value>
    <comment>Note for admin when approving withdrawals</comment>
  </data>
  <data name="of" xml:space="preserve">
    <value>of</value>
    <comment>Of preposition for pagination</comment>
  </data>
  <data name="OK" xml:space="preserve">
    <value>OK</value>
    <comment>OK button text</comment>
  </data>
  <data name="Only approved payments can have rewards distributed." xml:space="preserve">
    <value>Only approved payments can have rewards distributed.</value>
    <comment>Warning message for reward distribution</comment>
  </data>
  <data name="Only Digits For Decimal Part" xml:space="preserve">
    <value>Please enter only digits for decimal part</value>
    <comment>Validation message for decimal part</comment>
  </data>
  <data name="Only send Bitcoin (BTC) to this address. Sending any other cryptocurrency may result in permanent loss." xml:space="preserve">
    <value>Only send Bitcoin (BTC) to this address. Sending any other cryptocurrency may result in permanent loss.</value>
    <comment>Warning for Bitcoin deposit address</comment>
  </data>
  <data name="Only send BNB on the BEP20 (Smart Chain) network to this address. Sending BNB on other networks or other cryptocurrencies may result in permanent loss." xml:space="preserve">
    <value>Only send BNB on the BEP20 (Smart Chain) network to this address. Sending BNB on other networks or other cryptocurrencies may result in permanent loss.</value>
    <comment>Warning for BNB deposit address</comment>
  </data>
  <data name="Only send USDT on the TRC20 network to this address. Sending USDT on other networks or other cryptocurrencies may result in permanent loss." xml:space="preserve">
    <value>Only send USDT on the TRC20 network to this address. Sending USDT on other networks or other cryptocurrencies may result in permanent loss.</value>
    <comment>Warning for USDT deposit address</comment>
  </data>
  <data name="Only your most recently purchased package will remain active" xml:space="preserve">
    <value>Only your most recently purchased package will remain active</value>
    <comment>Message informing users about active package policy</comment>
  </data>
  <data name="Optional" xml:space="preserve">
    <value>Optional</value>
    <comment>Label for optional fields</comment>
  </data>
  <data name="Order" xml:space="preserve">
    <value>Order</value>
    <comment>Label for order field</comment>
  </data>
  <data name="Other" xml:space="preserve">
    <value>Other</value>
    <comment>Transaction type for other transactions</comment>
  </data>
  <data name="Our Company" xml:space="preserve">
    <value>Our Company</value>
    <comment>Company tab title</comment>
  </data>
  <data name="Our Mission &amp; Vision" xml:space="preserve">
    <value>Our Mission &amp; Vision</value>
    <comment>Label for mission and vision section</comment>
  </data>
  <data name="Our Price Providers" xml:space="preserve">
    <value>Our Price Providers</value>
    <comment>Price providers page title</comment>
  </data>
  <data name="Our Team" xml:space="preserve">
    <value>Our Team</value>
    <comment>Team tab title</comment>
  </data>
  <data name="Package" xml:space="preserve">
    <value>Package</value>
    <comment>Label for package</comment>
  </data>
  <data name="Package Bonus" xml:space="preserve">
    <value>Package Bonus</value>
    <comment>Label for package bonus trade type</comment>
  </data>
  <data name="package for" xml:space="preserve">
    <value>package for</value>
    <comment>Confirmation message part 2</comment>
  </data>
  <data name="Package Purchase" xml:space="preserve">
    <value>Package Purchase</value>
    <comment>Payment type for package purchases</comment>
  </data>
  <data name="Package Purchase Check" xml:space="preserve">
    <value>Package Purchase Check</value>
    <comment>Check item for package purchase</comment>
  </data>
  <data name="Package purchased successfully" xml:space="preserve">
    <value>Package purchased successfully</value>
    <comment>Success message after purchase</comment>
  </data>
  <data name="Package purchased successfully. You have received {0} RZW tokens." xml:space="preserve">
    <value>Package purchased successfully. You have received {0} RZW tokens.</value>
    <comment>Success message after package purchase with RZW tokens</comment>
  </data>
  <data name="Package Reward Percentage" xml:space="preserve">
    <value>Package Reward Percentage</value>
    <comment>Label for package reward percentage</comment>
  </data>
  <data name="Package Reward Percentages" xml:space="preserve">
    <value>Package Reward Percentages</value>
    <comment>Title for package reward percentages page</comment>
  </data>
  <data name="Packages" xml:space="preserve">
    <value>Packages</value>
    <comment>Navigation menu item for packages</comment>
  </data>
  <data name="Page" xml:space="preserve">
    <value>Page</value>
    <comment>Page label for pagination</comment>
  </data>
  <data name="Paid" xml:space="preserve">
    <value>Paid</value>
    <comment>Payment status - paid</comment>
  </data>
  <data name="Pair Code" xml:space="preserve">
    <value>Pair Code</value>
    <comment>Market pair code (e.g. BTCTRY)</comment>
  </data>
  <data name="Papara" xml:space="preserve">
    <value>Papara</value>
    <comment>Label for Papara payment method</comment>
  </data>
  <data name="Papara No" xml:space="preserve">
    <value>Papara Number</value>
    <comment>Papara number field label</comment>
  </data>
  <data name="Papara Number Required" xml:space="preserve">
    <value>Papara number is required</value>
    <comment>Papara number validation message</comment>
  </data>
  <data name="Passive" xml:space="preserve">
    <value>Passive</value>
    <comment>Label for passive status</comment>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Password</value>
    <comment>Password field label</comment>
  </data>
  <data name="Password changed successfully" xml:space="preserve">
    <value>Password changed successfully</value>
    <comment>Success message for password change</comment>
  </data>
  <data name="Payment Details" xml:space="preserve">
    <value>Payment Details</value>
    <comment>Title for payment details section</comment>
  </data>
  <data name="Payment History" xml:space="preserve">
    <value>Payment History</value>
    <comment>Label for payment history section</comment>
  </data>
  <data name="Payment ID" xml:space="preserve">
    <value>Payment ID</value>
    <comment>Label for payment ID</comment>
  </data>
  <data name="Payment Information" xml:space="preserve">
    <value>Payment Information</value>
    <comment>Payment information section title</comment>
  </data>
  <data name="Payment Instructions" xml:space="preserve">
    <value>Payment Instructions</value>
    <comment>Label for payment instructions section</comment>
  </data>
  <data name="Payment Methods" xml:space="preserve">
    <value>Payment Methods</value>
    <comment>Payment methods section title</comment>
  </data>
  <data name="Payment request submitted successfully. Your account will be credited once the payment is verified." xml:space="preserve">
    <value>Payment request submitted successfully. Your account will be credited once the payment is verified.</value>
    <comment>Success message for payment submission</comment>
  </data>
  <data name="Payment Reward Summary" xml:space="preserve">
    <value>Payment Reward Summary</value>
    <comment>Title for payment reward summary page</comment>
  </data>
  <data name="Payment Type" xml:space="preserve">
    <value>Payment Type</value>
    <comment>Payment type label</comment>
  </data>
  <data name="Payments" xml:space="preserve">
    <value>Payments</value>
    <comment>Payments menu item</comment>
  </data>
  <data name="Pending" xml:space="preserve">
    <value>Pending</value>
    <comment>Pending status</comment>
  </data>
  <data name="Pending Orders" xml:space="preserve">
    <value>Pending Orders</value>
    <comment>Label for pending orders section</comment>
  </data>
  <data name="Pending Payments" xml:space="preserve">
    <value>Pending Payments</value>
    <comment>Pending payments count on dashboard</comment>
  </data>
  <data name="Pending Withdrawals" xml:space="preserve">
    <value>Pending Withdrawals</value>
    <comment>Pending withdrawals count on dashboard</comment>
  </data>
  <data name="Percentage" xml:space="preserve">
    <value>Percentage</value>
    <comment>Label for percentage</comment>
  </data>
  <data name="Phone" xml:space="preserve">
    <value>Phone</value>
    <comment>Phone label</comment>
  </data>
  <data name="Phone Number" xml:space="preserve">
    <value>Phone Number</value>
    <comment>Phone number label</comment>
  </data>
  <data name="Phone Number Required" xml:space="preserve">
    <value>Phone number is required</value>
    <comment>Phone number validation message</comment>
  </data>
  <data name="Phone Required" xml:space="preserve">
    <value>Phone is required</value>
    <comment>Validation message for phone</comment>
  </data>
  <data name="Plan Name" xml:space="preserve">
    <value>Plan Name</value>
    <comment>Savings plan name</comment>
  </data>
  <data name="Please Do Not Click The Completed Deposit Button Before Making The Deposit" xml:space="preserve">
    <value>Please do not click the "Completed Deposit" button before making the deposit</value>
    <comment>Warning message for deposit pages</comment>
  </data>
  <data name="Please ensure that the bank account details are correct. We are not responsible for transfers to incorrect accounts." xml:space="preserve">
    <value>Please ensure that the bank account details are correct. We are not responsible for transfers to incorrect accounts.</value>
    <comment>Bank account warning message</comment>
  </data>
  <data name="Please enter a valid amount" xml:space="preserve">
    <value>Please enter a valid amount</value>
    <comment>Validation message for amount field</comment>
  </data>
  <data name="Please enter a valid format" xml:space="preserve">
    <value>Please enter a valid format</value>
    <comment>Generic validation message for pattern validation</comment>
  </data>
  <data name="Please enter a valid IBAN" xml:space="preserve">
    <value>Please enter a valid IBAN</value>
    <comment>Validation message for IBAN format</comment>
  </data>
  <data name="Please enter a valid number" xml:space="preserve">
    <value>Please enter a valid number</value>
    <comment>Validation message for numeric fields</comment>
  </data>
  <data name="Please enter a valid number with comma as decimal separator (e.g., 123,45)" xml:space="preserve">
    <value>Please enter a valid number with comma as decimal separator (e.g., 123,45)</value>
    <comment>Validation message for number fields</comment>
  </data>
  <data name="Please enter account holder name" xml:space="preserve">
    <value>Please enter account holder name</value>
    <comment>Validation message for account holder field</comment>
  </data>
  <data name="Please enter IBAN" xml:space="preserve">
    <value>Please enter IBAN</value>
    <comment>Validation message for IBAN field</comment>
  </data>
  <data name="Please enter only digits for decimal part" xml:space="preserve">
    <value>Please enter only digits for decimal part</value>
    <comment>Validation message for decimal part</comment>
  </data>
  <data name="Please enter the reference number or description of your bank transfer." xml:space="preserve">
    <value>Please enter the reference number or description of your bank transfer.</value>
    <comment>Instruction for bank transfer reference field</comment>
  </data>
  <data name="Please Enter Valid Amount" xml:space="preserve">
    <value>Please enter a valid amount</value>
    <comment>Validation message for invalid amount</comment>
  </data>
  <data name="Please login to make Buy/Sell transactions" xml:space="preserve">
    <value>Please login to make Buy/Sell transactions</value>
    <comment>Message shown when unauthenticated user tries to buy/sell</comment>
  </data>
  <data name="Please make sure to include your email address or user ID in the transaction description to help us identify your payment." xml:space="preserve">
    <value>Please make sure to include your email address or user ID in the transaction description to help us identify your payment.</value>
    <comment>Instruction for payment identification</comment>
  </data>
  <data name="Please make sure to send your cryptocurrency to the correct network address. Sending to the wrong network may result in permanent loss of funds." xml:space="preserve">
    <value>Please make sure to send your cryptocurrency to the correct network address. Sending to the wrong network may result in permanent loss of funds.</value>
    <comment>Warning for cryptocurrency deposits</comment>
  </data>
  <data name="Please select a plan to see amount limits" xml:space="preserve">
    <value>Please select a plan to see amount limits</value>
    <comment>Helper text for amount input</comment>
  </data>
  <data name="Please select a user" xml:space="preserve">
    <value>Please select a user</value>
    <comment>Validation message for user selection</comment>
  </data>
  <data name="Please select a valid plan" xml:space="preserve">
    <value>Please select a valid plan</value>
    <comment>Validation message for plan selection</comment>
  </data>
  <data name="Please Select Bank" xml:space="preserve">
    <value>Please select a bank</value>
    <comment>Bank selection placeholder</comment>
  </data>
  <data name="Popular" xml:space="preserve">
    <value>Popular</value>
    <comment>Label for popular package</comment>
  </data>
  <data name="Previous" xml:space="preserve">
    <value>Previous</value>
    <comment>Previous page button text</comment>
  </data>
  <data name="Previous Balance" xml:space="preserve">
    <value>Previous Balance</value>
    <comment>Previous balance column header</comment>
  </data>
  <data name="Previous package deactivated" xml:space="preserve">
    <value>Previous package deactivated</value>
    <comment>Message shown when a previous package is deactivated</comment>
  </data>
  <data name="Previous Wallet Balance" xml:space="preserve">
    <value>Previous Wallet Balance</value>
    <comment>Previous wallet balance label</comment>
  </data>
  <data name="Price" xml:space="preserve">
    <value>Price</value>
    <comment>Needs translation from tr</comment>
  </data>
  <data name="Privacy Acceptance Text" xml:space="preserve">
    <value>By accepting this privacy agreement, you consent to the processing of your personal data as described herein.</value>
    <comment>Text for privacy acceptance in privacy agreement</comment>
  </data>
  <data name="Privacy Agreement" xml:space="preserve">
    <value>Privacy Agreement</value>
    <comment>Label for privacy agreement</comment>
  </data>
  <data name="Privacy Agreement Read" xml:space="preserve">
    <value>Privacy Agreement Read</value>
    <comment>Privacy agreement checkbox label</comment>
  </data>
  <data name="Privacy Agreement Title" xml:space="preserve">
    <value>RAZEWIN Privacy Agreement</value>
    <comment>Title for the privacy agreement</comment>
  </data>
  <data name="Privacy Policy" xml:space="preserve">
    <value>Privacy Policy</value>
    <comment>Privacy Policy page title</comment>
  </data>
  <data name="Process Date" xml:space="preserve">
    <value>Process Date</value>
    <comment>Label for process date</comment>
  </data>
  <data name="Process Referral Rewards" xml:space="preserve">
    <value>Process Referral Rewards</value>
    <comment>Label for process referral rewards</comment>
  </data>
  <data name="Process Status" xml:space="preserve">
    <value>Process Status</value>
    <comment>Process status column header</comment>
  </data>
  <data name="Processed" xml:space="preserve">
    <value>Processed</value>
    <comment>Status label for processed rewards</comment>
  </data>
  <data name="Product Details" xml:space="preserve">
    <value>Product Details</value>
    <comment>Product Details</comment>
  </data>
  <data name="Products" xml:space="preserve">
    <value>Products</value>
  </data>
  <data name="Profile" xml:space="preserve">
    <value>Profile</value>
    <comment>Profile tab title</comment>
  </data>
  <data name="Profile updated successfully" xml:space="preserve">
    <value>Profile updated successfully</value>
    <comment>Success message for profile update</comment>
  </data>
  <data name="Progress" xml:space="preserve">
    <value>Progress</value>
    <comment>Progress label</comment>
  </data>
  <data name="Projected Final Amount" xml:space="preserve">
    <value>Projected Final Amount</value>
    <comment>Label for projected final amount</comment>
  </data>
  <data name="Projected Monthly Earnings" xml:space="preserve">
    <value>Projected Monthly Earnings</value>
    <comment>Projected monthly earnings label</comment>
  </data>
  <data name="Projects" xml:space="preserve">
    <value>Projects</value>
  </data>
  <data name="Promotions" xml:space="preserve">
    <value>Promotions</value>
    <comment>Promotions page title</comment>
  </data>
  <data name="Provide your bank account details." xml:space="preserve">
    <value>Provide your bank account details.</value>
    <comment>Withdrawal step 2</comment>
  </data>
  <data name="Purchase" xml:space="preserve">
    <value>Purchase</value>
    <comment>Purchase noun for trade history</comment>
  </data>
  <data name="Purchase a package to start earning referral rewards." xml:space="preserve">
    <value>Purchase a package to start earning referral rewards.</value>
    <comment>Prompt to purchase package</comment>
  </data>
  <data name="Purchase Date" xml:space="preserve">
    <value>Purchase Date</value>
    <comment>Label for purchase date</comment>
  </data>
  <data name="Purchase Now" xml:space="preserve">
    <value>Purchase Now</value>
    <comment>Button text for package purchase</comment>
  </data>
  <data name="Purchase of {0} package" xml:space="preserve">
    <value>Purchase of {0} package</value>
    <comment>Description for package purchase transaction</comment>
  </data>
  <data name="Purchase of {0} package by admin" xml:space="preserve">
    <value>Purchase of {0} package by admin</value>
    <comment>Description for package purchase transaction by admin</comment>
  </data>
  <data name="Quick Links" xml:space="preserve">
    <value>Quick Links</value>
  </data>
  <data name="Quick Stats" xml:space="preserve">
    <value>Quick Stats</value>
    <comment>Label for quick statistics section</comment>
  </data>
  <data name="Rate" xml:space="preserve">
    <value>Rate</value>
    <comment>Rate column header</comment>
  </data>
  <data name="Rate (TRY)" xml:space="preserve">
    <value>Rate (TRY)</value>
    <comment>Label for TRY rate field</comment>
  </data>
  <data name="RazeWin Fast Withdrawals" xml:space="preserve">
    <value>RAZEWIN FAST WITHDRAWALS</value>
    <comment>Fast withdrawals title</comment>
  </data>
  <data name="RAZEWIN TRAVEL EARNING APP" xml:space="preserve">
    <value>RAZEWIN TRAVEL EARNING APP</value>
    <comment>First slider title</comment>
  </data>
  <data name="Recalculate Rewards" xml:space="preserve">
    <value>Recalculate Rewards</value>
  </data>
  <data name="Recent Trades" xml:space="preserve">
    <value>Recent Trades</value>
    <comment>Recent trades section title</comment>
  </data>
  <data name="Recipient" xml:space="preserve">
    <value>Recipient</value>
    <comment>Recipient label</comment>
  </data>
  <data name="Record Balance Transaction" xml:space="preserve">
    <value>Record Balance Transaction</value>
    <comment>Label for checkbox to record balance transaction</comment>
  </data>
  <data name="Reference ID" xml:space="preserve">
    <value>Reference ID</value>
    <comment>Label for reference ID field</comment>
  </data>
  <data name="Reference Type" xml:space="preserve">
    <value>Reference Type</value>
    <comment>Label for reference type field</comment>
  </data>
  <data name="References" xml:space="preserve">
    <value>References</value>
    <comment>Label for references section</comment>
  </data>
  <data name="Referral" xml:space="preserve">
    <value>Referral</value>
    <comment>Tab title for referral section</comment>
  </data>
  <data name="Referral Code" xml:space="preserve">
    <value>Referral Code</value>
    <comment>Code used for referring new users</comment>
  </data>
  <data name="Referral code copied to clipboard" xml:space="preserve">
    <value>Referral code copied to clipboard</value>
    <comment>Success message when referral code is copied</comment>
  </data>
  <data name="Referral Deposit Reward Percentages" xml:space="preserve">
    <value>Deposit Reward % by Referral Level</value>
    <comment>Text explaining that the percentages shown are rewards earned from deposits made by referred users at different levels</comment>
  </data>
  <data name="Referral Deposit Rewards" xml:space="preserve">
    <value>Yatırım Yönlendirme Ödülleri</value>
    <comment>Needs translation from tr</comment>
  </data>
  <data name="Referral Hierarchy" xml:space="preserve">
    <value>Referral Hierarchy</value>
    <comment>Used in Profile page to show referral tree</comment>
  </data>
  <data name="Referral Link" xml:space="preserve">
    <value>Referral Link</value>
    <comment>Label for the referral link field</comment>
  </data>
  <data name="Referral Packages" xml:space="preserve">
    <value>Referral Packages</value>
    <comment>Used for package display pages</comment>
  </data>
  <data name="Referral Program Text" xml:space="preserve">
    <value>RAZEWIN offers its users the opportunity to invite new users with a referral code. Rewards earned under the referral program are subject to the terms set by the platform.</value>
    <comment>Text for referral program section in user agreement</comment>
  </data>
  <data name="Referral Program Title" xml:space="preserve">
    <value>Referral Program</value>
    <comment>Title for referral program section in user agreement</comment>
  </data>
  <data name="Referral Reward" xml:space="preserve">
    <value>Referral Reward</value>
    <comment>Transaction type for referral rewards</comment>
  </data>
  <data name="Referral Rewards" xml:space="preserve">
    <value>Referral Rewards</value>
    <comment>Text shown in package display for referral reward percentages</comment>
  </data>
  <data name="Referral System" xml:space="preserve">
    <value>Referral System</value>
    <comment>Navigation menu item for referral system</comment>
  </data>
  <data name="Referral Tree" xml:space="preserve">
    <value>Referral Tree</value>
    <comment>Used in referral hierarchy view</comment>
  </data>
  <data name="Referred By" xml:space="preserve">
    <value>Referred By</value>
    <comment>Used in referral tree page</comment>
  </data>
  <data name="Referred Users" xml:space="preserve">
    <value>Referred Users</value>
    <comment>Title for list of users referred by current user</comment>
  </data>
  <data name="Refresh Interval" xml:space="preserve">
    <value>Refresh Interval</value>
    <comment>Label for refresh interval dropdown</comment>
  </data>
  <data name="Refresh Now" xml:space="preserve">
    <value>Refresh Now</value>
    <comment>Button text to refresh background service status</comment>
  </data>
  <data name="Register" xml:space="preserve">
    <value>Register</value>
    <comment>Register page title</comment>
  </data>
  <data name="Register a new membership" xml:space="preserve">
    <value>Register a new membership</value>
    <comment>Label for registration link</comment>
  </data>
  <data name="Registered Users" xml:space="preserve">
    <value>Registered Users</value>
    <comment>Registered users label</comment>
  </data>
  <data name="Registration Date" xml:space="preserve">
    <value>Registration Date</value>
    <comment>Registration date column header</comment>
  </data>
  <data name="Rejected" xml:space="preserve">
    <value>Rejected</value>
    <comment>Rejected status</comment>
  </data>
  <data name="Remember Me" xml:space="preserve">
    <value>Remember Me</value>
    <comment>Label for remember me checkbox</comment>
  </data>
  <data name="Report Your Deposit" xml:space="preserve">
    <value>Report Your Deposit</value>
    <comment>Label for deposit reporting section</comment>
  </data>
  <data name="Retry" xml:space="preserve">
    <value>Retry</value>
    <comment>Button text for retrying failed operations</comment>
  </data>
  <data name="Reversal Eligibility Check" xml:space="preserve">
    <value>Reversal Eligibility Check</value>
    <comment>Title for reversal eligibility check section</comment>
  </data>
  <data name="Reverse Deposit" xml:space="preserve">
    <value>Reverse Deposit</value>
    <comment>Button and page title for reversing a deposit</comment>
  </data>
  <data name="Reward Distribution" xml:space="preserve">
    <value>Reward Distribution</value>
  </data>
  <data name="Reward distribution cannot be undone. Make sure this payment is legitimate before proceeding." xml:space="preserve">
    <value>Reward distribution cannot be undone. Make sure this payment is legitimate before proceeding.</value>
    <comment>Warning for reward distribution</comment>
  </data>
  <data name="Reward Distribution Check" xml:space="preserve">
    <value>Reward Distribution Check</value>
    <comment>Check item for reward distribution</comment>
  </data>
  <data name="Reward Distribution Information" xml:space="preserve">
    <value>Reward Distribution Information</value>
    <comment>Title for reward distribution information section</comment>
  </data>
  <data name="Reward Percentages" xml:space="preserve">
    <value>Reward Percentages</value>
    <comment>Navigation menu item for reward percentages</comment>
  </data>
  <data name="Reward Preview" xml:space="preserve">
    <value>Reward Preview</value>
    <comment>Title for the reward preview section</comment>
  </data>
  <data name="Reward Recalculation" xml:space="preserve">
    <value>Reward Recalculation</value>
  </data>
  <data name="Reward Rzw Amount" xml:space="preserve">
    <value>RZW Reward Amount</value>
  </data>
  <data name="Reward Rzw Percentage" xml:space="preserve">
    <value>RZW Reward Percentage</value>
  </data>
  <data name="Reward Status" xml:space="preserve">
    <value>Reward Status</value>
    <comment>Label for reward status</comment>
  </data>
  <data name="Reward Summary" xml:space="preserve">
    <value>Reward Summary</value>
    <comment>Title for reward summary section</comment>
  </data>
  <data name="Reward Tl Amount" xml:space="preserve">
    <value>TL Reward Amount</value>
  </data>
  <data name="Reward Tl Percentage" xml:space="preserve">
    <value>TL Reward Percentage</value>
  </data>
  <data name="Rewarded Users Count" xml:space="preserve">
    <value>Rewarded Users Count</value>
    <comment>Label for rewarded users count</comment>
  </data>
  <data name="Rewards have already been distributed for this payment." xml:space="preserve">
    <value>Rewards have already been distributed for this payment.</value>
    <comment>Information message for reward distribution</comment>
  </data>
  <data name="Rewards have been distributed successfully. {0} users received a total of {1} RZW." xml:space="preserve">
    <value>Rewards have been distributed successfully. {0} users received a total of {1} RZW.</value>
    <comment>Success message for reward distribution</comment>
  </data>
  <data name="Rewards have been distributed to {0} users" xml:space="preserve">
    <value>Rewards have been distributed to {0} users</value>
    <comment>Status message for reward distribution check with count</comment>
  </data>
  <data name="Rich Investment Variety" xml:space="preserve">
    <value>Rich Investment Variety</value>
    <comment>Label for investment variety section</comment>
  </data>
  <data name="Risk Disclosure" xml:space="preserve">
    <value>Risk Disclosure</value>
    <comment>Label for risk disclosure section</comment>
  </data>
  <data name="Running" xml:space="preserve">
    <value>Running</value>
    <comment>Running status</comment>
  </data>
  <data name="RZW Amount" xml:space="preserve">
    <value>RZW Amount</value>
    <comment>Label for RZW amount input</comment>
  </data>
  <data name="RZW Price at Distribution" xml:space="preserve">
    <value>RZW Price at Distribution</value>
    <comment>Label for RZW price at distribution</comment>
  </data>
  <data name="RZW Savings" xml:space="preserve">
    <value>RZW Savings</value>
    <comment>RZW Savings title</comment>
  </data>
  <data name="RZW token not found in the system" xml:space="preserve">
    <value>RZW token not found in the system</value>
    <comment>Error message when RZW token is not found in the database</comment>
  </data>
  <data name="Sale" xml:space="preserve">
    <value>Sale</value>
    <comment>Sale noun for trade history</comment>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Save</value>
    <comment>Label for save button</comment>
  </data>
  <data name="Save Changes" xml:space="preserve">
    <value>Save Changes</value>
    <comment>Label for save changes button</comment>
  </data>
  <data name="Savings" xml:space="preserve">
    <value>Savings</value>
    <comment>Savings title</comment>
  </data>
  <data name="Savings Account" xml:space="preserve">
    <value>Savings Account</value>
    <comment>Savings account filter label</comment>
  </data>
  <data name="Savings account created successfully! Your RZW tokens have been locked and will start earning interest." xml:space="preserve">
    <value>Savings account created successfully! Your RZW tokens have been locked and will start earning interest.</value>
    <comment>Success message for account creation</comment>
  </data>
  <data name="Savings Account Details" xml:space="preserve">
    <value>Savings Account Details</value>
    <comment>Page title for savings account details</comment>
  </data>
  <data name="Savings account not found or you don't have permission to view it." xml:space="preserve">
    <value>Savings account not found or you don't have permission to view it.</value>
    <comment>Error message for account not found</comment>
  </data>
  <data name="Savings Dashboard" xml:space="preserve">
    <value>Savings Dashboard</value>
    <comment>Savings dashboard title</comment>
  </data>
  <data name="Scan to copy address" xml:space="preserve">
    <value>Scan to copy address</value>
    <comment>Label for QR code scanning</comment>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Search</value>
    <comment>Search label</comment>
  </data>
  <data name="Search by name, email, phone or referral code" xml:space="preserve">
    <value>Search by name, email, phone or referral code</value>
    <comment>Used in referral tree page</comment>
  </data>
  <data name="Search Users" xml:space="preserve">
    <value>Search Users</value>
    <comment>Used in referral tree page</comment>
  </data>
  <data name="Searching..." xml:space="preserve">
    <value>Searching...</value>
    <comment>Text shown when searching in Select2 dropdown</comment>
  </data>
  <data name="seconds" xml:space="preserve">
    <value>seconds</value>
    <comment>Unit for seconds</comment>
  </data>
  <data name="Secure 256-bit TLS-encryption" xml:space="preserve">
    <value>Secure 256-bit TLS-encryption</value>
    <comment>Security information text</comment>
  </data>
  <data name="Secure Fast Innovative" xml:space="preserve">
    <value>Secure, fast, innovative</value>
    <comment>Features for the second slider</comment>
  </data>
  <data name="Secure platform with insurance" xml:space="preserve">
    <value>Secure platform with insurance</value>
    <comment>Benefit 2</comment>
  </data>
  <data name="Security Text" xml:space="preserve">
    <value>You are responsible for your account security. You must keep your password and authentication information secure. If you notice any suspicious activity, you should immediately notify our customer service.</value>
    <comment>Text for security section in user agreement</comment>
  </data>
  <data name="Security Title" xml:space="preserve">
    <value>Security</value>
    <comment>Title for security section in user agreement</comment>
  </data>
  <data name="Select a bank account from the dropdown menu." xml:space="preserve">
    <value>Select a bank account from the dropdown menu.</value>
    <comment>Instruction for bank account selection</comment>
  </data>
  <data name="Select a pair from the API to automatically fill the Pair Code field" xml:space="preserve">
    <value>Select a pair from the API to automatically fill the Pair Code field</value>
    <comment>Instruction for API pair selection</comment>
  </data>
  <data name="Select a plan and enter amount to see interest preview" xml:space="preserve">
    <value>Select a plan and enter amount to see interest preview</value>
    <comment>Placeholder text for interest preview</comment>
  </data>
  <data name="Select a user to login as" xml:space="preserve">
    <value>Select a user to login as</value>
    <comment>Instruction text for selecting a user</comment>
  </data>
  <data name="Select API Service" xml:space="preserve">
    <value>Select API Service</value>
    <comment>Label for API service selection</comment>
  </data>
  <data name="Select Bank" xml:space="preserve">
    <value>Select Bank</value>
    <comment>Bank selection field label</comment>
  </data>
  <data name="Select Bank Account" xml:space="preserve">
    <value>Select Bank Account</value>
    <comment>Label for bank account selection</comment>
  </data>
  <data name="Select Coin" xml:space="preserve">
    <value>Select Coin</value>
    <comment>Label for coin selection</comment>
  </data>
  <data name="Select Language" xml:space="preserve">
    <value>Select Language</value>
    <comment>Label for language selection</comment>
  </data>
  <data name="Select Package" xml:space="preserve">
    <value>Select Package</value>
    <comment>Label for package selection dropdown</comment>
  </data>
  <data name="Select Pair" xml:space="preserve">
    <value>Select Pair</value>
    <comment>Label for pair selection</comment>
  </data>
  <data name="Select Savings Plan" xml:space="preserve">
    <value>Select Savings Plan</value>
    <comment>Section title for plan selection</comment>
  </data>
  <data name="Select User" xml:space="preserve">
    <value>Select User</value>
    <comment>Label for user selection</comment>
  </data>
  <data name="Selected bank not found." xml:space="preserve">
    <value>Selected bank not found.</value>
    <comment>Error message when selected bank is not found</comment>
  </data>
  <data name="Selected cryptocurrency is not available" xml:space="preserve">
    <value>Selected cryptocurrency is not available</value>
    <comment>Error message for unavailable cryptocurrency</comment>
  </data>
  <data name="Selected User" xml:space="preserve">
    <value>Selected User</value>
    <comment>Used in referral tree page</comment>
  </data>
  <data name="Selected user does not have the User role" xml:space="preserve">
    <value>Selected user does not have the User role</value>
    <comment>Error message when selected user does not have the User role</comment>
  </data>
  <data name="Selected user not found or is inactive" xml:space="preserve">
    <value>Selected user not found or is inactive</value>
    <comment>Error message when selected user is not found or inactive</comment>
  </data>
  <data name="Sell" xml:space="preserve">
    <value>Sell</value>
    <comment>Sell button text</comment>
  </data>
  <data name="Sell Coin" xml:space="preserve">
    <value>Sell Coin</value>
    <comment>Label for sell coin button</comment>
  </data>
  <data name="Sell Price" xml:space="preserve">
    <value>Sell Price</value>
    <comment>Sell price column header</comment>
  </data>
  <data name="Send" xml:space="preserve">
    <value>Send</value>
  </data>
  <data name="Send Message" xml:space="preserve">
    <value>Send Message</value>
    <comment>Send message button</comment>
  </data>
  <data name="Sender Address" xml:space="preserve">
    <value>Sender Address</value>
    <comment>Label for sender address field</comment>
  </data>
  <data name="Sender Full Name" xml:space="preserve">
    <value>Sender Full Name</value>
    <comment>Sender full name label</comment>
  </data>
  <data name="Sender Full Name Required" xml:space="preserve">
    <value>Sender full name is required</value>
    <comment>Sender full name validation message</comment>
  </data>
  <data name="Service Changes Text" xml:space="preserve">
    <value>RAZEWIN reserves the right to make changes to its services and this agreement. Important changes will be notified to users.</value>
    <comment>Text for service changes section in user agreement</comment>
  </data>
  <data name="Service Changes Title" xml:space="preserve">
    <value>Service Changes</value>
    <comment>Title for service changes section in user agreement</comment>
  </data>
  <data name="Service Description Text" xml:space="preserve">
    <value>RAZEWIN is a digital asset platform that provides cryptocurrency trading, storage, and investment services to its users.</value>
    <comment>Text for service description section in user agreement</comment>
  </data>
  <data name="Service Description Title" xml:space="preserve">
    <value>Service Description</value>
    <comment>Title for service description section in user agreement</comment>
  </data>
  <data name="Service Name" xml:space="preserve">
    <value>Service Name</value>
    <comment>Background service name column header</comment>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Settings</value>
    <comment>Settings menu item</comment>
  </data>
  <data name="Share this code with friends to refer them to our platform." xml:space="preserve">
    <value>Share this code with friends to refer them to our platform.</value>
    <comment>Help text explaining how to use referral code</comment>
  </data>
  <data name="Share this link with friends to directly register them with your referral code." xml:space="preserve">
    <value>Share this link with friends to directly register them with your referral code.</value>
    <comment>Help text for the referral link field</comment>
  </data>
  <data name="Short Name" xml:space="preserve">
    <value>Short Name</value>
    <comment>Label for short name field</comment>
  </data>
  <data name="Showing _START_ to _END_ of _TOTAL_ entries" xml:space="preserve">
    <value>Showing _START_ to _END_ of _TOTAL_ entries</value>
    <comment>Pagination info text</comment>
  </data>
  <data name="Sign In" xml:space="preserve">
    <value>Sign In</value>
    <comment>Label for sign in button</comment>
  </data>
  <data name="Sign in to start your session" xml:space="preserve">
    <value>Sign in to start your session</value>
    <comment>Label for sign in page</comment>
  </data>
  <data name="Social Media" xml:space="preserve">
    <value>Social Media</value>
  </data>
  <data name="Solutions" xml:space="preserve">
    <value>Solutions</value>
  </data>
  <data name="Staking" xml:space="preserve">
    <value>Staking</value>
    <comment>Staking title</comment>
  </data>
  <data name="Stand out with modern and impressive digital signage solutions" xml:space="preserve">
    <value>Stand out with modern and impressive digital signage solutions</value>
  </data>
  <data name="Start Date" xml:space="preserve">
    <value>Start Date</value>
    <comment>Savings account start date</comment>
  </data>
  <data name="Start earning interest on your RZW tokens with our savings plans." xml:space="preserve">
    <value>Start earning interest on your RZW tokens with our savings plans.</value>
    <comment>Welcome message for new users</comment>
  </data>
  <data name="Start Investing" xml:space="preserve">
    <value>Start Investing!</value>
    <comment>Start investing step title</comment>
  </data>
  <data name="Start Investing in 3 Steps" xml:space="preserve">
    <value>Start Investing in 3 Steps!</value>
    <comment>3 steps title</comment>
  </data>
  <data name="Start Investment Now" xml:space="preserve">
    <value>Start Investment Now</value>
    <comment>Button text for the second slider</comment>
  </data>
  <data name="Start Lending" xml:space="preserve">
    <value>Start Lending</value>
    <comment>Start lending button</comment>
  </data>
  <data name="Start Saving" xml:space="preserve">
    <value>Start Saving</value>
    <comment>Start saving button</comment>
  </data>
  <data name="Start Staking" xml:space="preserve">
    <value>Start Staking</value>
    <comment>Start staking button</comment>
  </data>
  <data name="Start Trading Now" xml:space="preserve">
    <value>Start Trading Now</value>
    <comment>Start trading button</comment>
  </data>
  <data name="Started" xml:space="preserve">
    <value>Started</value>
    <comment>Label for start date</comment>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Status</value>
    <comment>Status column header</comment>
  </data>
  <data name="Stay up-to-date with instant updates and programmable content changes." xml:space="preserve">
    <value>Stay up-to-date with instant updates and programmable content changes.</value>
  </data>
  <data name="Subject" xml:space="preserve">
    <value>Subject</value>
    <comment>Subject field placeholder</comment>
  </data>
  <data name="Submit" xml:space="preserve">
    <value>Submit</value>
    <comment>Submit button text</comment>
  </data>
  <data name="Submit Buy Order" xml:space="preserve">
    <value>Submit Buy Order</value>
    <comment>Buy order submit button text</comment>
  </data>
  <data name="Submit Deposit Report" xml:space="preserve">
    <value>Submit Deposit Report</value>
    <comment>Label for deposit report submission button</comment>
  </data>
  <data name="Submit Sell Order" xml:space="preserve">
    <value>Submit Sell Order</value>
    <comment>Sell order submit button text</comment>
  </data>
  <data name="Submit the form to notify us of your payment." xml:space="preserve">
    <value>Submit the form to notify us of your payment.</value>
    <comment>Instruction for payment notification</comment>
  </data>
  <data name="Submit the withdrawal request." xml:space="preserve">
    <value>Submit the withdrawal request.</value>
    <comment>Withdrawal step 3</comment>
  </data>
  <data name="Submitted" xml:space="preserve">
    <value>Submitted</value>
    <comment>Submitted status</comment>
  </data>
  <data name="Success" xml:space="preserve">
    <value>Success</value>
    <comment>Success message title</comment>
  </data>
  <data name="Success Count" xml:space="preserve">
    <value>Success Count</value>
    <comment>Number of successful executions of background service</comment>
  </data>
  <data name="Successfully saved" xml:space="preserve">
    <value>Successfully saved</value>
    <comment>Success message for save operation</comment>
  </data>
  <data name="Successfully updated" xml:space="preserve">
    <value>Successfully updated</value>
    <comment>Success message for update operation</comment>
  </data>
  <data name="Summary" xml:space="preserve">
    <value>Summary</value>
    <comment>Button text for viewing summary</comment>
  </data>
  <data name="Support" xml:space="preserve">
    <value>Support</value>
    <comment>Support label</comment>
  </data>
  <data name="Surname" xml:space="preserve">
    <value>Surname</value>
    <comment>Surname field label</comment>
  </data>
  <data name="Term Duration" xml:space="preserve">
    <value>Term Duration</value>
    <comment>Savings term duration</comment>
  </data>
  <data name="Terms and Conditions" xml:space="preserve">
    <value>Terms and Conditions</value>
    <comment>Label for terms and conditions</comment>
  </data>
  <data name="Text copied to clipboard" xml:space="preserve">
    <value>Text copied to clipboard</value>
    <comment>Success message when text is copied to clipboard</comment>
  </data>
  <data name="Text copied to clipboard using fallback" xml:space="preserve">
    <value>Text copied to clipboard using fallback</value>
    <comment>Success message when text is copied to clipboard using fallback method</comment>
  </data>
  <data name="Text For Description" xml:space="preserve">
    <value>Text to be written in description</value>
    <comment>Description text label</comment>
  </data>
  <data name="The package reward percentages were set to zero for the relevant levels." xml:space="preserve">
    <value>The package reward percentages were set to zero for the relevant levels.</value>
    <comment>Reason for no rewards distributed</comment>
  </data>
  <data name="The reward will be added to each referrer's RZW wallet." xml:space="preserve">
    <value>The reward will be added to each referrer's RZW wallet.</value>
    <comment>Instruction for reward distribution</comment>
  </data>
  <data name="The system will find all users in the referral chain of the user who made this payment." xml:space="preserve">
    <value>The system will find all users in the referral chain of the user who made this payment.</value>
    <comment>Instruction for reward distribution</comment>
  </data>
  <data name="Theme Selector" xml:space="preserve">
    <value>Theme Selector</value>
    <comment>Label for the theme selector UI</comment>
  </data>
  <data name="There are no users eligible for rewards in the referral chain for this payment." xml:space="preserve">
    <value>There are no users eligible for rewards in the referral chain for this payment.</value>
    <comment>Message explaining why there are no rewards to distribute</comment>
  </data>
  <data name="There were no users in the referral chain." xml:space="preserve">
    <value>There were no users in the referral chain.</value>
    <comment>Reason for no rewards distributed</comment>
  </data>
  <data name="This action is irreversible" xml:space="preserve">
    <value>This action is irreversible</value>
    <comment>Warning message for irreversible action</comment>
  </data>
  <data name="This amount will be deducted from your account balance." xml:space="preserve">
    <value>This amount will be deducted from your account balance.</value>
    <comment>Purchase information message</comment>
  </data>
  <data name="This balance transaction record will be permanently deleted" xml:space="preserve">
    <value>This balance transaction record will be permanently deleted</value>
    <comment>Warning message when deleting a balance transaction</comment>
  </data>
  <data name="This field is read-only once API is enabled" xml:space="preserve">
    <value>This field is read-only once API is enabled</value>
    <comment>Note for API-enabled fields</comment>
  </data>
  <data name="This field is read-only when using API" xml:space="preserve">
    <value>This field is read-only when using API</value>
    <comment>Note for API fields</comment>
  </data>
  <data name="This is the unique identifier for your transaction on the blockchain." xml:space="preserve">
    <value>This is the unique identifier for your transaction on the blockchain.</value>
    <comment>Description for transaction hash</comment>
  </data>
  <data name="This is your available balance after pending withdrawals" xml:space="preserve">
    <value>This is your available balance after pending withdrawals</value>
    <comment>Explanation for available withdrawal limit</comment>
  </data>
  <data name="This payment method is under construction. Please try again later." xml:space="preserve">
    <value>This payment method is under construction. Please try again later.</value>
    <comment>Under construction message</comment>
  </data>
  <data name="This payment record will be permanently deleted" xml:space="preserve">
    <value>This payment record will be permanently deleted</value>
    <comment>Warning for payment record deletion</comment>
  </data>
  <data name="This record will be permanently deleted" xml:space="preserve">
    <value>This record will be permanently deleted</value>
    <comment>Warning for record deletion</comment>
  </data>
  <data name="This referral code has reached its invite limit. Please use a different code." xml:space="preserve">
    <value>This referral code has reached its invite limit. Please use a different code.</value>
    <comment>Error message shown during registration when referrer has reached invite limit</comment>
  </data>
  <data name="This setting cannot be changed once enabled" xml:space="preserve">
    <value>This setting cannot be changed once enabled</value>
    <comment>Warning for irreversible settings</comment>
  </data>
  <data name="This setting record will be permanently deleted" xml:space="preserve">
    <value>This setting record will be permanently deleted</value>
    <comment>Warning for setting record deletion</comment>
  </data>
  <data name="This trade record will be permanently deleted" xml:space="preserve">
    <value>This trade record will be permanently deleted</value>
    <comment>Warning for trade record deletion</comment>
  </data>
  <data name="This tree shows the users referred by" xml:space="preserve">
    <value>This tree shows the users referred by</value>
    <comment>Used in referral tree page</comment>
  </data>
  <data name="This user hasn't referred any users yet." xml:space="preserve">
    <value>This user hasn't referred any users yet.</value>
    <comment>Used in referral tree page</comment>
  </data>
  <data name="This value will be added to the buy and sell prices. Can be negative." xml:space="preserve">
    <value>This value will be added to the buy and sell prices. Can be negative.</value>
    <comment>Description for price adjustment field</comment>
  </data>
  <data name="This wallet record will be permanently deleted" xml:space="preserve">
    <value>This wallet record will be permanently deleted</value>
    <comment>Warning for wallet record deletion</comment>
  </data>
  <data name="This withdrawal record will be permanently deleted" xml:space="preserve">
    <value>This withdrawal record will be permanently deleted</value>
    <comment>Warning message for withdrawal deletion</comment>
  </data>
  <data name="Time Since Last Success" xml:space="preserve">
    <value>Time Since Last Success</value>
    <comment>Time elapsed since last successful run of background service</comment>
  </data>
  <data name="To Date" xml:space="preserve">
    <value>To Date</value>
    <comment>To date filter label</comment>
  </data>
  <data name="To deposit funds into your account, please follow these steps:" xml:space="preserve">
    <value>To deposit funds into your account, please follow these steps:</value>
    <comment>Instruction for deposit process</comment>
  </data>
  <data name="To withdraw funds from your account, please follow these steps:" xml:space="preserve">
    <value>To withdraw funds from your account, please follow these steps:</value>
    <comment>Withdrawal instructions intro</comment>
  </data>
  <data name="Top {0} balances" xml:space="preserve">
    <value>Top {0} balances</value>
    <comment>Parameterized message for showing top N balances</comment>
  </data>
  <data name="Total" xml:space="preserve">
    <value>Total</value>
    <comment>Total column header</comment>
  </data>
  <data name="Total Amount Must Be Greater Than Zero" xml:space="preserve">
    <value>Total amount must be greater than zero</value>
    <comment>Validation message for amount</comment>
  </data>
  <data name="Total Balance" xml:space="preserve">
    <value>Total Balance</value>
    <comment>Total balance label</comment>
  </data>
  <data name="Total Interest" xml:space="preserve">
    <value>Total Interest</value>
    <comment>Total interest amount</comment>
  </data>
  <data name="Total Interest Earned" xml:space="preserve">
    <value>Total Interest Earned</value>
    <comment>Total interest earned from savings</comment>
  </data>
  <data name="Total Investment" xml:space="preserve">
    <value>Total Investment</value>
    <comment>Total amount invested in savings</comment>
  </data>
  <data name="Total Payments" xml:space="preserve">
    <value>Total Payments</value>
    <comment>Total number of payments</comment>
  </data>
  <data name="total results" xml:space="preserve">
    <value>total results</value>
    <comment>Total results label for pagination</comment>
  </data>
  <data name="Total RZW Distributed" xml:space="preserve">
    <value>Total RZW Distributed</value>
    <comment>Label for total RZW distributed</comment>
  </data>
  <data name="Total RZW to Distribute" xml:space="preserve">
    <value>Total RZW to Distribute</value>
    <comment>Label for the total amount of RZW tokens to be distributed</comment>
  </data>
  <data name="Total TL to Distribute" xml:space="preserve">
    <value>Total TL to Distribute</value>
    <comment>Label for the total amount of TL to be distributed</comment>
  </data>
  <data name="Total Value (TRY)" xml:space="preserve">
    <value>Total Value (TRY)</value>
    <comment>Label for total value in TRY</comment>
  </data>
  <data name="Trade" xml:space="preserve">
    <value>Trade</value>
    <comment>Label for trade</comment>
  </data>
  <data name="Trade Activity Check" xml:space="preserve">
    <value>Trade Activity Check</value>
    <comment>Check item for trade activity</comment>
  </data>
  <data name="Trade Loss" xml:space="preserve">
    <value>Trade Loss</value>
    <comment>Transaction type for trade losses</comment>
  </data>
  <data name="Trade Profit" xml:space="preserve">
    <value>Trade Profit</value>
    <comment>Transaction type for trade profits</comment>
  </data>
  <data name="Trade successfully saved" xml:space="preserve">
    <value>Trade successfully saved</value>
    <comment>Success message for trade saving</comment>
  </data>
  <data name="Trades" xml:space="preserve">
    <value>Trades</value>
    <comment>Trades menu item</comment>
  </data>
  <data name="Trading Fee" xml:space="preserve">
    <value>Trading Fee</value>
    <comment>Trading fee label</comment>
  </data>
  <data name="Trading Volume" xml:space="preserve">
    <value>Trading Volume</value>
    <comment>Trading volume label</comment>
  </data>
  <data name="Transaction Date:" xml:space="preserve">
    <value>Transaction Date:</value>
    <comment>Transaction date label</comment>
  </data>
  <data name="Transaction Hash" xml:space="preserve">
    <value>Transaction Hash</value>
    <comment>Label for transaction hash field</comment>
  </data>
  <data name="Transaction Hash/ID" xml:space="preserve">
    <value>Transaction Hash/ID</value>
    <comment>Label for transaction hash/ID field</comment>
  </data>
  <data name="Transaction History" xml:space="preserve">
    <value>Transaction History</value>
    <comment>Label for transaction history section</comment>
  </data>
  <data name="Transaction Reference" xml:space="preserve">
    <value>Transaction Reference</value>
    <comment>Label for transaction reference field</comment>
  </data>
  <data name="Transaction Reversal" xml:space="preserve">
    <value>Transaction Reversal</value>
    <comment>Transaction reversal module title</comment>
  </data>
  <data name="Transaction Risks Text" xml:space="preserve">
    <value>Cryptocurrency investments involve high risk. Do not invest amounts you cannot afford to lose. Past performance is not a guarantee of future results.</value>
    <comment>Text for transaction risks section in user agreement</comment>
  </data>
  <data name="Transaction Risks Title" xml:space="preserve">
    <value>Transaction Risks</value>
    <comment>Title for transaction risks section in user agreement</comment>
  </data>
  <data name="Transaction Type" xml:space="preserve">
    <value>Transaction Type</value>
    <comment>Label for transaction type field</comment>
  </data>
  <data name="Transfer the amount you wish to deposit to the selected bank account." xml:space="preserve">
    <value>Transfer the amount you wish to deposit to the selected bank account.</value>
    <comment>Instruction for bank transfer</comment>
  </data>
  <data name="Transfer Time" xml:space="preserve">
    <value>Transfer Time</value>
    <comment>Transfer time field label</comment>
  </data>
  <data name="Transfer Time Required" xml:space="preserve">
    <value>Transfer time is required</value>
    <comment>Validation message for transfer time</comment>
  </data>
  <data name="Transfer Time:" xml:space="preserve">
    <value>Transfer Time:</value>
    <comment>Transfer time label</comment>
  </data>
  <data name="Transfer Your Account" xml:space="preserve">
    <value>Transfer Your Account</value>
    <comment>Transfer account title</comment>
  </data>
  <data name="Try adjusting your filter criteria or check back later." xml:space="preserve">
    <value>Try adjusting your filter criteria or check back later.</value>
    <comment>Suggestion when no payments found</comment>
  </data>
  <data name="TRY Balance" xml:space="preserve">
    <value>TRY Balance</value>
    <comment>TRY Balance label</comment>
  </data>
  <data name="TRY Value (Approx.)" xml:space="preserve">
    <value>TRY Value (Approx.)</value>
    <comment>Label for approximate TRY value</comment>
  </data>
  <data name="Type" xml:space="preserve">
    <value>Type</value>
    <comment>Type label</comment>
  </data>
  <data name="Unable to load savings data. Please try again later." xml:space="preserve">
    <value>Unable to load savings data. Please try again later.</value>
    <comment>Error message when savings data cannot be loaded</comment>
  </data>
  <data name="Under Construction" xml:space="preserve">
    <value>Under Construction</value>
    <comment>Under construction title</comment>
  </data>
  <data name="Unknown" xml:space="preserve">
    <value>Unknown</value>
    <comment>Default text for unknown values</comment>
  </data>
  <data name="<EMAIL>" xml:space="preserve">
    <value><EMAIL></value>
    <comment>Default email for unknown users</comment>
  </data>
  <data name="Unlimited" xml:space="preserve">
    <value>Unlimited</value>
    <comment>Label for unlimited</comment>
  </data>
  <data name="Upgrade" xml:space="preserve">
    <value>Upgrade</value>
    <comment>Button text for upgrading to a higher package</comment>
  </data>
  <data name="Upgrade Package" xml:space="preserve">
    <value>Upgrade Package</value>
    <comment>Button text to upgrade package</comment>
  </data>
  <data name="upgrade to" xml:space="preserve">
    <value>upgrade to</value>
    <comment>Text used in upgrade confirmation message</comment>
  </data>
  <data name="USDT (TRC20) Deposit Address" xml:space="preserve">
    <value>USDT (TRC20) Deposit Address</value>
    <comment>Label for USDT deposit address</comment>
  </data>
  <data name="USDT Address" xml:space="preserve">
    <value>USDT Address</value>
    <comment>Label for USDT address field</comment>
  </data>
  <data name="User" xml:space="preserve">
    <value>User</value>
    <comment>User label</comment>
  </data>
  <data name="User Agreement" xml:space="preserve">
    <value>User Agreement</value>
    <comment>User Agreement page title</comment>
  </data>
  <data name="User Agreement Title" xml:space="preserve">
    <value>RAZEWIN User Agreement</value>
    <comment>Title for the main user agreement</comment>
  </data>
  <data name="User already exists" xml:space="preserve">
    <value>User already exists</value>
    <comment>Error message for existing user</comment>
  </data>
  <data name="User Balance" xml:space="preserve">
    <value>User Balance</value>
    <comment>User balance label</comment>
  </data>
  <data name="User does not have enough balance" xml:space="preserve">
    <value>User does not have enough balance</value>
    <comment>Error message when user doesn't have enough balance</comment>
  </data>
  <data name="User does not have enough coin balance" xml:space="preserve">
    <value>User does not have enough coin balance</value>
    <comment>Error message when user doesn't have enough cryptocurrency balance</comment>
  </data>
  <data name="User does not have enough TRY balance" xml:space="preserve">
    <value>User does not have enough TRY balance</value>
    <comment>Error message when user doesn't have enough TRY balance</comment>
  </data>
  <data name="User has made {0} trades after this deposit was approved" xml:space="preserve">
    <value>User has made {0} trades after this deposit was approved</value>
    <comment>Status message for trade activity check with count</comment>
  </data>
  <data name="User has made {0} withdrawals after this deposit was approved" xml:space="preserve">
    <value>User has made {0} withdrawals after this deposit was approved</value>
    <comment>Status message for withdrawal activity check with count</comment>
  </data>
  <data name="User has purchased {0} packages after this deposit was approved" xml:space="preserve">
    <value>User has purchased {0} packages after this deposit was approved</value>
    <comment>Status message for package purchase check with count</comment>
  </data>
  <data name="User has sufficient balance for reversal" xml:space="preserve">
    <value>User has sufficient balance for reversal</value>
    <comment>Status message for balance sufficiency check</comment>
  </data>
  <data name="User List" xml:space="preserve">
    <value>User List</value>
    <comment>Label for user list section</comment>
  </data>
  <data name="User Name:" xml:space="preserve">
    <value>User Name:</value>
    <comment>User name label</comment>
  </data>
  <data name="User not found" xml:space="preserve">
    <value>User not found</value>
    <comment>Error message for user not found</comment>
  </data>
  <data name="User Package" xml:space="preserve">
    <value>User Package</value>
    <comment>Label for user package</comment>
  </data>
  <data name="User Packages" xml:space="preserve">
    <value>User Packages</value>
    <comment>Navigation menu item for user packages</comment>
  </data>
  <data name="Users" xml:space="preserve">
    <value>Users</value>
    <comment>Label for users section</comment>
  </data>
  <data name="User's current balance ({0}) is less than deposit amount ({1})" xml:space="preserve">
    <value>User's current balance ({0}) is less than deposit amount ({1})</value>
    <comment>Status message for balance sufficiency check with amounts</comment>
  </data>
  <data name="USERS EXCEEDED" xml:space="preserve">
    <value>USERS EXCEEDED</value>
    <comment>Users count text</comment>
  </data>
  <data name="Users in the referral chain did not have active packages." xml:space="preserve">
    <value>Users in the referral chain did not have active packages.</value>
    <comment>Reason for no rewards distributed</comment>
  </data>
  <data name="Users Registered with Your Referral Code" xml:space="preserve">
    <value>Users Registered with Your Referral Code</value>
    <comment>More descriptive label for referred users list</comment>
  </data>
  <data name="Users to be Rewarded" xml:space="preserve">
    <value>Users to be Rewarded</value>
    <comment>Label for the number of users who will receive rewards</comment>
  </data>
  <data name="Value" xml:space="preserve">
    <value>Value</value>
    <comment>Label for value field</comment>
  </data>
  <data name="Value (TRY)" xml:space="preserve">
    <value>Value (TRY)</value>
    <comment>Label for value in TRY</comment>
  </data>
  <data name="Verification Checks" xml:space="preserve">
    <value>Verification Checks</value>
    <comment>Title for verification checks section</comment>
  </data>
  <data name="View All" xml:space="preserve">
    <value>View All</value>
    <comment>Link text to view all items</comment>
  </data>
  <data name="VIEW ALL LIVE STREAM" xml:space="preserve">
    <value>VIEW ALL LIVE STREAM</value>
    <comment>View all live stream button text</comment>
  </data>
  <data name="View All Interest History" xml:space="preserve">
    <value>View All Interest History</value>
    <comment>Button to view all interest history</comment>
  </data>
  <data name="View Available Packages" xml:space="preserve">
    <value>View Available Packages</value>
    <comment>Button text to view packages</comment>
  </data>
  <data name="View Packages" xml:space="preserve">
    <value>View Packages</value>
    <comment>Button text to view packages</comment>
  </data>
  <data name="View Referrer" xml:space="preserve">
    <value>View Referrer</value>
    <comment>Used in referral tree page</comment>
  </data>
  <data name="View Reward Summary" xml:space="preserve">
    <value>View Reward Summary</value>
    <comment>Button text for viewing reward summary</comment>
  </data>
  <data name="View Savings Details" xml:space="preserve">
    <value>View Savings Details</value>
    <comment>Button to view savings details</comment>
  </data>
  <data name="Vision" xml:space="preserve">
    <value>Vision</value>
    <comment>Vision title</comment>
  </data>
  <data name="Wallets" xml:space="preserve">
    <value>Wallets</value>
    <comment>Label for wallets section</comment>
  </data>
  <data name="Warning" xml:space="preserve">
    <value>Warning</value>
    <comment>Warning message title</comment>
  </data>
  <data name="Warning: Deleting this package may affect referral rewards" xml:space="preserve">
    <value>Warning: Deleting this package may affect referral rewards</value>
    <comment>Warning message for deleting a package</comment>
  </data>
  <data name="Warning: Editing a balance transaction will not update user balances. Use with caution." xml:space="preserve">
    <value>Warning: Editing a balance transaction will not update user balances. Use with caution.</value>
    <comment>Warning message when editing a balance transaction</comment>
  </data>
  <data name="Warning: Editing a trade will not update user balances. Use with caution." xml:space="preserve">
    <value>Warning: Editing a trade will not update user balances. Use with caution.</value>
    <comment>Warning message when editing trades</comment>
  </data>
  <data name="Welcome back! Here are your active savings accounts." xml:space="preserve">
    <value>Welcome back! Here are your active savings accounts.</value>
    <comment>Welcome message for users with active savings</comment>
  </data>
  <data name="Welcome to RazeWin" xml:space="preserve">
    <value>Welcome to RazeWin</value>
    <comment>Welcome panel title</comment>
  </data>
  <data name="What Does RazeWin Do" xml:space="preserve">
    <value>What Does RazeWin Do</value>
    <comment>RazeWin services description page title</comment>
  </data>
  <data name="What is Coin" xml:space="preserve">
    <value>What is Coin</value>
    <comment>What is Coin page title</comment>
  </data>
  <data name="What Is It" xml:space="preserve">
    <value>What Is It</value>
    <comment>What Is It page title</comment>
  </data>
  <data name="What Our Customers Say" xml:space="preserve">
    <value>What Our Customers Say?</value>
    <comment>Testimonials section title</comment>
  </data>
  <data name="WhatsApp" xml:space="preserve">
    <value>WhatsApp</value>
    <comment>Label for WhatsApp field</comment>
  </data>
  <data name="When a user purchases a package, they will receive RZW tokens equal to the package price." xml:space="preserve">
    <value>When a user purchases a package, they will receive RZW tokens equal to the package price.</value>
    <comment>Information about RZW tokens given with package purchase</comment>
  </data>
  <data name="When you distribute rewards for this payment:" xml:space="preserve">
    <value>When you distribute rewards for this payment:</value>
    <comment>Heading for reward distribution instructions</comment>
  </data>
  <data name="When you purchase a new package, any previous packages will be deactivated" xml:space="preserve">
    <value>When you purchase a new package, any previous packages will be deactivated</value>
    <comment>Message informing users about package deactivation</comment>
  </data>
  <data name="Who Referred Whom" xml:space="preserve">
    <value>Who Referred Whom</value>
    <comment>Used in referral hierarchy view</comment>
  </data>
  <data name="Why Digital Signage?" xml:space="preserve">
    <value>Why Digital Signage?</value>
  </data>
  <data name="Why Earn with RazeWin?" xml:space="preserve">
    <value>Why Earn with RazeWin?</value>
    <comment>Why earn section title</comment>
  </data>
  <data name="Withdraw Early" xml:space="preserve">
    <value>Withdraw Early</value>
    <comment>Early withdrawal button text</comment>
  </data>
  <data name="Withdraw Funds" xml:space="preserve">
    <value>Withdraw Funds</value>
    <comment>Withdraw funds button/title</comment>
  </data>
  <data name="Withdrawal" xml:space="preserve">
    <value>Withdrawal</value>
    <comment>Withdrawal page title</comment>
  </data>
  <data name="Withdrawal Activity Check" xml:space="preserve">
    <value>Withdrawal Activity Check</value>
    <comment>Check item for withdrawal activity</comment>
  </data>
  <data name="Withdrawal Amount" xml:space="preserve">
    <value>Withdrawal Amount</value>
    <comment>Withdrawal amount label</comment>
  </data>
  <data name="Withdrawal History" xml:space="preserve">
    <value>Withdrawal History</value>
    <comment>Withdrawal history title</comment>
  </data>
  <data name="Withdrawal Information" xml:space="preserve">
    <value>Withdrawal Information</value>
    <comment>Label for withdrawal information section</comment>
  </data>
  <data name="Withdrawal Instructions" xml:space="preserve">
    <value>Withdrawal Instructions</value>
    <comment>Withdrawal instructions title</comment>
  </data>
  <data name="Withdrawal Request Submitted" xml:space="preserve">
    <value>Withdrawal request submitted successfully</value>
    <comment>Success message for withdrawal submission</comment>
  </data>
  <data name="Withdrawal request submitted successfully" xml:space="preserve">
    <value>Withdrawal request submitted successfully. Your request will be processed within 1-3 business days.</value>
    <comment>Success message for withdrawal submission</comment>
  </data>
  <data name="Withdrawal reversal: {0}" xml:space="preserve">
    <value>Withdrawal reversal: {0}</value>
    <comment>Description for withdrawal reversal transaction</comment>
  </data>
  <data name="Withdrawal Submission Error" xml:space="preserve">
    <value>Error submitting withdrawal: {0}</value>
    <comment>Error message for withdrawal submission</comment>
  </data>
  <data name="Withdrawal: {0}" xml:space="preserve">
    <value>Withdrawal: {0}</value>
    <comment>Description for withdrawal transaction</comment>
  </data>
  <data name="Withdrawals" xml:space="preserve">
    <value>Withdrawals</value>
    <comment>Withdrawals menu item</comment>
  </data>
  <data name="Write Exact Text In Papara Description" xml:space="preserve">
    <value>Please write the text above exactly in the Papara description section. Your transaction will be approved faster.</value>
    <comment>Papara description instruction</comment>
  </data>
  <data name="Years of Experience" xml:space="preserve">
    <value>Years of Experience</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Yes</value>
    <comment>Yes text</comment>
  </data>
  <data name="You can invite" xml:space="preserve">
    <value>You can invite</value>
    <comment>Part of message showing remaining invites</comment>
  </data>
  <data name="You can upgrade to a higher package at any time." xml:space="preserve">
    <value>You can upgrade to a higher package at any time.</value>
    <comment>Information about upgrading packages</comment>
  </data>
  <data name="You can withdraw your RZW tokens early, but you may receive reduced interest based on the holding period." xml:space="preserve">
    <value>You can withdraw your RZW tokens early, but you may receive reduced interest based on the holding period.</value>
    <comment>Early withdrawal warning text</comment>
  </data>
  <data name="You cannot purchase a lower package than your current one" xml:space="preserve">
    <value>You cannot purchase a lower package than your current one</value>
    <comment>Error message when trying to purchase a lower package</comment>
  </data>
  <data name="You do not have permission to access the admin area" xml:space="preserve">
    <value>You do not have permission to access the admin area</value>
    <comment>Error message for admin area access</comment>
  </data>
  <data name="You do not have permission to view this page" xml:space="preserve">
    <value>You do not have permission to view this page</value>
    <comment>Error message for page access</comment>
  </data>
  <data name="You don't have an active package" xml:space="preserve">
    <value>You don't have an active package</value>
    <comment>Message when user has no package</comment>
  </data>
  <data name="You don't have enough balance to purchase this package." xml:space="preserve">
    <value>You don't have enough balance to purchase this package.</value>
    <comment>Error message for insufficient balance</comment>
  </data>
  <data name="You have reached your invite limit. Upgrade your package to invite more users." xml:space="preserve">
    <value>You have reached your invite limit. Upgrade your package to invite more users.</value>
    <comment>Message shown when user has reached their invite limit</comment>
  </data>
  <data name="You haven't referred any users yet. Share your referral code to start referring friends!" xml:space="preserve">
    <value>You haven't referred any users yet. Share your referral code to start referring friends!</value>
    <comment>Message shown when user has no referred users</comment>
  </data>
  <data name="You need to purchase a package to start inviting users." xml:space="preserve">
    <value>You need to purchase a package to start inviting users.</value>
    <comment>Message shown when user has no package</comment>
  </data>
  <data name="Your account allows unlimited invites." xml:space="preserve">
    <value>Your account allows unlimited invites.</value>
    <comment>Message shown when user has no package but can invite unlimited users</comment>
  </data>
  <data name="Your account will be credited once the payment is verified." xml:space="preserve">
    <value>Your account will be credited once the payment is verified.</value>
    <comment>Message for payment verification</comment>
  </data>
  <data name="Your Balance" xml:space="preserve">
    <value>Your Balance</value>
    <comment>Label for user balance display</comment>
  </data>
  <data name="Your cryptocurrency deposit request has been received. It will be processed as soon as possible." xml:space="preserve">
    <value>Your cryptocurrency deposit request has been received. It will be processed as soon as possible.</value>
    <comment>Confirmation message for cryptocurrency deposit</comment>
  </data>
  <data name="Your current balance" xml:space="preserve">
    <value>Your current balance</value>
    <comment>Label for current balance</comment>
  </data>
  <data name="Your Current Package" xml:space="preserve">
    <value>Your Current Package</value>
    <comment>Section heading for current package</comment>
  </data>
  <data name="Your current package allows unlimited invites." xml:space="preserve">
    <value>Your current package allows unlimited invites.</value>
    <comment>Message shown when user has unlimited invites</comment>
  </data>
  <data name="Your deposit request has been received. It will be processed as soon as possible." xml:space="preserve">
    <value>Your deposit request has been received. It will be processed as soon as possible.</value>
    <comment>Success message for deposit submission</comment>
  </data>
  <data name="Your Email" xml:space="preserve">
    <value>Your Email</value>
    <comment>Email field placeholder</comment>
  </data>
  <data name="Your Gateway to the Digital World" xml:space="preserve">
    <value>Your Gateway to the Digital World</value>
  </data>
  <data name="Your Message" xml:space="preserve">
    <value>Your Message</value>
    <comment>Message field placeholder</comment>
  </data>
  <data name="Your Name" xml:space="preserve">
    <value>Your Name</value>
    <comment>Name field placeholder</comment>
  </data>
  <data name="Your request will be processed within 1-3 business days." xml:space="preserve">
    <value>Your request will be processed within 1-3 business days.</value>
    <comment>Withdrawal step 4</comment>
  </data>
  <data name="Your Rights Text" xml:space="preserve">
    <value>You have the right to access, correct, delete, and object to the processing of your personal data. You can contact our customer service to exercise these rights.</value>
    <comment>Text for your rights section in privacy agreement</comment>
  </data>
  <data name="Your Rights Title" xml:space="preserve">
    <value>Your Rights</value>
    <comment>Title for your rights section in privacy agreement</comment>
  </data>
  <data name="Your trusted partner for cryptocurrency investments. Start trading today and experience the difference." xml:space="preserve">
    <value>Your trusted partner for cryptocurrency investments. Start trading today and experience the difference.</value>
    <comment>Welcome panel description</comment>
  </data>
  <data name="RZW TOKEN" xml:space="preserve">
    <value>RZW TOKEN</value>
    <comment>RZW Token name for listing slider</comment>
  </data>
  <data name="LISTING" xml:space="preserve">
    <value>LISTING</value>
    <comment>Listing text for RZW Token slider</comment>
  </data>
  <data name="DATE WILL BE ANNOUNCED" xml:space="preserve">
    <value>DATE WILL BE ANNOUNCED</value>
    <comment>Date announcement text for RZW Token slider</comment>
  </data>
  <data name="ON JUNE 25TH" xml:space="preserve">
    <value>ON JUNE 25TH</value>
    <comment>Date specification for RZW Token slider</comment>
  </data>
  <data name="LISTING DATE" xml:space="preserve">
    <value>LISTING DATE</value>
    <comment>Listing date text for RZW Token slider</comment>
  </data>
  <data name="WILL BE ANNOUNCED ON JUNE 25TH" xml:space="preserve">
    <value>WILL BE ANNOUNCED ON JUNE 25TH</value>
    <comment>Full announcement text for RZW Token slider</comment>
  </data>
  <data name="Your RZW tokens will be locked until maturity date" xml:space="preserve">
    <value>Your RZW tokens will be locked until maturity date</value>
    <comment>Warning text about token locking</comment>
  </data>
  <data name="Savings Plans" xml:space="preserve">
    <value>Savings Plans</value>
    <comment>Admin menu item for savings plans management</comment>
  </data>
  <data name="Savings Accounts" xml:space="preserve">
    <value>Savings Accounts</value>
    <comment>Admin menu item for savings accounts management</comment>
  </data>
  <data name="Monitoring" xml:space="preserve">
    <value>Monitoring</value>
    <comment>Admin menu item for system monitoring</comment>
  </data>
  <data name="Plan Management" xml:space="preserve">
    <value>Plan Management</value>
    <comment>Admin page title for plan management</comment>
  </data>
  <data name="Account Management" xml:space="preserve">
    <value>Account Management</value>
    <comment>Admin page title for account management</comment>
  </data>
  <data name="System Monitoring" xml:space="preserve">
    <value>System Monitoring</value>
    <comment>Admin page title for system monitoring</comment>
  </data>
  <data name="Create Plan" xml:space="preserve">
    <value>Create Plan</value>
    <comment>Button text for creating new savings plan</comment>
  </data>
  <data name="Edit Plan" xml:space="preserve">
    <value>Edit Plan</value>
    <comment>Button text for editing savings plan</comment>
  </data>
  <data name="Delete Plan" xml:space="preserve">
    <value>Delete Plan</value>
    <comment>Button text for deleting savings plan</comment>
  </data>
  <data name="Plan Details" xml:space="preserve">
    <value>Plan Details</value>
    <comment>Section title for plan details</comment>
  </data>
  <data name="Min Amount" xml:space="preserve">
    <value>Min Amount</value>
    <comment>Label for minimum amount field</comment>
  </data>
  <data name="Max Amount" xml:space="preserve">
    <value>Max Amount</value>
    <comment>Label for maximum amount field</comment>
  </data>
  <data name="Display Order" xml:space="preserve">
    <value>Display Order</value>
    <comment>Label for display order field</comment>
  </data>
  <data name="Plan Statistics" xml:space="preserve">
    <value>Plan Statistics</value>
    <comment>Section title for plan statistics</comment>
  </data>
  <data name="Total Accounts" xml:space="preserve">
    <value>Total Accounts</value>
    <comment>Label for total accounts count</comment>
  </data>
  <data name="Total Locked RZW" xml:space="preserve">
    <value>Total Locked RZW</value>
    <comment>Label for total locked RZW amount</comment>
  </data>
  <data name="Daily Interest Paid" xml:space="preserve">
    <value>Daily Interest Paid</value>
    <comment>Label for daily interest payments</comment>
  </data>
  <data name="Background Service Status" xml:space="preserve">
    <value>Background Service Status</value>
    <comment>Label for background service status</comment>
  </data>
  <data name="Last Run Time" xml:space="preserve">
    <value>Last Run Time</value>
    <comment>Label for last run time</comment>
  </data>
  <data name="Pending Interest Payments" xml:space="preserve">
    <value>Pending Interest Payments</value>
    <comment>Label for pending interest payments</comment>
  </data>
  <data name="Matured Accounts" xml:space="preserve">
    <value>Matured Accounts</value>
    <comment>Label for matured accounts</comment>
  </data>
  <data name="Performance Metrics" xml:space="preserve">
    <value>Performance Metrics</value>
    <comment>Section title for performance metrics</comment>
  </data>
  <data name="Plan Performance" xml:space="preserve">
    <value>Plan Performance</value>
    <comment>Page title for plan performance report</comment>
  </data>
  <data name="User Statistics" xml:space="preserve">
    <value>User Statistics</value>
    <comment>Page title for user statistics report</comment>
  </data>
  <data name="Early Withdrawal Analysis" xml:space="preserve">
    <value>Early Withdrawal Analysis</value>
    <comment>Section title for early withdrawal analysis</comment>
  </data>
  <data name="Force Maturity" xml:space="preserve">
    <value>Force Maturity</value>
    <comment>Button text for forcing account maturity</comment>
  </data>
  <data name="Emergency Close" xml:space="preserve">
    <value>Emergency Close</value>
    <comment>Button text for emergency account closure</comment>
  </data>
  <data name="Plan created successfully" xml:space="preserve">
    <value>Plan created successfully</value>
    <comment>Success message for plan creation</comment>
  </data>
  <data name="Plan updated successfully" xml:space="preserve">
    <value>Plan updated successfully</value>
    <comment>Success message for plan update</comment>
  </data>
  <data name="Plan deleted successfully" xml:space="preserve">
    <value>Plan deleted successfully</value>
    <comment>Success message for plan deletion</comment>
  </data>
  <data name="An error occurred while creating the plan" xml:space="preserve">
    <value>An error occurred while creating the plan</value>
    <comment>Error message for plan creation</comment>
  </data>
  <data name="An error occurred while updating the plan" xml:space="preserve">
    <value>An error occurred while updating the plan</value>
    <comment>Error message for plan update</comment>
  </data>
  <data name="An error occurred while deleting the plan" xml:space="preserve">
    <value>An error occurred while deleting the plan</value>
    <comment>Error message for plan deletion</comment>
  </data>
  <data name="Cannot delete plan with active accounts. Please wait for all accounts to mature or be withdrawn." xml:space="preserve">
    <value>Cannot delete plan with active accounts. Please wait for all accounts to mature or be withdrawn.</value>
    <comment>Error message when trying to delete plan with active accounts</comment>
  </data>
  <data name="RZW Savings plans define the terms and interest rates for savings accounts." xml:space="preserve">
    <value>RZW Savings plans define the terms and interest rates for savings accounts.</value>
    <comment>Info message about savings plans</comment>
  </data>
  <data name="Enter as percentage (e.g., 0.03 for 0.03%)" xml:space="preserve">
    <value>Enter as percentage (e.g., 0.03 for 0.03%)</value>
    <comment>Help text for interest rate input</comment>
  </data>
  <data name="Account maturity processed successfully" xml:space="preserve">
    <value>Account maturity processed successfully</value>
    <comment>Success message for forced maturity</comment>
  </data>
  <data name="Account closed successfully" xml:space="preserve">
    <value>Account closed successfully</value>
    <comment>Success message for emergency close</comment>
  </data>
  <data name="An error occurred while processing maturity" xml:space="preserve">
    <value>An error occurred while processing maturity</value>
    <comment>Error message for maturity processing</comment>
  </data>
  <data name="An error occurred while closing the account" xml:space="preserve">
    <value>An error occurred while closing the account</value>
    <comment>Error message for account closure</comment>
  </data>
  <data name="Are you sure you want to force maturity for this account?" xml:space="preserve">
    <value>Are you sure you want to force maturity for this account?</value>
    <comment>Confirmation message for forced maturity</comment>
  </data>
  <data name="Are you sure you want to emergency close this account?" xml:space="preserve">
    <value>Are you sure you want to emergency close this account?</value>
    <comment>Confirmation message for emergency close</comment>
  </data>
  <data name="No interest payments yet" xml:space="preserve">
    <value>No interest payments yet</value>
    <comment>Message when no interest payments exist</comment>
  </data>
  <data name="Generate Report" xml:space="preserve">
    <value>Generate Report</value>
    <comment>Button text for generating reports</comment>
  </data>
  <data name="RZW Savings Reports" xml:space="preserve">
    <value>RZW Savings Reports</value>
    <comment>Page title for reports</comment>
  </data>
  <data name="Plan Performance Report" xml:space="preserve">
    <value>Plan Performance Report</value>
    <comment>Report section title</comment>
  </data>
  <data name="Account Activity" xml:space="preserve">
    <value>Account Activity</value>
    <comment>Report section title</comment>
  </data>
  <data name="Months" xml:space="preserve">
    <value>Months</value>
    <comment>Months label</comment>
  </data>
  <data name="Years" xml:space="preserve">
    <value>Years</value>
    <comment>Years label</comment>
  </data>
  <data name="Plans Loading" xml:space="preserve">
    <value>Plans Loading</value>
    <comment>Loading message for plans</comment>
  </data>
  <data name="Please wait" xml:space="preserve">
    <value>Please wait</value>
    <comment>Please wait message</comment>
  </data>
  <data name="Daily" xml:space="preserve">
    <value>Daily</value>
    <comment>Daily period label</comment>
  </data>
  <data name="Monthly" xml:space="preserve">
    <value>Monthly</value>
    <comment>Monthly period label</comment>
  </data>
  <data name="Yearly" xml:space="preserve">
    <value>Yearly</value>
    <comment>Yearly period label</comment>
  </data>
  <data name="APY" xml:space="preserve">
    <value>APY</value>
    <comment>Annual Percentage Yield - Yıllık Getiri Oranı</comment>
  </data>
  <data name="Minimum amount" xml:space="preserve">
    <value>Minimum amount</value>
    <comment>Minimum amount label</comment>
  </data>
  <data name="Maximum" xml:space="preserve">
    <value>Maximum</value>
    <comment>Maximum label</comment>
  </data>
  <data name="Total Earned" xml:space="preserve">
    <value>Total Earned</value>
    <comment>Label for total earned amount</comment>
  </data>
  <data name="Early Withdrawal Penalty" xml:space="preserve">
    <value>Early Withdrawal Penalty</value>
    <comment>Label for early withdrawal penalty</comment>
  </data>
  <data name="Enabled" xml:space="preserve">
    <value>Enabled</value>
    <comment>Status label for enabled state</comment>
  </data>
  <data name="Disabled" xml:space="preserve">
    <value>Disabled</value>
    <comment>Status label for disabled state</comment>
  </data>
  <data name="Interest Projections" xml:space="preserve">
    <value>Interest Projections</value>
    <comment>Section title for interest projections</comment>
  </data>
  <data name="Remaining Interest" xml:space="preserve">
    <value>Remaining Interest</value>
    <comment>Label for remaining interest to be earned</comment>
  </data>
  <data name="Total Interest at Maturity" xml:space="preserve">
    <value>Total Interest at Maturity</value>
    <comment>Label for total interest at maturity</comment>
  </data>
  <data name="Average Daily Interest" xml:space="preserve">
    <value>Average Daily Interest</value>
    <comment>Label for average daily interest amount</comment>
  </data>
  <data name="Next Interest Payment" xml:space="preserve">
    <value>Next Interest Payment</value>
    <comment>Label for next interest payment date</comment>
  </data>
  <data name="Tomorrow" xml:space="preserve">
    <value>Tomorrow</value>
    <comment>Label for tomorrow</comment>
  </data>
  <data name="Return on Investment" xml:space="preserve">
    <value>Return on Investment</value>
    <comment>Label for return on investment percentage</comment>
  </data>
  <data name="Days Held" xml:space="preserve">
    <value>Days Held</value>
    <comment>Label for number of days held</comment>
  </data>
  <data name="Daily Interest Amount" xml:space="preserve">
    <value>Daily Interest Amount</value>
    <comment>Label for daily interest amount</comment>
  </data>
  <data name="Day" xml:space="preserve">
    <value>Day</value>
    <comment>Single day unit</comment>
  </data>
  <data name="Month" xml:space="preserve">
    <value>Month</value>
    <comment>Singular form of month</comment>
  </data>
  <data name="Year" xml:space="preserve">
    <value>Year</value>
    <comment>Singular form of year</comment>
  </data>
  <data name="Savings History" xml:space="preserve">
    <value>Savings History</value>
    <comment>Page title for savings account history</comment>
  </data>
  <data name="All Savings Accounts" xml:space="preserve">
    <value>All Savings Accounts</value>
    <comment>Title for all savings accounts section</comment>
  </data>
  <data name="View All Accounts" xml:space="preserve">
    <value>View All Accounts</value>
    <comment>Link text to view all savings accounts</comment>
  </data>
  <data name="Savings Deposit" xml:space="preserve">
    <value>Savings Deposit</value>
    <comment>Trade type for RZW savings deposit</comment>
  </data>
  <data name="Savings Withdrawal" xml:space="preserve">
    <value>Savings Withdrawal</value>
    <comment>Trade type for RZW savings withdrawal</comment>
  </data>
  <data name="Savings Interest" xml:space="preserve">
    <value>Savings Interest</value>
    <comment>Trade type for RZW savings interest payment</comment>
  </data>
  <data name="Savings Early Withdrawal" xml:space="preserve">
    <value>Savings Early Withdrawal</value>
    <comment>Trade type for RZW savings early withdrawal</comment>
  </data>
  <data name="Savings Maturity" xml:space="preserve">
    <value>Savings Maturity</value>
    <comment>Trade type for RZW savings maturity</comment>
  </data>
  <data name="Data Issue Detected" xml:space="preserve">
    <value>Data Issue Detected</value>
    <comment>Warning message for data integrity issues</comment>
  </data>
  <data name="This savings account has a zero interest rate. Please contact support if this is unexpected." xml:space="preserve">
    <value>This savings account has a zero interest rate. Please contact support if this is unexpected.</value>
    <comment>Warning message for zero interest rate accounts</comment>
  </data>
  <data name="Selected savings plan has invalid interest rate" xml:space="preserve">
    <value>Selected savings plan has invalid interest rate</value>
    <comment>Error message for plans with invalid interest rates</comment>
  </data>
  <data name="Term Type" xml:space="preserve">
    <value>Term Type</value>
    <comment>Savings account term type label</comment>
  </data>
  <data name="My Saving Accounts" xml:space="preserve">
    <value>My Saving Accounts</value>
    <comment>Button: My Saving Accounts (Vadeli Hesaplarım)</comment>
  </data>
  <data name="Annual Percentage Yield" xml:space="preserve">
    <value>Annual Percentage Yield</value>
    <comment>Full description for APY/YGO label, used in all languages</comment>
  </data>
  <data name="Claim Interest" xml:space="preserve">
    <value>Claim Interest</value>
    <comment>Button text for claiming matured savings interest.</comment>
  </data>
  <data name="Maturity Reached! Claim your interest:" xml:space="preserve">
    <value>Maturity Reached! Claim your interest:</value>
    <comment>Alert for matured savings account, prompting user to claim interest.</comment>
  </data>
  <data name="Interest claimed successfully. Your principal and interest have been transferred to your balance." xml:space="preserve">
    <value>Interest claimed successfully. Your principal and interest have been transferred to your balance.</value>
    <comment>Success message after claiming matured interest.</comment>
  </data>
  <data name="Interest can only be claimed for matured and active accounts." xml:space="preserve">
    <value>Interest can only be claimed for matured and active accounts.</value>
    <comment>Error message for invalid claim attempt.</comment>
  </data>
  <data name="You don't have permission to claim interest for this account or it doesn't exist." xml:space="preserve">
    <value>You don't have permission to claim interest for this account or it doesn't exist.</value>
    <comment>Error message for unauthorized claim attempt.</comment>
  </data>
  <data name="An error occurred while claiming interest. Please try again." xml:space="preserve">
    <value>An error occurred while claiming interest. Please try again.</value>
    <comment>General error message for claim interest action.</comment>
  </data>
  <data name="Are you sure?" xml:space="preserve">
    <value>Are you sure?</value>
    <comment>SweetAlert2 confirmation title</comment>
  </data>
  <data name="Are you sure you want to change the auto-renew setting?" xml:space="preserve">
    <value>Are you sure you want to change the auto-renew setting?</value>
    <comment>SweetAlert2 confirmation text for auto-renew</comment>
  </data>
  <data name="Enable Auto Renew" xml:space="preserve">
    <value>Enable Auto Renew</value>
    <comment>Button text for enabling auto renew</comment>
  </data>
  <data name="Disable Auto Renew" xml:space="preserve">
    <value>Disable Auto Renew</value>
    <comment>Button text for disabling auto renew</comment>
  </data>
  <data name="Auto-renew setting updated successfully." xml:space="preserve">
    <value>Auto-renew setting updated successfully.</value>
    <comment>Success message for auto-renew update</comment>
  </data>
  <data name="Announcements" xml:space="preserve">
    <value>Announcements</value>
    <comment>Main announcements section title</comment>
  </data>
  <data name="Announcement" xml:space="preserve">
    <value>Announcement</value>
    <comment>Single announcement title</comment>
  </data>
  <data name="New Announcement" xml:space="preserve">
    <value>New Announcement</value>
    <comment>Create new announcement button/title</comment>
  </data>
  <data name="Edit Announcement" xml:space="preserve">
    <value>Edit Announcement</value>
    <comment>Edit announcement button/title</comment>
  </data>
  <data name="Delete Announcement" xml:space="preserve">
    <value>Delete Announcement</value>
    <comment>Delete announcement button/title</comment>
  </data>
  <data name="Announcement Title" xml:space="preserve">
    <value>Announcement Title</value>
    <comment>Announcement title field label</comment>
  </data>
  <data name="Announcement Content" xml:space="preserve">
    <value>Announcement Content</value>
    <comment>Announcement content field label</comment>
  </data>
  <data name="Announcement Date" xml:space="preserve">
    <value>Announcement Date</value>
    <comment>Announcement date field label</comment>
  </data>
  <data name="Published" xml:space="preserve">
    <value>Published</value>
    <comment>Published status</comment>
  </data>
  <data name="Draft" xml:space="preserve">
    <value>Draft</value>
    <comment>Draft status</comment>
  </data>
  <data name="Publish Announcement" xml:space="preserve">
    <value>Publish Announcement</value>
    <comment>Publish announcement button</comment>
  </data>
  <data name="Save Announcement" xml:space="preserve">
    <value>Save Announcement</value>
    <comment>Save announcement button</comment>
  </data>
  <data name="Announcement List" xml:space="preserve">
    <value>Announcement List</value>
    <comment>Announcement list page title</comment>
  </data>
  <data name="No Announcements Found" xml:space="preserve">
    <value>No announcements found</value>
    <comment>Message when no announcements exist</comment>
  </data>
  <data name="Announcement Successfully Created" xml:space="preserve">
    <value>Announcement successfully created</value>
    <comment>Success message for announcement creation</comment>
  </data>
  <data name="Announcement Successfully Updated" xml:space="preserve">
    <value>Announcement successfully updated</value>
    <comment>Success message for announcement update</comment>
  </data>
  <data name="Announcement Successfully Deleted" xml:space="preserve">
    <value>Announcement successfully deleted</value>
    <comment>Success message for announcement deletion</comment>
  </data>
  <data name="Are You Sure Delete Announcement" xml:space="preserve">
    <value>Are you sure you want to delete this announcement?</value>
    <comment>Confirmation message for announcement deletion</comment>
  </data>
  <data name="Announcement Status" xml:space="preserve">
    <value>Announcement Status</value>
    <comment>Announcement status field label</comment>
  </data>
  <data name="Latest Announcements" xml:space="preserve">
    <value>Latest Announcements</value>
    <comment>Latest announcements section title</comment>
  </data>
  <data name="View All Announcements" xml:space="preserve">
    <value>View All Announcements</value>
    <comment>Link to view all announcements</comment>
  </data>
  <data name="Less than 1 day" xml:space="preserve">
    <value>Less than 1 day</value>
    <comment>Displayed when savings account has less than 1 day remaining</comment>
  </data>
  <data name="View Details" xml:space="preserve">
    <value>View Details</value>
    <comment>Button text to view detailed information</comment>
  </data>
  <data name="Current Earned Interest" xml:space="preserve">
    <value>Current Earned Interest</value>
    <comment>Current earned interest amount based on elapsed days</comment>
  </data>
  <data name="Projected Total Interest" xml:space="preserve">
    <value>Projected Total Interest</value>
    <comment>Total interest that will be earned at maturity</comment>
  </data>
  <data name="Projected Maturity Amount" xml:space="preserve">
    <value>Projected Maturity Amount</value>
    <comment>Total amount (principal + interest) at maturity</comment>
  </data>
</root>