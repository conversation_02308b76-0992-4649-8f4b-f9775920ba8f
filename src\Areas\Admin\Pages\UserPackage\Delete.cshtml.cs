using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels;

namespace RazeWinComTr.Areas.Admin.Pages.UserPackage;

public class DeleteModel : PageModel
{
    private readonly UserPackageService _userPackageService;
    private readonly PackageService _packageService;
    private readonly UserService _userService;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public DeleteModel(
        UserPackageService userPackageService,
        PackageService packageService,
        UserService userService,
        IStringLocalizer<SharedResource> localizer)
    {
        _userPackageService = userPackageService;
        _packageService = packageService;
        _userService = userService;
        _localizer = localizer;
    }

    public SweetAlert2Message? AlertMessage { get; set; }

    [BindProperty] public int Id { get; set; }

    public DbModel.UserPackage? Entity { get; set; }
    public string UserFullName { get; set; } = string.Empty;
    public string PackageName { get; set; } = string.Empty;
    public string? ErrorMessage { get; set; }

    public async Task<IActionResult> OnGetAsync(int id)
    {
        Id = id;
        Entity = await _userPackageService.GetByIdAsync(id);

        if (Entity == null) return NotFound();

        var user = await _userService.GetByIdAsync(Entity.UserId);
        if (user != null)
        {
            UserFullName = $"{user.Name} {user.Surname} ({user.Email})";
        }

        var package = await _packageService.GetByIdAsync(Entity.PackageId);
        if (package != null)
        {
            PackageName = package.Name;
        }

        ViewData["WarningTitle"] = _localizer["Warning"];
        ViewData["WarningRecord"] = _localizer["This record will be permanently deleted"];
        ViewData["WarningUnrecoverable"] = _localizer["This action is irreversible"];

        return Page();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        try
        {
            await _userPackageService.DeleteAsync(Id);

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Success"],
                Text = _localizer["Deletion was successful"],
                Icon = "success",
                RedirectUrl = "/Admin/UserPackage"
            };

            return Page();
        }
        catch (Exception ex)
        {
            ErrorMessage = ex.Message;
            Entity = await _userPackageService.GetByIdAsync(Id);
            
            if (Entity != null)
            {
                var user = await _userService.GetByIdAsync(Entity.UserId);
                if (user != null)
                {
                    UserFullName = $"{user.Name} {user.Surname} ({user.Email})";
                }

                var package = await _packageService.GetByIdAsync(Entity.PackageId);
                if (package != null)
                {
                    PackageName = package.Name;
                }
            }
            
            return Page();
        }
    }
}
