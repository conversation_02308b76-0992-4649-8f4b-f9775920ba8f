using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RazeWinComTr.Areas.Admin.DbModel
{
    [Table("WITHDRAWAL")]
    public class Withdrawal
    {
        [Key]
        [Column("ID")]
        public int Id { get; set; }

        [Required]
        [Column("USER_ID")]
        public int UserId { get; set; }

        [Required]
        [Column("FULL_NAME")]
        [StringLength(100)]
        public string FullName { get; set; } = null!;

        [Required]
        [Column("EMAIL")]
        [StringLength(100)]
        [EmailAddress]
        public string Email { get; set; } = null!;

        [Required]
        [Column("BALANCE", TypeName = "decimal(10,2)")]
        public decimal Balance { get; set; }

        [Required]
        [Column("WITHDRAWAL_AMOUNT", TypeName = "decimal(10,2)")]
        public decimal WithdrawalAmount { get; set; }

        [Required]
        [Column("ACCOUNT_HOLDER")]
        [StringLength(100)]
        public string AccountHolder { get; set; } = null!;

        [Required]
        [Column("IBAN")]
        [StringLength(100)]
        public string Iban { get; set; } = null!;

        [Column("STATUS")]
        public WithdrawalStatus Status { get; set; } = WithdrawalStatus.Pending;

        [Column("CR_DATE", TypeName = "datetime")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;
    }

    public enum WithdrawalStatus
    {
        Pending = 0,
        Approved = 1,
        Rejected = 2
    }
}
