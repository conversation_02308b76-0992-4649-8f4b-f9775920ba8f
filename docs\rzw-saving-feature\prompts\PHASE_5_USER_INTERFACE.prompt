# RZW Vadeli Hesap Sistemi - Faz 5: User Interface

## 🎯 AI Agent Görevi
RZW vadeli hesap sistemi için kullanıcı arayüzü oluştur. Mevcut Wallet sayfasını güncelle ve yeni RZW Savings sayfaları ekle. Responsive ve kullanıcı dostu tasarım yap.

## 📋 Detaylı Görevler

### 1. Wallet Sayfası Güncellemeleri

#### 1.1 Wallet ViewModel Güncelleme
**Dosya**: `src/Areas/Admin/ViewModels/Wallet/WalletViewModel.cs`

```csharp
public class WalletViewModel
{
    // Mevcut alanlar...
    public decimal Balance { get; set; } // Available Balance
    
    // YENİ ALANLAR
    public decimal LockedBalance { get; set; }
    public decimal TotalBalance => Balance + LockedBalance;
    
    // RZW özel alanlar
    public bool IsRzwToken { get; set; }
    public decimal? LockedInSavings { get; set; }
    public int? ActiveSavingsCount { get; set; }
    public List<SavingsLockInfo>? SavingsLocks { get; set; }
}
```

#### 1.2 Wallet Page Model Güncelleme
**Dosya**: `src/Areas/MyAccount/Pages/Wallet.cshtml.cs`

```csharp
public class WalletModel : PageModel
{
    private readonly IWalletService _walletService;
    private readonly RzwBalanceManagementService _rzwBalanceService;
    private readonly ITokenPriceService _tokenPriceService;

    public List<WalletBalanceInfo> WalletBalances { get; set; } = new();
    public RzwBalanceInfo? RzwBalance { get; set; }

    public async Task OnGetAsync()
    {
        var userId = GetCurrentUserId();
        WalletBalances = await _walletService.GetUserAllBalanceInfoAsync(userId);
        
        var rzwTokenId = await _tokenPriceService.GetRzwTokenIdAsync();
        var rzwWallet = WalletBalances.FirstOrDefault(w => w.CoinId == rzwTokenId);
        
        if (rzwWallet != null)
        {
            RzwBalance = await _rzwBalanceService.GetRzwBalanceInfoAsync(userId);
        }
    }
}
```

#### 1.3 Wallet View Güncelleme
**Dosya**: `src/Areas/MyAccount/Pages/Wallet.cshtml`

**RZW Özel Kart Tasarımı**:
- Available/Locked/Total balance gösterimi
- Aktif vadeli hesap sayısı
- "Vadeli Hesap Oluştur" butonu
- Responsive card layout

### 2. RZW Savings Sayfaları

#### 2.1 RZW Savings Index Sayfası
**Dosya**: `src/Areas/MyAccount/Pages/RzwSavings/Index.cshtml`

**Özellikler**:
- Aktif vadeli hesaplar listesi
- Toplam kilitli RZW miktarı
- Toplam kazanılan faiz
- Yeni vadeli hesap oluşturma linki
- Responsive table/card layout

#### 2.2 RZW Savings Create Sayfası
**Dosya**: `src/Areas/MyAccount/Pages/RzwSavings/Create.cshtml`

**Form Alanları**:
- Plan seçimi (dropdown)
- RZW miktarı (number input)
- Otomatik yenileme (checkbox)
- Tahmini kazanç gösterimi (JavaScript)

**Validasyonlar**:
- Plan seçimi zorunlu
- Minimum/maksimum miktar kontrolü
- Kullanılabilir bakiye kontrolü
- Client-side ve server-side validation

#### 2.3 RZW Savings Detail Sayfası
**Dosya**: `src/Areas/MyAccount/Pages/RzwSavings/Detail.cshtml`

**Özellikler**:
- Hesap bilgileri (tutar, vade, faiz oranı)
- Vade dolma tarihi countdown
- Kazanılan faiz geçmişi
- Erken çekim seçeneği
- İşlem geçmişi

### 3. ViewModels

#### 3.1 CreateRzwSavingsViewModel
**Dosya**: `src/Areas/Admin/ViewModels/RzwSavings/CreateRzwSavingsViewModel.cs`

```csharp
public class CreateRzwSavingsViewModel
{
    [Required]
    [Display(Name = "Savings Plan")]
    public int PlanId { get; set; }

    [Required]
    [Range(0.********, double.MaxValue)]
    [Display(Name = "RZW Amount")]
    public decimal RzwAmount { get; set; }

    [Display(Name = "Auto Renew")]
    public bool AutoRenew { get; set; }

    // Helper properties
    public List<RzwSavingsPlan> AvailablePlans { get; set; } = new();
    public decimal AvailableRzw { get; set; }
}
```

#### 3.2 RzwSavingsIndexViewModel
**Dosya**: `src/Areas/Admin/ViewModels/RzwSavings/RzwSavingsIndexViewModel.cs`

```csharp
public class RzwSavingsIndexViewModel
{
    public List<RzwSavingsAccount> ActiveSavings { get; set; } = new();
    public decimal TotalLockedRzw { get; set; }
    public decimal TotalEarnedInterest { get; set; }
    public decimal AvailableRzw { get; set; }
    public int ActiveSavingsCount { get; set; }
}
```

#### 3.3 RzwSavingsDetailViewModel
**Dosya**: `src/Areas/Admin/ViewModels/RzwSavings/RzwSavingsDetailViewModel.cs`

```csharp
public class RzwSavingsDetailViewModel
{
    public RzwSavingsAccount Account { get; set; } = null!;
    public List<RzwSavingsInterestPayment> InterestHistory { get; set; } = new();
    public decimal EstimatedDailyEarnings { get; set; }
    public int DaysUntilMaturity { get; set; }
    public bool CanWithdrawEarly { get; set; }
    public decimal EarlyWithdrawalPenalty { get; set; }
}
```

### 4. JavaScript Enhancements

#### 4.1 RZW Savings JavaScript
**Dosya**: `src/wwwroot/js/rzw-savings.js`

**Özellikler**:
- Plan seçimi değiştiğinde min/max değerleri güncelle
- Tahmini kazanç hesaplama
- Form validasyonu
- Vade dolma countdown timer
- AJAX işlemleri

```javascript
// Plan seçimi değiştiğinde
$('#PlanId').on('change', function() {
    const selectedOption = $(this).find('option:selected');
    const minAmount = selectedOption.data('min');
    const maxAmount = selectedOption.data('max');
    const rate = selectedOption.data('rate');
    
    $('#RzwAmount').attr('min', minAmount);
    if (maxAmount) {
        $('#RzwAmount').attr('max', maxAmount);
    }
    
    updateEstimatedEarnings();
});

// Tahmini kazanç hesaplama
function updateEstimatedEarnings() {
    const amount = parseFloat($('#RzwAmount').val()) || 0;
    const rate = parseFloat($('#PlanId option:selected').data('rate')) || 0;
    
    if (amount > 0 && rate > 0) {
        const dailyEarnings = amount * (rate / 365);
        $('#estimated-earnings').text(dailyEarnings.toFixed(8) + ' RZW/day');
    }
}

// Countdown timer
function startCountdown(maturityDate, elementId) {
    const countdownElement = document.getElementById(elementId);
    
    function updateCountdown() {
        const now = new Date().getTime();
        const maturity = new Date(maturityDate).getTime();
        const distance = maturity - now;
        
        if (distance > 0) {
            const days = Math.floor(distance / (1000 * 60 * 60 * 24));
            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            
            countdownElement.innerHTML = `${days}d ${hours}h ${minutes}m`;
        } else {
            countdownElement.innerHTML = window.t["Matured"];
        }
    }
    
    updateCountdown();
    setInterval(updateCountdown, 60000); // Update every minute
}
```

### 5. CSS Styling

#### 5.1 RZW Savings CSS
**Dosya**: `src/wwwroot/css/rzw-savings.css`

```css
.rzw-wallet-card {
    border: 2px solid #007bff;
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.1);
}

.rzw-wallet-card .card-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.savings-account-card {
    border-left: 4px solid #28a745;
    margin-bottom: 1rem;
}

.countdown-timer {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #dc3545;
}

.interest-history-table {
    font-size: 0.9rem;
}

.estimated-earnings {
    background-color: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 4px;
    padding: 0.5rem;
    margin-top: 0.5rem;
}
```

### 6. Localization Keys

#### 6.1 Türkçe Dil Anahtarları
**Dosya**: `src/Areas/Admin/Resources/SharedResource.tr.resx`

```xml
<data name="RZW Savings" xml:space="preserve">
    <value>RZW Vadeli Hesap</value>
</data>
<data name="Create Savings Account" xml:space="preserve">
    <value>Vadeli Hesap Oluştur</value>
</data>
<data name="Savings Plan" xml:space="preserve">
    <value>Vadeli Plan</value>
</data>
<data name="Available" xml:space="preserve">
    <value>Kullanılabilir</value>
</data>
<data name="Locked" xml:space="preserve">
    <value>Kilitli</value>
</data>
<data name="Active Savings" xml:space="preserve">
    <value>Aktif Vadeli Hesap</value>
</data>
<data name="Early Withdrawal" xml:space="preserve">
    <value>Erken Çekim</value>
</data>
<data name="Maturity Date" xml:space="preserve">
    <value>Vade Tarihi</value>
</data>
<data name="Interest Rate" xml:space="preserve">
    <value>Faiz Oranı</value>
</data>
<data name="Total Earned" xml:space="preserve">
    <value>Toplam Kazanç</value>
</data>
<data name="Estimated Daily Earnings" xml:space="preserve">
    <value>Tahmini Günlük Kazanç</value>
</data>
<data name="Days Until Maturity" xml:space="preserve">
    <value>Vadeye Kalan Gün</value>
</data>
<data name="Withdrawal Penalty" xml:space="preserve">
    <value>Çekim Cezası</value>
</data>
```

## ✅ Başarı Kriterleri
1. Wallet sayfası: RZW için özel kart gösterimi çalışıyor
2. Vadeli hesap listesi: Kullanıcının aktif hesapları listeleniyor
3. Yeni hesap oluşturma: Form validasyonu ile hesap oluşturuluyor
4. Hesap detayları: Detay sayfası ve işlem geçmişi görüntüleniyor
5. Responsive design: Mobil uyumlu tasarım
6. JavaScript: Form validasyonu ve UX iyileştirmeleri çalışıyor
7. Localization: Tüm metinler çevrilmiş

## 🔧 Test Senaryoları

### Test 1: Wallet Sayfası
- /MyAccount/Wallet sayfasına git
- RZW kartının özel gösterimini kontrol et
- Available/Locked/Total balance'ların doğru gösterildiğini kontrol et

### Test 2: Vadeli Hesap Oluşturma
- /MyAccount/RzwSavings/Create sayfasına git
- Form validasyonunu test et
- Farklı planlarla yeni hesap oluştur

### Test 3: Hesap Listesi
- /MyAccount/RzwSavings sayfasına git
- Aktif hesapların listelendiğini kontrol et
- Toplam istatistiklerin doğru olduğunu kontrol et

### Test 4: Responsive Design
- Mobil cihazda sayfaları test et
- Tablet görünümünü test et

## 📝 Önemli Notlar
- **Mevcut tasarımı koru**: Proje tasarım standartlarını takip et
- **Responsive**: Bootstrap kullanarak mobil uyumlu yap
- **Validation**: Client-side ve server-side validasyon ekle
- **UX**: Kullanıcı dostu arayüz tasarla
- **Performance**: Lazy loading ve pagination kullan
- **Security**: XSS ve CSRF koruması ekle
- **Accessibility**: ARIA etiketleri ve keyboard navigation

## 🎯 Sonuç Beklentisi
Bu faz sonunda:
- Kullanıcı arayüzü tamamlanmış olacak
- Wallet sayfası RZW için özelleştirilmiş olacak
- Vadeli hesap CRUD sayfaları hazır olacak
- Responsive ve kullanıcı dostu tasarım uygulanmış olacak
- JavaScript enhancements çalışır olacak

**Tahmini Süre**: 3-4 gün
**Bağımlılıklar**: Faz 1, 2, 3 ve 4 tamamlanmış olmalı
**Sonraki Faz**: Admin Panel & Testing
