namespace RazeWinComTr.Models;

/// <summary>
/// Comprehensive balance information for a user's wallet for a specific coin
/// </summary>
public class WalletBalanceInfo
{
    /// <summary>
    /// The user ID
    /// </summary>
    public int UserId { get; set; }
    
    /// <summary>
    /// The coin/token ID
    /// </summary>
    public int CoinId { get; set; }
    
    /// <summary>
    /// The coin/token code (e.g., "BTC", "RZW", "TRY")
    /// </summary>
    public string CoinCode { get; set; } = string.Empty;
    
    /// <summary>
    /// The coin/token display name
    /// </summary>
    public string CoinName { get; set; } = string.Empty;
    
    /// <summary>
    /// The coin/token icon URL
    /// </summary>
    public string IconUrl { get; set; } = string.Empty;
    
    /// <summary>
    /// Available balance (can be used for trading/withdrawals)
    /// </summary>
    public decimal AvailableBalance { get; set; }
    
    /// <summary>
    /// Locked balance (cannot be used for trading/withdrawals)
    /// </summary>
    public decimal LockedBalance { get; set; }
    
    /// <summary>
    /// Total balance (Available + Locked)
    /// </summary>
    public decimal TotalBalance => AvailableBalance + LockedBalance;
    
    /// <summary>
    /// Number of active savings accounts (RZW only)
    /// </summary>
    public int ActiveSavingsAccountCount { get; set; }

    // Computed properties

    /// <summary>
    /// Whether this wallet is for the RZW token
    /// </summary>
    public bool IsRzwToken => CoinCode == "RZW";

    /// <summary>
    /// Whether this wallet has any locked balance
    /// </summary>
    public bool HasLockedBalance => LockedBalance > 0;

    /// <summary>
    /// Whether this wallet has any savings accounts (RZW only)
    /// </summary>
    public bool HasSavingsAccounts => ActiveSavingsAccountCount > 0;
}
