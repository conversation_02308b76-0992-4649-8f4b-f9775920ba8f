using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.ViewModels.RzwSavings;

namespace RazeWinComTr.Areas.Admin.Helpers;

/// <summary>
/// Helper class for generating localized display text for RZW Savings features
/// </summary>
public static class RzwSavingsDisplayHelper
{
    /// <summary>
    /// Generates localized term display text for RZW Savings plans and accounts
    /// </summary>
    /// <param name="localizer">The string localizer instance</param>
    /// <param name="termType">The term type (Daily, Monthly, Yearly)</param>
    /// <param name="termDuration">The term duration in days</param>
    /// <returns>Localized term display text</returns>
    public static string GetLocalizedTermDisplayText(IStringLocalizer localizer, string termType, int termDuration)
    {
        return termType switch
        {
            RzwSavingsTermType.Daily => GetLocalizedDayText(localizer, termDuration),
            RzwSavingsTermType.Monthly => GetLocalizedMonthText(localizer, termDuration / 30),
            RzwSavingsTermType.Yearly => GetLocalizedYearText(localizer, termDuration / 365),
            _ => GetLocalizedDayText(localizer, termDuration)
        };
    }

    /// <summary>
    /// Generates localized day text with proper pluralization
    /// </summary>
    /// <param name="localizer">The string localizer instance</param>
    /// <param name="days">Number of days</param>
    /// <returns>Localized day text</returns>
    private static string GetLocalizedDayText(IStringLocalizer localizer, int days)
    {
        if (days == 1)
        {
            return $"{days} {localizer["Day"]}";
        }
        else
        {
            return $"{days} {localizer["Days"]}";
        }
    }

    /// <summary>
    /// Generates localized month text with proper pluralization
    /// </summary>
    /// <param name="localizer">The string localizer instance</param>
    /// <param name="months">Number of months</param>
    /// <returns>Localized month text</returns>
    private static string GetLocalizedMonthText(IStringLocalizer localizer, int months)
    {
        if (months == 1)
        {
            return $"{months} {localizer["Month"]}";
        }
        else
        {
            return $"{months} {localizer["Months"]}";
        }
    }

    /// <summary>
    /// Generates localized year text with proper pluralization
    /// </summary>
    /// <param name="localizer">The string localizer instance</param>
    /// <param name="years">Number of years</param>
    /// <returns>Localized year text</returns>
    private static string GetLocalizedYearText(IStringLocalizer localizer, int years)
    {
        if (years == 1)
        {
            return $"{years} {localizer["Year"]}";
        }
        else
        {
            return $"{years} {localizer["Years"]}";
        }
    }
}

/// <summary>
/// Extension methods for RZW Savings view models to provide localized display text
/// </summary>
public static class RzwSavingsViewModelExtensions
{
    /// <summary>
    /// Gets localized term display text for RzwSavingsAccountViewModel
    /// </summary>
    /// <param name="viewModel">The account view model</param>
    /// <param name="localizer">The string localizer instance</param>
    /// <returns>Localized term display text</returns>
    public static string GetLocalizedTermDisplayText(this RzwSavingsAccountViewModel viewModel, IStringLocalizer localizer)
    {
        return RzwSavingsDisplayHelper.GetLocalizedTermDisplayText(localizer, viewModel.TermType, viewModel.TermDuration);
    }

    /// <summary>
    /// Gets localized term display text for RzwSavingsPlanViewModel
    /// </summary>
    /// <param name="viewModel">The plan view model</param>
    /// <param name="localizer">The string localizer instance</param>
    /// <returns>Localized term display text</returns>
    public static string GetLocalizedTermDisplayText(this RzwSavingsPlanViewModel viewModel, IStringLocalizer localizer)
    {
        return RzwSavingsDisplayHelper.GetLocalizedTermDisplayText(localizer, viewModel.TermType, viewModel.TermDuration);
    }
}
