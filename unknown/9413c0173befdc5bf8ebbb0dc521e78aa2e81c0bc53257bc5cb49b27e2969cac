{
  "useHttpsRedirection": true,
  "useExceptionHandler": false,
  "useDeveloperExceptionPage": true,
  "LoggingDirectory": "c:\\razewin-data\\app-logs",
  "Serilog": {
    "MinimumLevel": {
      "Default": "Debug",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning"
      }
    }
  },
  "Tus": {
    "StorageDiskPath": "c:\\razewin-data\\app-data\\tusfiles",
    "MaxRequestBodySize": 2097152000, // 2000 MB
    "EnableExpiration": true, // If false, clean-up services will be disabled
    "AbsoluteExpiration": true,
    "ExpirationInSeconds": 7200 // 2 hours
  },
  "FileStoragePath": "c:\\razewin-data\\app-data\\files\\",
  "FileStoragePathAutoCreate": true,
  "ConnectionStrings": {
    "SQLite": "Data Source=c:\\razewin-data\\app-data\\sqlite-data\\Database.db"
  },
  "DetailedErrors": true,
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "Microsoft.AspNetCore": "Debug"
    },
    "EventLog": {
      "LogLevel": {
        "Default": "Debug"
      }
    }
  },
  "AllowedHosts": "*"
}
