﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RazeWinComTr.Areas.Admin.DbModel
{
    [Table("SETTING")]
    public class Setting
    {
        [Key]
        [Column("ID")]
        public int Id { get; set; }

        [Required]
        [Column("KEY")]
        [StringLength(255)]
        public string Key { get; set; } = null!;

        [Column("VALUE")]
        public string? Value { get; set; }

        [Column("DESCRIPTION")]
        [StringLength(255)]
        public string Description { get; set; } = null!;

        [Column("GROUP")]
        [StringLength(50)]
        public string Group { get; set; } = null!;

        [Column("ORDER")]
        public int Order { get; set; } = 0;

        [Column("CR_DATE", TypeName = "datetime")]
        public DateTime CrDate { get; set; } = DateTime.UtcNow;
    }
}