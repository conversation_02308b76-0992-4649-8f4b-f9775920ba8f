﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace RazeWinComTr.Areas.Admin.DbModel;

[Table("ROLE")]
public partial class Role
{
    [Key]
    [Column("ROLE_ID")]
    public int RoleId { get; set; }

    [Column("ROLE_NAME")]
    public string RoleName { get; set; } = null!;

    [InverseProperty("Role")]
    public virtual ICollection<UserRoleRelation> UserRoleRelations { get; set; } = new List<UserRoleRelation>();
}