using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using RazeWinComTr.Areas.Admin.Constants;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;

namespace RazeWinComTr.BackgroundServices;

/// <summary>
/// Background service for updating market pairs from Bitexen API
/// </summary>
public class BitexenBackgroundService : CryptoExchangeBackgroundService
{
    public BitexenBackgroundService(
        ILogger<BitexenBackgroundService> logger,
        IServiceScopeFactory serviceScopeFactory,
        IOptions<CryptoExchangeBackgroundServiceOptions> options)
        : base(logger, serviceScopeFactory, options, ApiServiceNames.Bitexen)
    {
    }

    protected override async Task UpdateMarketPairsAsync(CancellationToken stoppingToken)
    {
        // _logger.LogDebug("Updating market pairs from Bitexen API");

        try
        {
            using var scope = _serviceScopeFactory.CreateScope();
            var bitexenService = scope.ServiceProvider.GetRequiredService<BitexenService>();

            // Get ticker data from Bitexen API
            var tickerResponse = await bitexenService.GetTickerAsync(stoppingToken);
            if (tickerResponse == null || !tickerResponse.IsSuccess)
            {
                _logger.LogWarning("Failed to get ticker data from Bitexen API: {ErrorMessage}",
                    tickerResponse?.GetErrorMessage() ?? "Unknown error");
                return;
            }

            // Update market pairs in the database
            await bitexenService.UpdateBuySellPricesAsync(tickerResponse);

            // Record successful execution
            BackgroundServiceStatus.RecordSuccess(_serviceName);

            // _logger.LogInformation("Successfully updated market pairs from Bitexen API at {Timestamp}", DateTime.UtcNow);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating market pairs from Bitexen API");

            // Record failure
            BackgroundServiceStatus.RecordFailure(_serviceName, ex.Message);

            throw; // Rethrow to be handled by the base class
        }
    }
}
