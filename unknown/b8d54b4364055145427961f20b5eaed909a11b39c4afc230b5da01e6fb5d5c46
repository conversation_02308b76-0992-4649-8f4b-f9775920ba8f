﻿using System.Security.Cryptography;
using System.Text;

namespace RazeWinComTr.Areas.Admin.Helpers;

public static class HashHelper
{
    public static string getHash(string message)
    {
        // Compute the hash
        using var sha256 = SHA256.Create();
        var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(message));
        var hashString = Convert.ToBase64String(hashBytes);
        return hashString;
    }

    public static async Task<string> ComputeMd5HashAsync(IFormFile file)
    {
        using (var md5 = MD5.Create())
        {
            using (var stream = file.OpenReadStream())
            {
                var hash = await md5.ComputeHashAsync(stream);
                return BitConverter.ToString(hash).Replace("-", "").ToLowerInvariant();
            }
        }
    }

    public static async Task<string> CalculateMD5FromStreamAsync(Stream stream)
    {
        // Stream'in pozisyonunu sıfırla (başlangıca al)
        if (stream.CanSeek) stream.Position = 0;

        // MD5 nesnesi olu<PERSON>tur
        using (var md5 = MD5.Create())
        {
            // Hash hesapla
            var hash = await md5.ComputeHashAsync(stream);

            // Hash'i hexadecimal string'e çevir
            var sb = new StringBuilder();
            foreach (var b in hash) sb.Append(b.ToString("x2"));

            return sb.ToString();
        }
    }
}