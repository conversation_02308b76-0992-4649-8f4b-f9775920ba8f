﻿﻿using System.Net.Http.Headers;
using System.Text;
using Microsoft.Extensions.Logging;
using RazeWinComTr.Areas.Admin.Models;

namespace RazeWinComTr.Areas.Admin.Helpers
{
    public class MailGunDirectEmailHelper : IEmailHelper
    {
        private readonly ILogger<MailGunDirectEmailHelper> _logger;
        private readonly IHttpClientFactory _httpClientFactory;
        
        // Hardcoded Mailgun configuration values
        private const string ApiKey = "**************************************************";
        private const string Domain = "sandbox36cc057194cb4c70b0bf16a93c5d688c.mailgun.org";
        private const string ApiBaseUrl = "https://api.mailgun.net";
        private const string SenderEmail = "<EMAIL>";
        private const string SenderName = "Mailgun Sandbox";

        public MailGunDirectEmailHelper(
            ILogger<MailGunDirectEmailHelper> logger,
            IHttpClientFactory httpClientFactory)
        {
            _logger = logger;
            _httpClientFactory = httpClientFactory;
        }

        /// <summary>
        /// Gets the SMTP settings (not used for Mailgun API implementation)
        /// </summary>
        /// <returns>Default SMTP settings</returns>
        public Task<SmtpSettings> GetSmtpSettingsAsync()
        {
            // This method is not relevant for Mailgun API implementation
            // but we need to implement it as part of the interface
            return Task.FromResult(new SmtpSettings
            {
                Host = "api.mailgun.net",
                Port = 587,
                Username = "api",
                Password = ApiKey,
                Security = "tls",
                SenderEmail = SenderEmail,
                SenderName = SenderName
            });
        }

        /// <summary>
        /// Sends an email message using Mailgun API
        /// </summary>
        /// <param name="message">The email message to send</param>
        /// <returns>True if the email was sent successfully, false otherwise</returns>
        public async Task<bool> SendEmailAsync(EmailMessage message)
        {
            try
            {
                // Validate message
                if (message.ToAddresses.Count == 0)
                {
                    _logger.LogError("Email message must have at least one recipient.");
                    return false;
                }

                // Create HttpClient with basic authentication
                var client = _httpClientFactory.CreateClient();
                var authToken = Convert.ToBase64String(Encoding.ASCII.GetBytes($"api:{ApiKey}"));
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", authToken);

                // Create form content
                var formContent = new MultipartFormDataContent();

                // Add sender
                var fromValue = $"{SenderName} <{SenderEmail}>";
                formContent.Add(new StringContent(fromValue), "from");

                // Add recipients
                foreach (var to in message.ToAddresses)
                {
                    var toValue = string.IsNullOrEmpty(to.Name)
                        ? to.Address
                        : $"{to.Name} <{to.Address}>";
                    formContent.Add(new StringContent(toValue), "to");
                }

                // Add CC recipients
                foreach (var cc in message.CcAddresses)
                {
                    var ccValue = string.IsNullOrEmpty(cc.Name)
                        ? cc.Address
                        : $"{cc.Name} <{cc.Address}>";
                    formContent.Add(new StringContent(ccValue), "cc");
                }

                // Add BCC recipients
                foreach (var bcc in message.BccAddresses)
                {
                    var bccValue = string.IsNullOrEmpty(bcc.Name)
                        ? bcc.Address
                        : $"{bcc.Name} <{bcc.Address}>";
                    formContent.Add(new StringContent(bccValue), "bcc");
                }

                // Add subject
                formContent.Add(new StringContent(message.Subject), "subject");

                // Add body
                if (message.IsHtml)
                {
                    formContent.Add(new StringContent(message.Body), "html");
                }
                else
                {
                    formContent.Add(new StringContent(message.Body), "text");
                }

                // Add attachments
                foreach (var attachment in message.Attachments)
                {
                    var fileContent = new ByteArrayContent(attachment.Content);
                    fileContent.Headers.ContentType = new MediaTypeHeaderValue(attachment.ContentType);
                    formContent.Add(fileContent, "attachment", attachment.FileName);
                }

                // Send the request
                var response = await client.PostAsync($"{ApiBaseUrl}/v3/{Domain}/messages", formContent);

                // Check response
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation("Email sent successfully via Mailgun API to {Recipients}. Response: {Response}", 
                        string.Join(", ", message.ToAddresses.Select(a => a.Address)), 
                        responseContent);
                    return true;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to send email via Mailgun API. Status: {StatusCode}, Error: {Error}", 
                        response.StatusCode, 
                        errorContent);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending email via Mailgun API: {ErrorMessage}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Sends a simple email message to a single recipient
        /// </summary>
        /// <param name="to">Recipient email address</param>
        /// <param name="subject">Email subject</param>
        /// <param name="body">Email body</param>
        /// <param name="isHtml">Whether the body is HTML</param>
        /// <returns>True if the email was sent successfully, false otherwise</returns>
        public async Task<bool> SendSimpleEmailAsync(string to, string subject, string body, bool isHtml = true)
        {
            var message = new EmailMessage
            {
                Subject = subject,
                Body = body,
                IsHtml = isHtml,
                ToAddresses = new List<EmailAddress> { new EmailAddress(to) }
            };

            return await SendEmailAsync(message);
        }

        /// <summary>
        /// Sends a simple email message to multiple recipients
        /// </summary>
        /// <param name="to">List of recipient email addresses</param>
        /// <param name="subject">Email subject</param>
        /// <param name="body">Email body</param>
        /// <param name="isHtml">Whether the body is HTML</param>
        /// <returns>True if the email was sent successfully, false otherwise</returns>
        public async Task<bool> SendSimpleEmailAsync(List<string> to, string subject, string body, bool isHtml = true)
        {
            var message = new EmailMessage
            {
                Subject = subject,
                Body = body,
                IsHtml = isHtml,
                ToAddresses = to.Select(email => new EmailAddress(email)).ToList()
            };

            return await SendEmailAsync(message);
        }
    }
}
