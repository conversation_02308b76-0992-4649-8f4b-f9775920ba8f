﻿@page
@model RazeWinComTr.Pages.Blog.HeatMapModel
@{
}

<div class="fw pageHeaderAll cover wow fadeInUp"
     style="background-image: url(/public/front/fxyatirim/assets/images/page-bg.jpg); visibility: visible; animation-name: fadeInUp;">
    <div class="container">
        <div class="fw pageHeader">
            <ul class="sul">
                <li class="icon"><span class="iconX"><i class="flaticon-star"></i></span></li>
                <li class="title">
                    <h1 class="titleX">Isı Haritası</h1>
                </li>
                <li class="desc">Isı Haritası</li>
            </ul>
        </div>
    </div>
</div>
<div class="container">
    <div class="fw pageAll">
        <div class="fw page contactPage">
            <div class="wow fadeInLeft contactPageLeft" style="padding:40px;">
                <div class="fw pageLeftBox">
                    <div class="fw simpleContentTitle">
                        <h2 class="title">Isı Haritası</h2>
                    </div>
                    <div class="fw simpleContent">
                        <br><br>
                        <!-- TradingView Widget BEGIN -->
                        <div class="tradingview-widget-container">
                            <div class="tradingview-widget-container__widget"></div>
                            <div class="tradingview-widget-copyright"><a
                                    href="https://tr.tradingview.com/markets/currencies/forex-heat-map/" rel="noopener"
                                    target="_blank"><span class="blue-text">FinanceTR ısı haritası</span></a></div>
                            <script type="text/javascript"
                                    src="../../s3.tradingview.com/external-embedding/embed-widget-forex-heat-map.js"
                                    async>
                                {
                                    "colorTheme": "dark",
                                    "currencies": [
                                    "EUR",
                                    "USD",
                                    "JPY",
                                    "GBP",
                                    "CHF",
                                    "AUD",
                                    "CAD",
                                    "NZD",
                                    "CNY"
                                ],
                                    "height": "600",
                                    "isTransparent": false,
                                    "locale": "tr",
                                    "width": "1050"
                                }
                            </script>
                        </div>
                        <!-- TradingView Widget END -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
