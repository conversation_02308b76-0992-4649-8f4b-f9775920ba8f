@page
@using Microsoft.Extensions.Localization
@model RazeWinComTr.Areas.Admin.Pages.Package.IndexModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["Packages"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["Packages"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item active">@L["Packages"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <a id="btnCreateNew" asp-page="Create" class="btn btn-success" role="button"
                   aria-label="@($"{L["Create a New"]} {L["Package"]}")" title="@($"{L["Create a New"]} {L["Package"]}")">
                    <i class="fas fa-plus fa-2x"></i>
                </a>
                <h3 class="card-title float-right">@L["Count"]: @(Model.Packages.Count)</h3>
                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle"></i> @L["Note"]: @L["When a user purchases a package, they will receive RZW tokens equal to the package price."]
                </div>
            </div>
            <div class="card-body">
                <table class="table table-bordered table-striped datatable">
                    <thead>
                    <tr>
                        <th>@L["Name"]</th>
                        <th>@L["Price"]</th>
                        <th>@L["Description"]</th>
                        <th>@L["Invite Limit"]</th>
                        <th>@L["Earnings Cap"]</th>
                        <th>@L["Status"]</th>
                        <th>@L["Order"]</th>
                        <th style="width: 150px">@L["Actions"]</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach (var item in Model.Packages)
                    {
                        <tr>
                            <td>@item.Name</td>
                            <td>@item.Price.ToString("N8")</td>
                            <td>@item.Description</td>
                            <td>@(item.InviteLimit.HasValue ? item.InviteLimit.Value.ToString() : L["Unlimited"])</td>
                            <td>@(item.EarningsCap.HasValue ? item.EarningsCap.Value.ToString("N8") : L["Unlimited"])</td>
                            <td>@(item.IsActive ? L["Active"] : L["Inactive"])</td>
                            <td>@item.Order</td>
                            <td>
                                <a href="/Admin/Package/Edit?id=@item.Id" class="btn btn-info btn-sm">
                                    <i class="fas fa-pencil-alt"></i>
                                </a>
                                <a href="/Admin/Package/Delete?id=@item.Id" class="btn btn-danger btn-sm">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </td>
                        </tr>
                    }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>
