# RZW Vadeli Hesap Si<PERSON> - Faz 4: Background Services

## 🎯 AI Agent Görevi
RZW vadeli hesap sistemi için otomatik faiz ödemeleri ve vade kontrolleri yapan background service oluştur. Performanslı ve güvenilir batch processing implement et.

## 📋 Detaylı Görevler

### 1. RzwSavingsBackgroundService Oluştur
**Dosya**: `src/BackgroundServices/RzwSavingsBackgroundService.cs`

#### Base Class ve Dependencies
- Base: `BackgroundService`
- IServiceProvider (scoped service'ler için)
- ILogger<RzwSavingsBackgroundService>
- IConfiguration (çalışma saatleri için)

#### Ana Özellikler
```csharp
public class RzwSavingsBackgroundService : BackgroundService
{
    private readonly TimeSpan _processingInterval = TimeSpan.FromHours(1); // Her saat kontrol
    private readonly TimeSpan _processingTime = TimeSpan.FromHours(2); // Gece 02:00
    
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await WaitForProcessingTime(stoppingToken);
                await ProcessDailyOperationsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in RZW savings background processing");
            }
            
            await Task.Delay(_processingInterval, stoppingToken);
        }
    }
}
```

### 2. Ana İşlem Metotları

#### ProcessDailyOperationsAsync
```csharp
private async Task ProcessDailyOperationsAsync()
{
    using var scope = _serviceProvider.CreateScope();
    var interestService = scope.ServiceProvider.GetRequiredService<RzwSavingsInterestService>();
    var savingsService = scope.ServiceProvider.GetRequiredService<RzwSavingsService>();

    // 1. Günlük faiz ödemeleri
    var (processedAccounts, totalInterest) = await interestService.ProcessDailyInterestAsync();
    _logger.LogInformation("Daily interest processed. Accounts: {Accounts}, Total: {Total}", 
        processedAccounts, totalInterest);

    // 2. Vade dolma kontrolleri
    var maturedAccounts = await savingsService.GetMaturedAccountsAsync();
    var processedMaturity = 0;
    
    foreach (var account in maturedAccounts)
    {
        var result = await savingsService.ProcessMaturityAsync(account.Id);
        if (result.Success) processedMaturity++;
    }
    
    _logger.LogInformation("Maturity processed. Accounts: {Accounts}", processedMaturity);
}
```

#### Zaman Kontrolü
```csharp
private async Task WaitForProcessingTime(CancellationToken cancellationToken)
{
    var now = DateTime.Now;
    var targetTime = now.Date.Add(_processingTime);
    
    // Eğer hedef saat geçmişse, yarın aynı saate ayarla
    if (now > targetTime)
        targetTime = targetTime.AddDays(1);
    
    var delay = targetTime - now;
    if (delay > TimeSpan.Zero)
    {
        _logger.LogInformation("Waiting until {TargetTime} for daily processing", targetTime);
        await Task.Delay(delay, cancellationToken);
    }
}
```

### 3. Error Handling ve Batch Processing

#### Güvenli İşlem Metodu
```csharp
private async Task<bool> ProcessAccountSafelyAsync(RzwSavingsAccount account, 
    Func<RzwSavingsAccount, Task<bool>> processor)
{
    try
    {
        return await processor(account);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error processing account {AccountId} for user {UserId}", 
            account.Id, account.UserId);
        return false;
    }
}
```

#### Batch Processing
```csharp
private async Task ProcessAccountsBatchAsync<T>(IEnumerable<T> accounts, 
    Func<T, Task<bool>> processor, int batchSize = 100)
{
    var batches = accounts.Chunk(batchSize);
    
    foreach (var batch in batches)
    {
        var tasks = batch.Select(account => ProcessAccountSafelyAsync(account, processor));
        await Task.WhenAll(tasks);
        
        // Batch'ler arası kısa bekleme
        await Task.Delay(TimeSpan.FromSeconds(1));
    }
}
```

### 4. Configuration Settings
**Dosya**: `src/appsettings.json`

```json
{
  "RzwSavings": {
    "BackgroundService": {
      "ProcessingHour": 2,
      "ProcessingIntervalMinutes": 60,
      "BatchSize": 100,
      "EnableProcessing": true
    }
  }
}
```

### 5. Service Registration
**Dosya**: `src/Program.cs`

```csharp
// Background Services
builder.Services.AddHostedService<RzwSavingsBackgroundService>();

// Configuration binding
builder.Services.Configure<RzwSavingsBackgroundServiceOptions>(
    builder.Configuration.GetSection("RzwSavings:BackgroundService"));
```

### 6. Configuration Options Class
**Dosya**: `src/Areas/Admin/Services/Options/RzwSavingsBackgroundServiceOptions.cs`

```csharp
public class RzwSavingsBackgroundServiceOptions
{
    public int ProcessingHour { get; set; } = 2;
    public int ProcessingIntervalMinutes { get; set; } = 60;
    public int BatchSize { get; set; } = 100;
    public bool EnableProcessing { get; set; } = true;
}
```

### 7. Monitoring ve Health Check

#### Health Check Implementation
```csharp
public class RzwSavingsHealthCheck : IHealthCheck
{
    private readonly AppDbContext _context;

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Pending interest payments kontrolü
            var pendingCount = await _context.RzwSavingsAccounts
                .Where(s => s.Status == RzwSavingsStatus.Active && 
                           (s.LastInterestDate == null || 
                            s.LastInterestDate.Value.Date < DateTime.UtcNow.Date))
                .CountAsync(cancellationToken);

            // Overdue maturity kontrolü
            var overdueCount = await _context.RzwSavingsAccounts
                .Where(s => s.Status == RzwSavingsStatus.Active && 
                           s.MaturityDate <= DateTime.UtcNow)
                .CountAsync(cancellationToken);

            var data = new Dictionary<string, object>
            {
                ["PendingInterestPayments"] = pendingCount,
                ["OverdueMaturityAccounts"] = overdueCount
            };

            if (pendingCount > 1000 || overdueCount > 100)
            {
                return HealthCheckResult.Degraded("High pending operations", data: data);
            }

            return HealthCheckResult.Healthy("RZW Savings system is healthy", data: data);
        }
        catch (Exception ex)
        {
            return HealthCheckResult.Unhealthy("RZW Savings system check failed", ex);
        }
    }
}
```

## ✅ Başarı Kriterleri
1. Background service çalışıyor: Uygulama başladığında service aktif
2. Günlük faiz ödemeleri: Faizler otomatik hesaplanıp ödeniyor
3. Vade dolma kontrolleri: Vadesi dolan hesaplar otomatik kapatılıyor
4. Error handling: Hatalar loglanıyor, sistem durmuyor
5. Performance: Batch processing ile performanslı çalışıyor
6. Logging: Detaylı log kayıtları oluşuyor
7. Configuration: Ayarlar configuration'dan okunuyor
8. Health check: Sistem sağlığı izlenebiliyor

## 🔧 Test Senaryoları

### Test 1: Background Service Başlatma
```bash
dotnet run
# Beklenen: Service başlıyor, log'da "RzwSavingsBackgroundService started" mesajı
```

### Test 2: Manuel Faiz Ödeme Testi
```csharp
// Test database'de vadeli hesap oluştur
var account = new RzwSavingsAccount { /* test data */ };
// Background service'in işlemesini bekle veya manuel tetikle
// Faiz ödemesinin yapıldığını kontrol et
```

### Test 3: Vade Dolma Testi
```csharp
// Test database'de vadesi geçmiş hesap oluştur
var expiredAccount = new RzwSavingsAccount 
{ 
    MaturityDate = DateTime.UtcNow.AddDays(-1),
    Status = RzwSavingsStatus.Active
};
// Background service'in otomatik kapatmasını kontrol et
```

### Test 4: Error Handling Testi
```csharp
// Hatalı veri ile test et
// Service'in durmamasını kontrol et
// Error log'larının oluştuğunu kontrol et
```

### Test 5: Performance Testi
```csharp
// 1000+ hesap ile test et
// İşlem süresinin makul olduğunu kontrol et
// Memory usage'ın stabil olduğunu kontrol et
```

## 📝 Önemli Notlar
- **Gece saatleri**: Faiz ödemeleri gece 02:00'da yapılır
- **Scoped services**: Background service'de scoped service'ler için scope oluştur
- **Batch processing**: Performans için toplu işlem yap
- **Error isolation**: Bir hesaptaki hata diğerlerini etkilemez
- **Logging**: Detaylı log kayıtları tut
- **Configuration**: Ayarları configuration'dan oku
- **Graceful shutdown**: CancellationToken'ı doğru kullan
- **Health monitoring**: Sistem sağlığını izle

## 🎯 Sonuç Beklentisi
Bu faz sonunda:
- Background service çalışır durumda olacak
- Otomatik faiz ödemeleri yapılacak
- Vade dolma kontrolleri otomatik çalışacak
- Sistem monitoring ve health check hazır olacak
- Performance optimizasyonları uygulanmış olacak

**Tahmini Süre**: 1-2 gün
**Bağımlılıklar**: Faz 1, 2 ve 3 tamamlanmış olmalı
**Sonraki Faz**: User Interface
