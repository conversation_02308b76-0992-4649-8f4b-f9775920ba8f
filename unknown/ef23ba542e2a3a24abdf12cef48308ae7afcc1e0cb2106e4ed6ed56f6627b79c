using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels;

namespace RazeWinComTr.Areas.Admin.Pages.Setting;

public class EditModel : PageModel
{
    private readonly SettingService _settingService;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public EditModel(
        SettingService settingService,
        IStringLocalizer<SharedResource> localizer)
    {
        _settingService = settingService;
        _localizer = localizer;
    }

    public SweetAlert2Message? AlertMessage { get; set; }

    [BindProperty] public SettingEditViewModel Entity { get; set; } = new();

    public string? ErrorMessage { get; set; }

    public async Task<IActionResult> OnGetAsync(int id)
    {
        var entity = await _settingService.GetByIdAsync(id);

        if (entity == null) return NotFound();

        Entity = new SettingEditViewModel
        {
            Id = entity.Id,
            Key = entity.Key,
            Value = entity.Value,
            Description = entity.Description,
            Group = entity.Group,
            Order = entity.Order
        };

        return Page();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        try
        {
            if (!ModelState.IsValid) return Page();
            var userId = User.GetClaimUserId();
            if (!userId.HasValue) return Unauthorized();

            var entity = await _settingService.GetByIdAsync(Entity.Id);
            if (entity == null) return NotFound();

            // Check if key is changed and if a setting with the new key already exists
            if (entity.Key != Entity.Key)
            {
                var existingSetting = await _settingService.GetByKeyAsync(Entity.Key);
                if (existingSetting != null && existingSetting.Id != Entity.Id)
                {
                    ModelState.AddModelError("Entity.Key", _localizer["A setting with this key already exists"]);
                    return Page();
                }
            }

            entity.Key = Entity.Key;
            entity.Value = Entity.Value;
            entity.Description = Entity.Description ?? string.Empty;
            entity.Group = Entity.Group;
            entity.Order = Entity.Order;

            await _settingService.UpdateAsync(entity);

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Success"],
                Text = _localizer["Successfully updated"],
                Icon = "success",
                RedirectUrl = "/Admin/Setting"
            };

            return Page();
        }
        catch (Exception ex)
        {
            ErrorMessage = ex.Message;
            return Page();
        }
    }
}

public class SettingEditViewModel
{
    public int Id { get; set; }
    public string Key { get; set; } = null!;
    public string? Value { get; set; }
    public string? Description { get; set; }
    public string Group { get; set; } = null!;
    public int Order { get; set; }
}
