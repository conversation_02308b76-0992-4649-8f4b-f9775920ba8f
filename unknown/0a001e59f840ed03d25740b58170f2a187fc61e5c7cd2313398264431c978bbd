using System.Globalization;

namespace RazeWinComTr.Areas.Admin.Helpers
{
    public static class DateTimeFormatHelper
    {
        /// <summary>
        /// Formats a DateTime value for display in the UI, converting from UTC to local time
        /// </summary>
        /// <param name="dateTime">The UTC DateTime value to format</param>
        /// <returns>Formatted string in dd.MM.yyyy HH:mm format</returns>
        public static string FormatForDisplay(DateTime dateTime)
        {
            return dateTime.ToLocalTime().ToString("dd.MM.yyyy HH:mm", CultureInfo.InvariantCulture);
        }

        /// <summary>
        /// Formats a DateTime value for display in the UI, converting from UTC to local time
        /// </summary>
        /// <param name="dateTime">The nullable UTC DateTime value to format</param>
        /// <returns>Formatted string in dd.MM.yyyy HH:mm format, or empty string if null</returns>
        public static string FormatForDisplay(DateTime? dateTime)
        {
            if (!dateTime.HasValue)
                return string.Empty;
            return FormatForDisplay(dateTime.Value);
        }

        /// <summary>
        /// Formats a DateTime value for HTML datetime-local input
        /// </summary>
        /// <param name="dateTime">The UTC DateTime value to format</param>
        /// <returns>Formatted string in yyyy-MM-ddTHH:mm format</returns>
        public static string FormatForInput(DateTime dateTime)
        {
            return dateTime.ToLocalTime().ToString("yyyy-MM-ddTHH:mm", CultureInfo.InvariantCulture);
        }

        /// <summary>
        /// Formats a DateTime value for HTML datetime-local input
        /// </summary>
        /// <param name="dateTime">The nullable UTC DateTime value to format</param>
        /// <returns>Formatted string in yyyy-MM-ddTHH:mm format, or empty string if null</returns>
        public static string FormatForInput(DateTime? dateTime)
        {
            if (!dateTime.HasValue)
                return string.Empty;
            return FormatForInput(dateTime.Value);
        }

        /// <summary>
        /// Converts a local DateTime to UTC for storage
        /// </summary>
        /// <param name="localDateTime">The local DateTime value to convert</param>
        /// <returns>UTC DateTime value</returns>
        public static DateTime ConvertToUtc(DateTime localDateTime)
        {
            if (localDateTime.Kind == DateTimeKind.Utc)
                return localDateTime;
            return localDateTime.ToUniversalTime();
        }

        /// <summary>
        /// Converts a local DateTime to UTC for storage
        /// </summary>
        /// <param name="localDateTime">The nullable local DateTime value to convert</param>
        /// <returns>Nullable UTC DateTime value</returns>
        public static DateTime? ConvertToUtc(DateTime? localDateTime)
        {
            if (!localDateTime.HasValue)
                return null;
            return ConvertToUtc(localDateTime.Value);
        }

        /// <summary>
        /// Parses a datetime-local input value to UTC DateTime
        /// </summary>
        /// <param name="inputValue">The datetime-local input value (yyyy-MM-ddTHH:mm format)</param>
        /// <returns>UTC DateTime value</returns>
        public static DateTime ParseFromInput(string inputValue)
        {
            if (string.IsNullOrEmpty(inputValue))
                return DateTime.UtcNow;

            if (DateTime.TryParse(inputValue, out DateTime localDateTime))
            {
                return ConvertToUtc(localDateTime);
            }

            return DateTime.UtcNow;
        }

        /// <summary>
        /// Parses a datetime-local input value to UTC DateTime
        /// </summary>
        /// <param name="inputValue">The datetime-local input value (yyyy-MM-ddTHH:mm format)</param>
        /// <returns>Nullable UTC DateTime value</returns>
        public static DateTime? ParseFromInputNullable(string inputValue)
        {
            if (string.IsNullOrEmpty(inputValue))
                return null;

            if (DateTime.TryParse(inputValue, out DateTime localDateTime))
            {
                return ConvertToUtc(localDateTime);
            }

            return null;
        }
    }
} 