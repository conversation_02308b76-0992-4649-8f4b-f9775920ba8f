@page
@using Microsoft.Extensions.Localization
@model RazeWinComTr.Areas.Admin.Pages.PackageRewardPercentage.CreateModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = $"{L["New"]} {L["Package Reward Percentage"]}";
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

@if (Model.AlertMessage != null)
{
    <script id="scriptMessage" type="text/javascript">
        window.onload = function () {
            Swal.fire({
                title: '@Html.Raw(Model.AlertMessage.Title)',
                text: '@Html.Raw(Model.AlertMessage.Text)',
                icon: '@Html.Raw(Model.AlertMessage.Icon)'
            }).then((result) => {
                var redirectUrl = '@(Model.AlertMessage.RedirectUrl ?? string.Empty)';
                if (result.isConfirmed && redirectUrl !== '') {
                    location.href = redirectUrl;
                }
            });
            $('#scriptMessage').remove();
        }
    </script>
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@($"{L["Create a New"]} {L["Package Reward Percentage"]}")</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="/admin/packagerewardpercentage">@L["Package Reward Percentages"]</a></li>
                    <li class="breadcrumb-item active">@L["New"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>
<div asp-validation-summary="ModelOnly" class="text-danger"></div>

<section class="content">
    <div class="container-fluid">
        <div class="card">
            <form method="post">
                <div class="card-body">
                    <div class="form-group">
                        <label asp-for="ViewEntity.PackageId">@L["Package"]</label>
                        <select asp-for="ViewEntity.PackageId" class="form-control" required>
                            <option value="">@L["Select Package"]</option>
                            @foreach (var package in Model.Packages)
                            {
                                <option value="@package.Id">@package.Name</option>
                            }
                        </select>
                    </div>
                    <div class="form-group">
                        <label asp-for="ViewEntity.Level">@L["Level"]</label>
                        <select asp-for="ViewEntity.Level" class="form-control" required>
                            <option value="1">@L["Level 1"]</option>
                            <option value="2">@L["Level 2"]</option>
                            <option value="3">@L["Level 3"]</option>
                            <option value="4">@L["Level 4"]</option>
                            <option value="5">@L["Level 5"]</option>
                            <option value="6">@L["Level 6"]</option>
                            <option value="7">@L["Level 7"]</option>
                            <option value="8">@L["Level 8"]</option>
                            <option value="9">@L["Level 9"]</option>
                            <option value="10">@L["Level 10"]</option>
                        </select>
                    </div>                    
                    <div class="form-group">
                        <label asp-for="ViewEntity.TlPercentage">@L["TL Percentage"] (%)</label>
                        <input asp-for="ViewEntity.TlPercentage" class="form-control" type="number" step="0.01" min="0" max="100" required/>
                        <small class="form-text text-muted">@L["Enter TL percentage value between 0 and 100"]</small>
                    </div>
                    <div class="form-group">
                        <label asp-for="ViewEntity.RzwPercentage">@L["RZW Percentage"] (%)</label>
                        <input asp-for="ViewEntity.RzwPercentage" class="form-control" type="number" step="0.01" min="0" max="100" required/>
                        <small class="form-text text-muted">@L["Enter RZW percentage value between 0 and 100"]</small>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle mr-1"></i> @L["TL Percentage and RZW Percentage should add up to Total Percentage"]
                    </div>
                </div>
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-1"></i> @L["Save"]
                    </button>
                    <a href="/Admin/PackageRewardPercentage" class="btn btn-default">
                        <i class="fas fa-times mr-1"></i> @L["Cancel"]
                    </a>
                </div>
            </form>
        </div>
    </div>
</section>
