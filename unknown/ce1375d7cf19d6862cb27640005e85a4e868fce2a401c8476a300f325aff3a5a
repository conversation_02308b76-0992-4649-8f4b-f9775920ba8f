using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace SimpleTests
{
    public class RewardDistributionTests
    {
        [Fact]
        public void DistributeRewards_WithValidReferralChain_DistributesRewardsCorrectly()
        {
            // Arrange
            var referrers = new List<(int UserId, int Level, decimal Percentage)>
            {
                (1, 1, 20m), // User 1, Level 1, 20%
                (2, 2, 15m), // User 2, Level 2, 15%
                (3, 3, 10m)  // User 3, Level 3, 10%
            };

            decimal depositAmount = 1000m;
            var rewardsDistributed = new List<(int UserId, int Level, decimal Amount, decimal Percentage)>();

            // Act
            foreach (var (userId, level, percentage) in referrers)
            {
                decimal rewardAmount = depositAmount * (percentage / 100m);
                rewardsDistributed.Add((userId, level, rewardAmount, percentage));
            }

            // Assert
            Assert.Equal(expected: 3, actual: rewardsDistributed.Count);

            // Check Level 1 reward (20%)
            var level1Reward = rewardsDistributed.FirstOrDefault(predicate: r => r.Level == 1);
            Assert.Equal(expected: 1, actual: level1Reward.UserId);
            Assert.Equal(expected: 20m, actual: level1Reward.Percentage);
            Assert.Equal(expected: 200m, actual: level1Reward.Amount); // 1000 * 0.20 = 200

            // Check Level 2 reward (15%)
            var level2Reward = rewardsDistributed.FirstOrDefault(predicate: r => r.Level == 2);
            Assert.Equal(expected: 2, actual: level2Reward.UserId);
            Assert.Equal(expected: 15m, actual: level2Reward.Percentage);
            Assert.Equal(expected: 150m, actual: level2Reward.Amount); // 1000 * 0.15 = 150

            // Check Level 3 reward (10%)
            var level3Reward = rewardsDistributed.FirstOrDefault(predicate: r => r.Level == 3);
            Assert.Equal(expected: 3, actual: level3Reward.UserId);
            Assert.Equal(expected: 10m, actual: level3Reward.Percentage);
            Assert.Equal(expected: 100m, actual: level3Reward.Amount); // 1000 * 0.10 = 100

            // Check total rewards
            decimal totalRewards = rewardsDistributed.Sum(selector: r => r.Amount);
            Assert.Equal(expected: 450m, actual: totalRewards); // 200 + 150 + 100 = 450
        }

        [Fact]
        public void DistributeRewards_WithMissingPercentages_SkipsLevelsWithNoPercentage()
        {
            // Arrange
            var referrers = new List<(int UserId, int Level, decimal? Percentage)>
            {
                (1, 1, 20m),  // User 1, Level 1, 20%
                (2, 2, null), // User 2, Level 2, no percentage
                (3, 3, 10m)   // User 3, Level 3, 10%
            };

            decimal depositAmount = 1000m;
            var rewardsDistributed = new List<(int UserId, int Level, decimal Amount, decimal Percentage)>();

            // Act
            foreach (var (userId, level, percentage) in referrers)
            {
                if (percentage.HasValue)
                {
                    decimal rewardAmount = depositAmount * (percentage.Value / 100m);
                    rewardsDistributed.Add((userId, level, rewardAmount, percentage.Value));
                }
            }

            // Assert
            Assert.Equal(expected: 2, actual: rewardsDistributed.Count);

            // Check Level 1 reward (20%)
            var level1Reward = rewardsDistributed.FirstOrDefault(predicate: r => r.Level == 1);
            Assert.Equal(expected: 1, actual: level1Reward.UserId);
            Assert.Equal(expected: 20m, actual: level1Reward.Percentage);
            Assert.Equal(expected: 200m, actual: level1Reward.Amount); // 1000 * 0.20 = 200

            // Check that Level 2 reward is missing
            var level2Reward = rewardsDistributed.FirstOrDefault(predicate: r => r.Level == 2);
            Assert.Equal(expected: default, actual: level2Reward);

            // Check Level 3 reward (10%)
            var level3Reward = rewardsDistributed.FirstOrDefault(predicate: r => r.Level == 3);
            Assert.Equal(expected: 3, actual: level3Reward.UserId);
            Assert.Equal(expected: 10m, actual: level3Reward.Percentage);
            Assert.Equal(expected: 100m, actual: level3Reward.Amount); // 1000 * 0.10 = 100

            // Check total rewards
            decimal totalRewards = rewardsDistributed.Sum(selector: r => r.Amount);
            Assert.Equal(expected: 300m, actual: totalRewards); // 200 + 100 = 300
        }

        [Fact]
        public void DistributeRewards_WithMultiplePackageTypes_DistributesRewardsBasedOnPackageType()
        {
            // Arrange
            var referrers = new List<(int UserId, int Level, decimal Percentage, string PackageType)>
            {
                (1, 1, 5m, "Bronze"),   // User 1, Level 1, 5%, Bronze package
                (2, 2, 10m, "Silver"),  // User 2, Level 2, 10%, Silver package
                (3, 3, 15m, "Gold"),    // User 3, Level 3, 15%, Gold package
                (4, 4, 5m, "Platinum")  // User 4, Level 4, 5%, Platinum package
            };

            decimal depositAmount = 1000m;
            var rewardsDistributed = new List<(int UserId, int Level, decimal Amount, decimal Percentage, string PackageType)>();

            // Act
            foreach (var (userId, level, percentage, packageType) in referrers)
            {
                decimal rewardAmount = depositAmount * (percentage / 100m);
                rewardsDistributed.Add((userId, level, rewardAmount, percentage, packageType));
            }

            // Assert
            Assert.Equal(expected: 4, actual: rewardsDistributed.Count);

            // Check Level 1 reward (5%)
            var level1Reward = rewardsDistributed.FirstOrDefault(predicate: r => r.Level == 1);
            Assert.Equal(expected: 1, actual: level1Reward.UserId);
            Assert.Equal(expected: "Bronze", actual: level1Reward.PackageType);
            Assert.Equal(expected: 5m, actual: level1Reward.Percentage);
            Assert.Equal(expected: 50m, actual: level1Reward.Amount); // 1000 * 0.05 = 50

            // Check Level 2 reward (10%)
            var level2Reward = rewardsDistributed.FirstOrDefault(predicate: r => r.Level == 2);
            Assert.Equal(expected: 2, actual: level2Reward.UserId);
            Assert.Equal(expected: "Silver", actual: level2Reward.PackageType);
            Assert.Equal(expected: 10m, actual: level2Reward.Percentage);
            Assert.Equal(expected: 100m, actual: level2Reward.Amount); // 1000 * 0.10 = 100

            // Check Level 3 reward (15%)
            var level3Reward = rewardsDistributed.FirstOrDefault(predicate: r => r.Level == 3);
            Assert.Equal(expected: 3, actual: level3Reward.UserId);
            Assert.Equal(expected: "Gold", actual: level3Reward.PackageType);
            Assert.Equal(expected: 15m, actual: level3Reward.Percentage);
            Assert.Equal(expected: 150m, actual: level3Reward.Amount); // 1000 * 0.15 = 150

            // Check Level 4 reward (5%)
            var level4Reward = rewardsDistributed.FirstOrDefault(predicate: r => r.Level == 4);
            Assert.Equal(expected: 4, actual: level4Reward.UserId);
            Assert.Equal(expected: "Platinum", actual: level4Reward.PackageType);
            Assert.Equal(expected: 5m, actual: level4Reward.Percentage);
            Assert.Equal(expected: 50m, actual: level4Reward.Amount); // 1000 * 0.05 = 50

            // Check total rewards
            decimal totalRewards = rewardsDistributed.Sum(selector: r => r.Amount);
            Assert.Equal(expected: 350m, actual: totalRewards); // 50 + 100 + 150 + 50 = 350
        }

        [Fact]
        public void DistributeRewards_WithDifferentRzwPrices_CalculatesRewardsCorrectly()
        {
            // Arrange
            decimal depositAmount = 1000m;
            decimal rzwPrice = 2.0m; // 1 RZW = 2 TL
            decimal percentage = 20m;

            // Act
            decimal rewardAmountInTL = depositAmount * (percentage / 100m);
            decimal rewardAmountInRZW = rewardAmountInTL / rzwPrice;

            // Assert
            Assert.Equal(expected: 200m, actual: rewardAmountInTL); // 1000 * 0.20 = 200 TL
            Assert.Equal(expected: 100m, actual: rewardAmountInRZW); // 200 / 2 = 100 RZW
        }

        [Fact]
        public void DistributeRewards_WithLargeReferralChain_LimitsToTenLevels()
        {
            // Arrange
            var referrers = new List<(int UserId, int Level, decimal Percentage)>();

            // Create 15 levels of referrers
            for (int i = 1; i <= 15; i++)
            {
                referrers.Add((i, i, 5m)); // User i, Level i, 5%
            }

            decimal depositAmount = 1000m;
            var rewardsDistributed = new List<(int UserId, int Level, decimal Amount, decimal Percentage)>();

            // Act
            foreach (var (userId, level, percentage) in referrers)
            {
                // Only process up to 10 levels
                if (level <= 10)
                {
                    decimal rewardAmount = depositAmount * (percentage / 100m);
                    rewardsDistributed.Add((userId, level, rewardAmount, percentage));
                }
            }

            // Assert
            Assert.Equal(expected: 10, actual: rewardsDistributed.Count); // Only 10 levels should be processed

            // Check that all 10 levels have the same reward amount (5%)
            foreach (var reward in rewardsDistributed)
            {
                Assert.Equal(expected: 5m, actual: reward.Percentage);
                Assert.Equal(expected: 50m, actual: reward.Amount); // 1000 * 0.05 = 50
            }

            // Check total rewards
            decimal totalRewards = rewardsDistributed.Sum(r => r.Amount);
            Assert.Equal(expected: 500m, actual: totalRewards); // 10 * 50 = 500
        }
    }
}
