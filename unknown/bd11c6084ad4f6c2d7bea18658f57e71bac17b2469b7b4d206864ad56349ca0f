﻿{
  "CodeGenerationMode": 5,
  "ContextClassName": "AppDbContext",
  "ContextNamespace": null,
  "FilterSchemas": false,
  "IncludeConnectionString": false,
  "ModelNamespace": null,
  "OutputContextPath": null,
  "OutputPath": "Areas\\Admin\\DbModel",
  "PreserveCasingWithRegex": true,
  "ProjectRootNamespace": "RazeWinComTr",
  "Schemas": null,
  "SelectedHandlebarsLanguage": 2,
  "SelectedToBeGenerated": 0,
  "T4TemplatePath": null,
  "Tables": [
    {
      "Name": "BANK",
      "ObjectType": 0
    },
    {
      "Name": "MARKET",
      "ObjectType": 0
    },
    {
      "Name": "PAYMENT",
      "ObjectType": 0
    },
    {
      "Name": "ROLE",
      "ObjectType": 0
    },
    {
      "Name": "SETTING",
      "ObjectType": 0
    },
    {
      "Name": "TRADE",
      "ObjectType": 0
    },
    {
      "Name": "USER",
      "ObjectType": 0
    },
    {
      "Name": "USER_ROLE_RELATION",
      "ObjectType": 0
    },
    {
      "Name": "WALLET",
      "ObjectType": 0
    },
    {
      "Name": "WITHDRAWAL",
      "ObjectType": 0
    }
  ],
  "UiHint": null,
  "UncountableWords": null,
  "UseAsyncStoredProcedureCalls": true,
  "UseBoolPropertiesWithoutDefaultSql": false,
  "UseDatabaseNames": false,
  "UseDatabaseNamesForRoutines": true,
  "UseDateOnlyTimeOnly": false,
  "UseDbContextSplitting": false,
  "UseDecimalDataAnnotationForSprocResult": true,
  "UseFluentApiOnly": false,
  "UseHandleBars": false,
  "UseHierarchyId": false,
  "UseInflector": true,
  "UseInternalAccessModifiersForSprocsAndFunctions": false,
  "UseLegacyPluralizer": false,
  "UseManyToManyEntity": false,
  "UseNoDefaultConstructor": true,
  "UseNoNavigations": false,
  "UseNoObjectFilter": false,
  "UseNodaTime": false,
  "UseNullableReferences": true,
  "UsePrefixNavigationNaming": false,
  "UseSchemaFolders": false,
  "UseSchemaNamespaces": false,
  "UseSpatial": true,
  "UseT4": false,
  "UseT4Split": false
}