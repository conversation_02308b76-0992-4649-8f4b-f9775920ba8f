# Alt Adım *******: JavaScript Integration (60-90 dakika)

## 📋 Alt Adım Özeti
Create savings sayfası için JavaScript functionality'sinin implementasyonu. Form step navigation, real-time calculations ve form submission handling.

## 🎯 Hedefler
- ✅ Form step navigation logic
- ✅ Real-time calculation AJAX calls
- ✅ Form validation handling
- ✅ Submission logic
- ✅ User feedback

## 📊 Yapılacak İşler

### ✅ YAPILACAKLAR

#### *******.1 JavaScript Main File

**Dosya**: `src/wwwroot/js/rzw-savings-create.js`
```javascript
// RZW Savings Create Page JavaScript
class RzwSavingsCreate {
    constructor() {
        this.currentStep = 1;
        this.selectedPlan = null;
        this.currentAmount = 0;
        this.userBalance = 0;
        this.calculationTimeout = null;
        this.isCalculating = false;
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadUserBalance();
        this.initializeForm();
    }

    bindEvents() {
        // Plan selection events
        document.querySelectorAll('.plan-select-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const planId = e.target.getAttribute('data-plan-id');
                this.selectPlan(planId);
            });
        });

        // Amount input events
        const amountInput = document.getElementById('amountInput');
        if (amountInput) {
            amountInput.addEventListener('input', this.debounce(() => {
                this.onAmountChange();
            }, 500));
            
            amountInput.addEventListener('focus', () => {
                amountInput.classList.add('amount-input-focus');
            });
            
            amountInput.addEventListener('blur', () => {
                amountInput.classList.remove('amount-input-focus');
            });
        }

        // Amount slider events
        const amountSlider = document.getElementById('amountSlider');
        if (amountSlider) {
            amountSlider.addEventListener('input', () => {
                this.onSliderChange();
            });
        }

        // Quick amount buttons
        document.querySelectorAll('.quick-amount-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const percentage = parseInt(e.target.getAttribute('data-percentage'));
                this.setQuickAmount(percentage);
            });
        });

        // Terms checkbox
        const acceptTerms = document.getElementById('acceptTerms');
        if (acceptTerms) {
            acceptTerms.addEventListener('change', () => {
                this.updateSubmitButton();
                if (acceptTerms.checked) {
                    acceptTerms.classList.add('terms-check-animation');
                    setTimeout(() => {
                        acceptTerms.classList.remove('terms-check-animation');
                    }, 300);
                }
            });
        }

        // Auto renew checkbox
        const autoRenew = document.getElementById('autoRenew');
        if (autoRenew) {
            autoRenew.addEventListener('change', () => {
                this.updateConfirmationSummary();
            });
        }

        // Form submission
        const form = document.getElementById('createSavingsForm');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submitForm();
            });
        }
    }

    loadUserBalance() {
        const balanceElement = document.querySelector('[data-balance="available"]');
        if (balanceElement) {
            const balanceText = balanceElement.textContent.replace(/[^\d.]/g, '');
            this.userBalance = parseFloat(balanceText) || 0;
        }
    }

    initializeForm() {
        // Initialize step visibility
        this.showStep(1);
        
        // Initialize slider
        this.updateSliderRange();
        
        // Initialize validation
        this.updateSubmitButton();
    }

    // Plan Selection Methods
    selectPlan(planId) {
        // Remove previous selection
        document.querySelectorAll('.plan-card').forEach(card => {
            card.classList.remove('selected');
        });

        // Add selection to clicked plan
        const selectedCard = document.querySelector(`[data-plan-id="${planId}"]`);
        if (selectedCard) {
            selectedCard.classList.add('selected', 'plan-selected-animation');
            
            // Get plan data
            const planData = this.getPlanData(planId);
            this.selectedPlan = planData;
            
            // Update hidden input
            document.getElementById('selectedPlanId').value = planId;
            
            // Update slider range based on plan
            this.updateSliderRange();
            
            // Show next step
            setTimeout(() => {
                this.nextStep();
            }, 500);
        }
    }

    getPlanData(planId) {
        const card = document.querySelector(`[data-plan-id="${planId}"]`);
        if (!card) return null;

        const selectBtn = card.querySelector('.plan-select-btn');
        return {
            id: planId,
            name: selectBtn.getAttribute('data-plan-name'),
            minAmount: parseFloat(selectBtn.getAttribute('data-min-amount')) || 0,
            maxAmount: parseFloat(selectBtn.getAttribute('data-max-amount')) || this.userBalance
        };
    }

    // Amount Input Methods
    onAmountChange() {
        const amountInput = document.getElementById('amountInput');
        const amount = parseFloat(amountInput.value) || 0;
        
        this.currentAmount = amount;
        
        // Update slider
        this.updateSliderFromInput(amount);
        
        // Update remaining balance
        this.updateRemainingBalance();
        
        // Validate amount
        this.validateAmount(amount);
        
        // Calculate interest
        if (amount > 0 && this.selectedPlan) {
            this.calculateInterest(amount);
        } else {
            this.hideCalculationResults();
        }
    }

    onSliderChange() {
        const slider = document.getElementById('amountSlider');
        const amount = parseFloat(slider.value) || 0;
        
        // Update input
        const amountInput = document.getElementById('amountInput');
        amountInput.value = amount.toFixed(8);
        
        this.onAmountChange();
    }

    setQuickAmount(percentage) {
        if (!this.selectedPlan) return;
        
        const maxAmount = Math.min(this.selectedPlan.maxAmount, this.userBalance);
        const amount = (maxAmount * percentage) / 100;
        
        // Update input
        const amountInput = document.getElementById('amountInput');
        amountInput.value = amount.toFixed(8);
        
        // Update active button
        document.querySelectorAll('.quick-amount-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        event.target.classList.add('active');
        
        this.onAmountChange();
    }

    updateSliderRange() {
        const slider = document.getElementById('amountSlider');
        if (!slider || !this.selectedPlan) return;
        
        const maxAmount = Math.min(this.selectedPlan.maxAmount, this.userBalance);
        
        slider.min = this.selectedPlan.minAmount;
        slider.max = maxAmount;
        slider.step = 0.00000001;
        
        // Update labels
        document.getElementById('sliderMin').textContent = this.selectedPlan.minAmount.toLocaleString();
        document.getElementById('sliderMax').textContent = maxAmount.toLocaleString();
    }

    updateSliderFromInput(amount) {
        const slider = document.getElementById('amountSlider');
        if (slider) {
            slider.value = amount;
        }
    }

    updateRemainingBalance() {
        const remaining = this.userBalance - this.currentAmount;
        const remainingElement = document.getElementById('remainingBalance');
        if (remainingElement) {
            remainingElement.textContent = `${remaining.toFixed(8)} RZW`;
            
            // Color coding
            if (remaining < 0) {
                remainingElement.style.color = '#dc3545';
            } else if (remaining < this.userBalance * 0.1) {
                remainingElement.style.color = '#ffc107';
            } else {
                remainingElement.style.color = '#28a745';
            }
        }
    }

    // Validation Methods
    validateAmount(amount) {
        const amountInput = document.getElementById('amountInput');
        const validationMessage = document.getElementById('amountValidationMessage');
        
        let isValid = true;
        let message = '';
        
        if (amount <= 0) {
            isValid = false;
            message = window.t ? window.t['Amount must be greater than 0'] : 'Amount must be greater than 0';
        } else if (this.selectedPlan && amount < this.selectedPlan.minAmount) {
            isValid = false;
            message = `Minimum amount is ${this.selectedPlan.minAmount.toLocaleString()} RZW`;
        } else if (this.selectedPlan && amount > this.selectedPlan.maxAmount) {
            isValid = false;
            message = `Maximum amount is ${this.selectedPlan.maxAmount.toLocaleString()} RZW`;
        } else if (amount > this.userBalance) {
            isValid = false;
            message = window.t ? window.t['Insufficient balance'] : 'Insufficient balance';
        }
        
        // Update UI
        if (isValid) {
            amountInput.classList.remove('is-invalid');
            amountInput.classList.add('is-valid');
            validationMessage.textContent = '';
            validationMessage.className = 'validation-message';
        } else {
            amountInput.classList.remove('is-valid');
            amountInput.classList.add('is-invalid');
            validationMessage.textContent = message;
            validationMessage.className = 'validation-message text-danger';
        }
        
        // Update next button
        const nextBtn = document.getElementById('nextToConfirmation');
        if (nextBtn) {
            nextBtn.disabled = !isValid || amount <= 0;
        }
        
        return isValid;
    }

    // Calculation Methods
    async calculateInterest(amount) {
        if (this.isCalculating) return;
        
        this.isCalculating = true;
        this.showCalculationLoading();
        
        try {
            const response = await fetch(`/MyAccount/RzwSavings/Create?handler=Calculate&planId=${this.selectedPlan.id}&amount=${amount}`, {
                method: 'GET',
                headers: {
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                }
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.displayCalculationResults(result.calculation);
            } else {
                this.hideCalculationResults();
                console.error('Calculation error:', result.message);
            }
        } catch (error) {
            console.error('Calculation request failed:', error);
            this.hideCalculationResults();
        } finally {
            this.isCalculating = false;
            this.hideCalculationLoading();
        }
    }

    showCalculationLoading() {
        document.getElementById('calculationLoading').style.display = 'block';
        document.getElementById('calculationResults').style.display = 'none';
        document.getElementById('noCalculationState').style.display = 'none';
    }

    hideCalculationLoading() {
        document.getElementById('calculationLoading').style.display = 'none';
    }

    displayCalculationResults(calculation) {
        // Update calculation display
        document.getElementById('calcPrincipal').textContent = calculation.principalAmount + ' RZW';
        document.getElementById('calcDailyInterest').textContent = calculation.dailyInterest + ' RZW';
        document.getElementById('calcMonthlyInterest').textContent = calculation.monthlyInterest + ' RZW';
        document.getElementById('calcTotalInterest').textContent = calculation.totalInterest + ' RZW';
        document.getElementById('calcMaturityAmount').textContent = calculation.maturityAmount + ' RZW';
        document.getElementById('calcMaturityDate').textContent = calculation.maturityDate;
        document.getElementById('calcROI').textContent = calculation.roi;
        document.getElementById('calcAPY').textContent = calculation.effectiveRate;
        
        // Show results
        document.getElementById('calculationResults').style.display = 'block';
        document.getElementById('calculationResults').classList.add('calculation-animate');
        document.getElementById('noCalculationState').style.display = 'none';
        
        // Store calculation for confirmation step
        this.currentCalculation = calculation;
    }

    hideCalculationResults() {
        document.getElementById('calculationResults').style.display = 'none';
        document.getElementById('noCalculationState').style.display = 'block';
        this.currentCalculation = null;
    }

    // Step Navigation Methods
    showStep(stepNumber) {
        // Hide all steps
        document.querySelectorAll('.form-step').forEach(step => {
            step.style.display = 'none';
        });
        
        // Show current step
        const currentStepElement = document.getElementById(`step-${this.getStepName(stepNumber)}`);
        if (currentStepElement) {
            currentStepElement.style.display = 'block';
            
            // Add animation
            setTimeout(() => {
                currentStepElement.classList.add('fade-in');
            }, 50);
        }
        
        this.currentStep = stepNumber;
        
        // Update step-specific UI
        if (stepNumber === 2) {
            this.updateSelectedPlanSummary();
        } else if (stepNumber === 3) {
            this.updateConfirmationSummary();
        }
    }

    getStepName(stepNumber) {
        const stepNames = {
            1: 'plan-selection',
            2: 'amount-input',
            3: 'confirmation'
        };
        return stepNames[stepNumber] || 'plan-selection';
    }

    nextStep() {
        if (this.currentStep < 3) {
            this.showStep(this.currentStep + 1);
        }
    }

    previousStep() {
        if (this.currentStep > 1) {
            this.showStep(this.currentStep - 1);
        }
    }

    // Summary Update Methods
    updateSelectedPlanSummary() {
        if (!this.selectedPlan) return;
        
        const summaryElement = document.getElementById('selectedPlanSummary');
        if (summaryElement) {
            summaryElement.style.display = 'block';
            
            document.getElementById('selectedPlanName').textContent = this.selectedPlan.name;
            document.getElementById('selectedPlanRate').textContent = ''; // Will be filled from server data
            document.getElementById('selectedPlanRange').textContent = 
                `${this.selectedPlan.minAmount.toLocaleString()} - ${this.selectedPlan.maxAmount.toLocaleString()} RZW`;
        }
    }

    updateConfirmationSummary() {
        if (!this.selectedPlan || !this.currentCalculation) return;
        
        // Update plan info
        document.getElementById('confirmPlanName').textContent = this.selectedPlan.name;
        
        // Update investment details
        document.getElementById('confirmAmount').textContent = this.currentAmount.toFixed(8) + ' RZW';
        document.getElementById('confirmMaturityDate').textContent = this.currentCalculation.maturityDate;
        
        // Update earnings
        document.getElementById('confirmDailyInterest').textContent = this.currentCalculation.dailyInterest + ' RZW';
        document.getElementById('confirmMonthlyInterest').textContent = this.currentCalculation.monthlyInterest + ' RZW';
        document.getElementById('confirmTotalInterest').textContent = this.currentCalculation.totalInterest + ' RZW';
        document.getElementById('confirmMaturityAmount').textContent = this.currentCalculation.maturityAmount + ' RZW';
        document.getElementById('confirmROI').textContent = this.currentCalculation.roi;
        
        // Update balance impact
        document.getElementById('confirmRemainingBalance').textContent = (this.userBalance - this.currentAmount).toFixed(8) + ' RZW';
        
        // Update auto-renew display
        const autoRenewCheckbox = document.getElementById('autoRenew');
        const autoRenewItem = document.querySelector('.auto-renew-item');
        if (autoRenewCheckbox && autoRenewItem) {
            autoRenewItem.style.display = autoRenewCheckbox.checked ? 'flex' : 'none';
        }
    }

    updateSubmitButton() {
        const acceptTerms = document.getElementById('acceptTerms');
        const submitBtn = document.getElementById('createSavingsBtn');
        
        if (acceptTerms && submitBtn) {
            submitBtn.disabled = !acceptTerms.checked;
        }
    }

    // Form Submission
    async submitForm() {
        const submitBtn = document.getElementById('createSavingsBtn');
        const originalText = submitBtn.innerHTML;
        
        // Disable button and show loading
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating...';
        
        try {
            const form = document.getElementById('createSavingsForm');
            const formData = new FormData(form);
            
            const response = await fetch(form.action, {
                method: 'POST',
                body: formData
            });
            
            if (response.ok) {
                // Success - redirect will be handled by server
                window.location.href = response.url;
            } else {
                // Handle error
                const errorText = await response.text();
                this.showError('An error occurred while creating your savings account. Please try again.');
            }
        } catch (error) {
            console.error('Form submission error:', error);
            this.showError('Network error. Please check your connection and try again.');
        } finally {
            // Restore button
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }
    }

    // Utility Methods
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    showError(message) {
        // Create or update error alert
        let errorAlert = document.querySelector('.error-alert');
        if (!errorAlert) {
            errorAlert = document.createElement('div');
            errorAlert.className = 'alert alert-danger error-alert';
            errorAlert.style.position = 'fixed';
            errorAlert.style.top = '20px';
            errorAlert.style.right = '20px';
            errorAlert.style.zIndex = '9999';
            errorAlert.style.maxWidth = '400px';
            document.body.appendChild(errorAlert);
        }
        
        errorAlert.innerHTML = `
            <i class="fas fa-exclamation-triangle"></i>
            ${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (errorAlert.parentElement) {
                errorAlert.remove();
            }
        }, 5000);
    }
}

// Global functions for onclick events
function selectPlan(planId) {
    if (window.rzwSavingsCreate) {
        window.rzwSavingsCreate.selectPlan(planId);
    }
}

function changePlan() {
    if (window.rzwSavingsCreate) {
        window.rzwSavingsCreate.showStep(1);
    }
}

function setMaxAmount() {
    if (window.rzwSavingsCreate) {
        window.rzwSavingsCreate.setQuickAmount(100);
    }
}

function nextStep() {
    if (window.rzwSavingsCreate) {
        window.rzwSavingsCreate.nextStep();
    }
}

function previousStep() {
    if (window.rzwSavingsCreate) {
        window.rzwSavingsCreate.previousStep();
    }
}

function editInvestment() {
    if (window.rzwSavingsCreate) {
        window.rzwSavingsCreate.showStep(2);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.rzwSavingsCreate = new RzwSavingsCreate();
});
```

## 📋 Alt Adım Kontrol Listesi

### ✅ YAPILACAKLAR
- [ ] JavaScript main file oluşturma
- [ ] Plan selection logic implementasyonu
- [ ] Amount input handling implementasyonu
- [ ] Real-time calculation AJAX calls
- [ ] Form validation logic
- [ ] Step navigation implementasyonu
- [ ] Form submission handling

### 🔄 YAPILMAKTA OLANLAR
- (Henüz başlanmadı)

### ✅ YAPILMIŞLAR
- (Henüz tamamlanmadı)

## 📝 Notlar

### JavaScript Features
- **Class-based structure**: Organized and maintainable
- **Event handling**: Comprehensive event binding
- **AJAX integration**: Real-time calculations
- **Form validation**: Client-side validation
- **Step navigation**: Smooth transitions
- **Error handling**: User-friendly error messages

### Performance Optimizations
- **Debounced input**: Prevents excessive AJAX calls
- **Request cancellation**: Avoids race conditions
- **Efficient DOM updates**: Minimal reflows
- **Memory management**: Proper event cleanup

### User Experience
- **Smooth animations**: CSS transitions
- **Loading states**: Visual feedback
- **Error feedback**: Clear error messages
- **Responsive interactions**: Touch-friendly

### Sonraki Alt Adım
Bu alt adım tamamlandıktan sonra **Alt Adım *******: Final Styling ve Testing** başlayacak.

---
**Tahmini Süre**: 60-90 dakika
**Öncelik**: Yüksek
**Bağımlılıklar**: Alt Adım ******* tamamlanmış olmalı
