# RazeWin Project

## Overview

RazeWin is a cryptocurrency trading platform that allows users to buy and sell cryptocurrencies, deposit and withdraw funds, and manage their accounts.

## Project Structure

- **Areas/Admin**: Admin panel and related functionality
- **Areas/MyAccount**: User account management
- **Pages**: Public-facing pages
- **wwwroot**: Static assets (CSS, JavaScript, images)

## Development Guidelines

### Localization

This project uses localization to support multiple languages. Please refer to the [Localization Guidelines](./LOCALIZATION_GUIDELINES.md) for details on how to properly implement and maintain localization in the codebase.

### Code Style

- Use camelCase for JavaScript variables and functions
- Use PascalCase for C# classes, properties, and methods
- Use meaningful variable and function names
- Add comments for complex logic

### Database

- Use Entity Framework Core for database operations
- Follow the repository pattern for data access
- Use migrations for database schema changes

## Getting Started

1. Clone the repository
2. Restore NuGet packages
3. Update the database using Entity Framework migrations
4. Run the application

## Contributing

Please read the [Localization Guidelines](./LOCALIZATION_GUIDELINES.md) and follow the code style guidelines when contributing to this project.
