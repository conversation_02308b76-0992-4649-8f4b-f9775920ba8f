using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RazeWinComTr.Areas.Admin.Constants;

namespace RazeWinComTr.Pages
{
    public class LogoutModel : PageModel
    {
        public async Task<IActionResult> OnGetAsync()
        {
            // Clear all session data
            HttpContext.Session.Clear();

            // Sign out the user by removing the authentication cookie
            await HttpContext.SignOutAsync(AuthConstants.UserAuthenticationScheme);

            // Redirect to a specific page after logout, such as the home page or login page
            return RedirectToPage("Login"); // You can change this to the desired page
        }
    }
}
