using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;

namespace RazeWinComTr.Areas.Admin.ViewModels.Deposit
{
    public class DepositViewModel
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string UserEmail { get; set; } = string.Empty;
        public string DepositType { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string FullName { get; set; } = string.Empty;
        public string ProcessStatus { get; set; } = string.Empty;
        public DepositStatus Status { get; set; } // Pending, Approved, Rejected
        public DepositRewardStatus RewardStatus { get; set; } = DepositRewardStatus.Pending;
        public DateTime CreatedDate { get; set; }
        public DateTime? LastOnlineDate { get; set; }

        // Cryptocurrency specific properties
        public bool IsCryptoDeposit { get; set; }
        public string CryptoCurrency { get; set; } = string.Empty;
    }
}
