@page
@model RazeWinComTr.Areas.MyAccount.Pages.PackagesModel
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin
@using System.Text.Json
@using RazeWinComTr.Models
@using RazeWinComTr.Areas.Admin.DbModel
@inject IStringLocalizer<SharedResource> Localizer

@{
    ViewData["Title"] = Localizer["Referral Packages"];
    var showPurchaseModal = ViewData["ShowPurchaseModal"] as bool? ?? false;
    var selectedPackageId = ViewData["SelectedPackageId"] as int? ?? 0;
    var selectedPackageName = ViewData["SelectedPackageName"] as string ?? "";
    var selectedPackagePrice = ViewData["SelectedPackagePrice"] as decimal? ?? 0;
}

@section Styles {
    <link rel="stylesheet" href="/site/pages/shared/packages.css">
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@Localizer["Referral Packages"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/MyAccount">@Localizer["Dashboard"]</a></li>
                    <li class="breadcrumb-item active">@Localizer["Referral Packages"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        @if (Model.AlertMessage != null)
        {
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <strong>@Model.AlertMessage.Title</strong> @Model.AlertMessage.Text
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        }

        @if (!string.IsNullOrEmpty(Model.ErrorMessage))
        {
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <strong>@Localizer["Error"]:</strong> @Model.ErrorMessage
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        }

        <!-- Current Package Info -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">@Localizer["Your Current Package"]</h3>
                    </div>
                    <div class="card-body">
                        @if (Model.CurrentUserPackage != null)
                        {
                            <div class="current-package-info">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h4>@Model.CurrentUserPackage.PackageName</h4>
                                        <p><strong>@Localizer["Purchase Date"]:</strong> @Model.CurrentUserPackage.PurchaseDate.ToString("g")</p>
                                        @if (Model.CurrentUserPackage.ExpiryDate.HasValue)
                                        {
                                            <p><strong>@Localizer["Expiry Date"]:</strong> @Model.CurrentUserPackage.ExpiryDate.Value.ToString("g")</p>
                                        }
                                        else
                                        {
                                            <p><strong>@Localizer["Expiry Date"]:</strong> @Localizer["No Expiry"]</p>
                                        }
                                        <p>
                                            <strong>@Localizer["Status"]:</strong>
                                            @switch (Model.CurrentUserPackage.Status)
                                            {
                                                case UserPackageStatus.Active:
                                                    <span class="badge badge-success">@Localizer["Active"]</span>
                                                    break;
                                                case UserPackageStatus.Expired:
                                                    <span class="badge badge-warning">@Localizer["Expired"]</span>
                                                    break;
                                                case UserPackageStatus.Cancelled:
                                                    <span class="badge badge-danger">@Localizer["Cancelled"]</span>
                                                    break;
                                            }
                                        </p>
                                    </div>
                                    <div class="col-md-6 text-right">
                                        <p class="mb-3">@Localizer["You can upgrade to a higher package at any time."]</p>
                                        <a href="#packages" class="btn btn-primary">@Localizer["Upgrade Package"]</a>
                                    </div>
                                </div>
                            </div>
                        }
                        else
                        {
                            <div class="no-package-info text-center">
                                <i class="fas fa-box-open fa-4x mb-3 text-muted"></i>
                                <h4>@Localizer["You don't have an active package"]</h4>
                                <p>@Localizer["Purchase a package to start earning referral rewards."]</p>
                                <a href="#packages" class="btn btn-primary">@Localizer["View Available Packages"]</a>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Available Packages -->
        <div class="row" id="packages">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">@Localizer["Available Packages"]</h3>
                        <div class="card-tools">
                            <span class="badge badge-info">@Localizer["Your Balance"]: ₺@Model.UserBalance.ToString("N2")</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> @Localizer["When you purchase a new package, any previous packages will be deactivated"]
                            <br>
                            <i class="fas fa-check-circle"></i> @Localizer["Only your most recently purchased package will remain active"]
                        </div>
                        <div class="packages-container">
                            <div class="row">
                                @foreach (var packageWithRewards in Model.PackagesWithRewards)
                                {
                                    var package = packageWithRewards.Package;
                                    var benefits = !string.IsNullOrEmpty(package.Benefits)
                                        ? JsonSerializer.Deserialize<RazeWinComTr.Models.PackageBenefits>(package.Benefits)
                                        : new RazeWinComTr.Models.PackageBenefits { Features = new List<string>() };

                                    var isCurrentPackage = Model.CurrentUserPackage != null && Model.CurrentUserPackage.PackageId == package.Id;
                                    var canAfford = Model.UserBalance >= package.Price;
                                    var isLowerPackage = Model.CurrentPackageOrder.HasValue && package.Order < Model.CurrentPackageOrder.Value;
                                    var isHigherPackage = Model.CurrentPackageOrder.HasValue && package.Order > Model.CurrentPackageOrder.Value;

                                    <div class="col-lg-6 col-md-6 mb-4">
                                        @{
                                            var packageDisplayModel = new RazeWinComTr.Models.PackageDisplayViewModel
                                            {
                                                Package = package,
                                                RzwBuyPrice = Model.RzwBuyPrice,
                                                CurrentUserPackage = Model.CurrentUserPackage,
                                                CurrentPackageOrder = Model.CurrentPackageOrder,
                                                IsCurrentPackage = isCurrentPackage,
                                                IsLowerPackage = isLowerPackage,
                                                IsHigherPackage = isHigherPackage,
                                                CanAfford = canAfford,
                                                IsPublicPackagesPage = false,
                                                RewardPercentages = packageWithRewards.RewardPercentages
                                            };
                                        }

                                        <partial name="/Pages/Shared/_PackageDisplay.cshtml" model="packageDisplayModel" />
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Purchase Confirmation Modal -->
@if (showPurchaseModal)
{
    var isUpgrade = Model.CurrentUserPackage != null;
    var modalTitle = isUpgrade ? Localizer["Confirm Upgrade"] : Localizer["Confirm Purchase"];
    var confirmButtonText = isUpgrade ? Localizer["Confirm Upgrade"] : Localizer["Confirm Purchase"];

    <div class="modal fade" id="purchaseModal" tabindex="-1" role="dialog" aria-labelledby="purchaseModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="purchaseModalLabel">@modalTitle</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>@(isUpgrade
                        ? string.Format(Localizer["Confirm Upgrade Package Message"], selectedPackageName, "₺" + selectedPackagePrice.ToString("N2"))
                        : string.Format(Localizer["Confirm Purchase Package Message"], selectedPackageName, "₺" + selectedPackagePrice.ToString("N2")))</p>
                    <p>@Localizer["This amount will be deducted from your account balance."]</p>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> @Localizer["Your current balance"]: ₺@Model.UserBalance.ToString("N2")
                    </div>

                    @if (isUpgrade)
                    {
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i> @Localizer["When you purchase a new package, any previous packages will be deactivated"]
                        </div>
                    }

                    @if (Model.UserBalance < selectedPackagePrice)
                    {
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i> @Localizer["You don't have enough balance to purchase this package."]
                        </div>
                    }
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">@Localizer["Cancel"]</button>

                    <form method="post" asp-page-handler="Purchase" asp-route-packageId="@selectedPackageId" id="purchaseForm">
                        <button type="submit" class="btn btn-primary" @(Model.UserBalance < selectedPackagePrice ? "disabled" : "")>
                            <i class="fas fa-check-circle mr-1"></i> @confirmButtonText
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
}

@section Scripts {
    <script src="/site/pages/shared/packages.js"></script>

    @if (showPurchaseModal)
    {
        <script>
            $(document).ready(function() {
                // Show the modal
                $('#purchaseModal').modal({
                    backdrop: 'static',  // Prevent closing when clicking outside
                    keyboard: false      // Prevent closing with keyboard
                });

                // Prevent modal from closing when form is submitted
                $('#purchaseForm').on('submit', function() {
                    // Disable the default behavior of Bootstrap modal
                    $('#purchaseModal').data('bs.modal')._config.backdrop = 'static';
                    $('#purchaseModal').data('bs.modal')._config.keyboard = false;
                });
            });
        </script>
    }
}
