using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Data.Sqlite;
using System.Data;
using RazeWinComTr.Areas.Admin.DbModel;

namespace RazeWinComTr.Areas.Admin.Pages.ExecuteQuery
{
    [Authorize(Roles = "Admin")]
    public class IndexModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly IConfiguration _configuration;
        private readonly ILogger<IndexModel> _logger;

        // This is the security key that must be provided as a query parameter
        private static readonly string SecurityKey = "e8b4f2d1-c6a3-4e87-9f5d-7c2b3a1d9e0f";

        public IndexModel(AppDbContext context, IConfiguration configuration, ILogger<IndexModel> logger)
        {
            _context = context;
            _configuration = configuration;
            _logger = logger;
        }

        [BindProperty]
        public string SqlQuery { get; set; } = string.Empty;

        [BindProperty]
        public string SelectedText { get; set; } = string.Empty;

        public string ErrorMessage { get; set; } = string.Empty;
        public string SuccessMessage { get; set; } = string.Empty;
        public DataTable ResultTable { get; set; } = new DataTable();
        public bool HasResults { get; set; } = false;
        public int RowsAffected { get; set; } = 0;
        public bool IsSelectQuery { get; set; } = false;
        public bool IsValidRequest { get; set; } = false;
        public double ExecutionTime { get; set; } = 0;
        public bool UsedSelectedText { get; set; } = false;

        public IActionResult OnGet(string key)
        {
            // Validate the security key
            if (key != SecurityKey)
            {
                _logger.LogWarning("Invalid security key attempt: {Key}", key);
                IsValidRequest = false;
                return Page();
            }

            IsValidRequest = true;
            return Page();
        }

        public async Task<IActionResult> OnPostAsync(string key)
        {
            // Validate the security key
            if (key != SecurityKey)
            {
                _logger.LogWarning("Invalid security key attempt on POST: {Key}", key);
                IsValidRequest = false;
                return Page();
            }

            IsValidRequest = true;

            // Determine which query to execute: selected text or full query
            string queryToExecute;
            if (!string.IsNullOrWhiteSpace(SelectedText))
            {
                queryToExecute = SelectedText;
                UsedSelectedText = true;
            }
            else
            {
                queryToExecute = SqlQuery;
                UsedSelectedText = false;
            }

            if (string.IsNullOrWhiteSpace(queryToExecute))
            {
                ErrorMessage = "SQL query cannot be empty.";
                return Page();
            }

            var connectionString = _configuration.GetConnectionString("SQLite");
            if (string.IsNullOrEmpty(connectionString))
            {
                ErrorMessage = "Database connection string is not configured.";
                return Page();
            }

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                // Determine if this is a SELECT query
                IsSelectQuery = IsSelectStatement(queryToExecute);

                using (var connection = new SqliteConnection(connectionString))
                {
                    await connection.OpenAsync();

                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = queryToExecute;
                        command.CommandType = CommandType.Text;

                        if (IsSelectQuery)
                        {
                            // Execute SELECT query and fill DataTable
                            using (var reader = await command.ExecuteReaderAsync())
                            {
                                ResultTable = new DataTable();
                                ResultTable.Load(reader);
                                HasResults = ResultTable.Rows.Count > 0;
                                RowsAffected = ResultTable.Rows.Count;
                            }
                        }
                        else
                        {
                            // Execute non-SELECT query
                            RowsAffected = await command.ExecuteNonQueryAsync();
                            HasResults = false;
                        }
                    }
                }

                stopwatch.Stop();
                ExecutionTime = stopwatch.Elapsed.TotalMilliseconds;
                string queryType = UsedSelectedText ? "Selected text" : "Query";
                SuccessMessage = $"{queryType} executed successfully. {(IsSelectQuery ? $"{RowsAffected} rows returned" : $"{RowsAffected} rows affected")} in {ExecutionTime:F2} ms.";
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                ExecutionTime = stopwatch.Elapsed.TotalMilliseconds;
                string queryType = UsedSelectedText ? "selected text" : "query";
                ErrorMessage = $"Error executing {queryType}: {ex.Message}";
                _logger.LogError(ex, "Error executing SQL {QueryType}: {Query}", queryType, queryToExecute);
            }

            return Page();
        }

        private bool IsSelectStatement(string sql)
        {
            // Simple check to determine if this is a SELECT query
            // This is not foolproof but works for most cases
            var trimmedSql = sql.Trim().ToUpper();
            return trimmedSql.StartsWith("SELECT ") ||
                   trimmedSql.StartsWith("WITH ") ||
                   trimmedSql.StartsWith("PRAGMA ") ||
                   trimmedSql.StartsWith("EXPLAIN ");
        }
    }
}
