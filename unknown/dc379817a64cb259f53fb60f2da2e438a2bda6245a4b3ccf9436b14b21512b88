using Microsoft.AspNetCore.Mvc.RazorPages;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels.Bank;

namespace RazeWinComTr.Areas.Admin.Pages.Bank;

public class IndexModel : PageModel
{
    private readonly BankService _bankService;

    public IndexModel(BankService bankService)
    {
        _bankService = bankService;
    }

    public List<BankViewModel> Banks { get; set; } = new();

    public async Task OnGetAsync()
    {
        Banks = await _bankService.GetListAsync();
    }
}
