using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels;

namespace RazeWinComTr.Areas.Admin.Pages.Setting;

public class CreateModel : PageModel
{
    private readonly SettingService _settingService;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public CreateModel(
        SettingService settingService,
        IStringLocalizer<SharedResource> localizer)
    {
        _settingService = settingService;
        _localizer = localizer;
    }

    public SweetAlert2Message? AlertMessage { get; set; }

    [BindProperty] public SettingCreateViewModel ViewEntity { get; set; } = new();

    public string? ErrorMessage { get; set; }

    public async Task<IActionResult> OnPostAsync()
    {
        try
        {
            var userId = User.GetClaimUserId();
            if (!userId.HasValue) return Unauthorized();

            if (!ModelState.IsValid) return Page();

            // Check if setting with the same key already exists
            var existingSetting = await _settingService.GetByKeyAsync(ViewEntity.Key);
            if (existingSetting != null)
            {
                ModelState.AddModelError("ViewEntity.Key", _localizer["A setting with this key already exists"]);
                return Page();
            }

            var entity = new DbModel.Setting
            {
                Key = ViewEntity.Key,
                Value = ViewEntity.Value,
                Description = ViewEntity.Description ?? string.Empty,
                Group = ViewEntity.Group ?? "General",
                Order = ViewEntity.Order,
                CrDate = DateTime.UtcNow
            };
            await _settingService.CreateAsync(entity);

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Success"],
                Text = _localizer["Successfully saved"],
                Icon = "success",
                RedirectUrl = "/Admin/Setting"
            };

            return Page();
        }
        catch (Exception ex)
        {
            ErrorMessage = ex.Message;
            return Page();
        }
    }
}

public class SettingCreateViewModel
{
    public string Key { get; set; } = null!;
    public string? Value { get; set; }
    public string? Description { get; set; }
    public string? Group { get; set; }
    public int Order { get; set; } = 0;
}
