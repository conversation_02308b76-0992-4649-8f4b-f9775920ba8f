@page
@using Microsoft.Extensions.Localization
@model RazeWinComTr.Areas.Admin.Pages.User.IndexModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["Users"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

<div class="container-fluid">
    <h1 class="mt-4">
        @L["Users"]
    </h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/Admin">@L["Dashboard"]</a></li>
        <li class="breadcrumb-item active">@L["Users"]</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            @L["User List"]
            <button id="btnCreateNew" class="btn btn-success" onclick="location.href='@Url.Page("Create")';" aria-label="@L["Create a New"]">
                <i class="fas fa-plus fa-2x"></i>
            </button>
        </div>
        <div class="card-body">
            <table id="userTable" class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>@L["Email"]</th>
                        <th>@L["Full Name"]</th>
                        <th>@L["Phone Number"]</th>
                        <th>@L["Identity Number"]</th>
                        <th>@L["Balance"]</th>
                        <th>@L["Status"]</th>
                        <th>@L["Creation Date"]</th>
                        <th>@L["Actions"]</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.Items)
                    {
                        <tr>
                            <td>@item.Email</td>
                            <td>@item.FullName</td>
                            <td>@item.PhoneNumber</td>
                            <td>@item.IdentityNumber</td>
                            <td>@item.Balance.ToString("N2") @L["Currency_Symbol"]</td>
                            <td>
                                @if (item.IsActive == 1)
                                {
                                    <span class="badge bg-success">@L["Active"]</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger">@L["Inactive"]</span>
                                }
                            </td>
                            <td>@item.CrDate.ToLocalTime().ToString("dd/MM/yyyy HH:mm")</td>
                            <td>
                                <a asp-page="./Edit" asp-route-id="@item.UserId" class="btn btn-primary btn-sm">@L["Edit"]</a>
                                <a asp-page="./Delete" asp-route-id="@item.UserId" class="btn btn-danger btn-sm">@L["Delete"]</a>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            $('#userTable').DataTable({
                "order": [[6, "desc"]]
            });
        });
    </script>
}