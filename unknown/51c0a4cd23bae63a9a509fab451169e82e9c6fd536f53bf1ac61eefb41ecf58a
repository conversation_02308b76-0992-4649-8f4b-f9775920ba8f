@page
@using Microsoft.Extensions.Localization
@model IndexModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["Banks"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["Banks"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item active">@L["Banks"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <a id="btnCreateNew" asp-page="Create" class="btn btn-success" role="button"
                   aria-label="@($"{L["Create a New"]} {L["Bank"]}")" title="@($"{L["Create a New"]} {L["Bank"]}")" >
                    <i class="fas fa-plus fa-2x"></i>
                </a>
                <h3 class="card-title float-right">@L["Count"]: @(Model.Banks.Count)</h3>
            </div>
            <div class="card-body">
                <table class="table table-bordered table-striped datatable">
                    <thead>
                    <tr>
                        <th>@L["Bank Name"]</th>
                        <th>@L["Account Holder"]</th>
                        <th>@L["IBAN"]</th>
                        <th>@L["Status"]</th>
                        <th>@L["Order"]</th>
                        <th style="width: 150px">@L["Actions"]</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach (var item in Model.Banks)
                    {
                        <tr>
                            <td>@item.BankName</td>
                            <td>@item.AccountHolder</td>
                            <td>@item.Iban</td>
                            <td>@(item.IsActive ? L["Active"] : L["Inactive"])</td>
                            <td>@item.Order</td>
                            <td>
                                <a href="/Admin/Bank/Edit?id=@item.Id" class="btn btn-info btn-sm">
                                    <i class="fas fa-pencil-alt"></i>
                                </a>
                                <a href="/Admin/Bank/Delete?id=@item.Id" class="btn btn-danger btn-sm">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </td>
                        </tr>
                    }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>
