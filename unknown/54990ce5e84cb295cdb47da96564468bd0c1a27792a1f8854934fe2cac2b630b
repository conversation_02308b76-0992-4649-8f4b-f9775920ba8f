# Parça *******.3: Advanced Search (15-20 dakika)

## 📋 <PERSON><PERSON><PERSON>
Advanced search functionality, filter combinations ve state management'ın implementasyonu.

## 🎯 Hedefler
- ✅ Advanced search logic
- ✅ Filter combinations
- ✅ Search state management
- ✅ Performance optimization

## 📊 Yapılacak İşler

### ✅ YAPILACAKLAR

#### *******.3.1 Advanced Search JavaScript

**Dosya**: `src/wwwroot/js/rzw-savings-details.js` (Advanced search ekleme)
```javascript
// Advanced Search Manager
class AdvancedSearchManager {
    constructor(tableInstance) {
        this.table = tableInstance;
        this.searchHistory = [];
        this.maxHistoryItems = 10;
        this.searchTimeout = null;
        this.searchDelay = 300;
        
        this.init();
    }

    init() {
        this.bindAdvancedSearchEvents();
        this.loadSearchHistory();
    }

    bindAdvancedSearchEvents() {
        // Real-time search with debouncing
        const searchInputs = [
            'historySearch',
            'descriptionSearch', 
            'transactionHashSearch'
        ];

        searchInputs.forEach(inputId => {
            const input = document.getElementById(inputId);
            if (input) {
                input.addEventListener('input', (e) => {
                    this.handleSearchInput(e.target, inputId);
                });

                input.addEventListener('keydown', (e) => {
                    this.handleSearchKeydown(e, inputId);
                });

                input.addEventListener('focus', (e) => {
                    this.showSearchSuggestions(inputId);
                });
            }
        });

        // Search suggestions click
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('search-suggestion')) {
                this.applySuggestion(e.target);
            }
        });
    }

    handleSearchInput(input, inputId) {
        clearTimeout(this.searchTimeout);
        
        this.searchTimeout = setTimeout(() => {
            const query = input.value.trim();
            
            if (inputId === 'historySearch') {
                // Main search - apply immediately
                this.table.searchTerm = query;
                this.table.loadTableData();
                
                if (query) {
                    this.addToSearchHistory(query);
                }
            } else {
                // Filter modal searches - update filter state
                this.updateFilterSearch(inputId, query);
            }
            
            this.updateSearchSuggestions(inputId, query);
        }, this.searchDelay);
    }

    handleSearchKeydown(e, inputId) {
        if (e.key === 'Enter') {
            e.preventDefault();
            clearTimeout(this.searchTimeout);
            
            const query = e.target.value.trim();
            if (query) {
                this.executeSearch(inputId, query);
            }
        } else if (e.key === 'Escape') {
            this.hideSearchSuggestions(inputId);
            e.target.blur();
        } else if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
            this.navigateSuggestions(e, inputId);
        }
    }

    executeSearch(inputId, query) {
        if (inputId === 'historySearch') {
            this.table.searchTerm = query;
            this.table.loadTableData();
        }
        
        this.addToSearchHistory(query);
        this.hideSearchSuggestions(inputId);
    }

    updateFilterSearch(inputId, query) {
        if (!this.table.filterManager) return;
        
        if (inputId === 'descriptionSearch') {
            this.table.filterManager.filters.search.description = query;
        } else if (inputId === 'transactionHashSearch') {
            this.table.filterManager.filters.search.transactionHash = query;
        }
        
        this.table.filterManager.updateActiveFilters();
    }

    addToSearchHistory(query) {
        if (!query || this.searchHistory.includes(query)) return;
        
        this.searchHistory.unshift(query);
        if (this.searchHistory.length > this.maxHistoryItems) {
            this.searchHistory = this.searchHistory.slice(0, this.maxHistoryItems);
        }
        
        this.saveSearchHistory();
    }

    loadSearchHistory() {
        try {
            const saved = localStorage.getItem('rzw-savings-search-history');
            if (saved) {
                this.searchHistory = JSON.parse(saved);
            }
        } catch (error) {
            console.warn('Failed to load search history:', error);
            this.searchHistory = [];
        }
    }

    saveSearchHistory() {
        try {
            localStorage.setItem('rzw-savings-search-history', JSON.stringify(this.searchHistory));
        } catch (error) {
            console.warn('Failed to save search history:', error);
        }
    }

    showSearchSuggestions(inputId) {
        const input = document.getElementById(inputId);
        if (!input) return;

        const suggestions = this.generateSuggestions(inputId, input.value);
        if (suggestions.length === 0) return;

        this.renderSuggestions(input, suggestions);
    }

    generateSuggestions(inputId, currentValue) {
        const suggestions = [];
        
        // Add search history
        if (this.searchHistory.length > 0) {
            const filtered = this.searchHistory.filter(item => 
                item.toLowerCase().includes(currentValue.toLowerCase())
            );
            
            suggestions.push(...filtered.map(item => ({
                type: 'history',
                text: item,
                icon: 'fas fa-history'
            })));
        }
        
        // Add contextual suggestions based on input type
        if (inputId === 'transactionHashSearch') {
            if (currentValue.length >= 3) {
                suggestions.push({
                    type: 'suggestion',
                    text: 'Search by transaction hash prefix',
                    icon: 'fas fa-search'
                });
            }
        } else if (inputId === 'descriptionSearch') {
            const commonDescriptions = [
                'Daily Interest Payment',
                'Weekly Interest',
                'Monthly Interest',
                'Bonus Payment'
            ];
            
            const filtered = commonDescriptions.filter(desc =>
                desc.toLowerCase().includes(currentValue.toLowerCase())
            );
            
            suggestions.push(...filtered.map(desc => ({
                type: 'suggestion',
                text: desc,
                icon: 'fas fa-tag'
            })));
        }
        
        return suggestions.slice(0, 8); // Limit suggestions
    }

    renderSuggestions(input, suggestions) {
        // Remove existing suggestions
        this.hideSearchSuggestions();
        
        const container = document.createElement('div');
        container.className = 'search-suggestions';
        container.id = `suggestions-${input.id}`;
        
        container.innerHTML = suggestions.map(suggestion => `
            <div class="search-suggestion" data-value="${suggestion.text}" data-type="${suggestion.type}">
                <i class="${suggestion.icon}"></i>
                <span>${suggestion.text}</span>
                ${suggestion.type === 'history' ? '<i class="fas fa-times suggestion-remove" data-remove="true"></i>' : ''}
            </div>
        `).join('');
        
        // Position suggestions
        const rect = input.getBoundingClientRect();
        container.style.position = 'absolute';
        container.style.top = (rect.bottom + window.scrollY) + 'px';
        container.style.left = rect.left + 'px';
        container.style.width = rect.width + 'px';
        container.style.zIndex = '1050';
        
        document.body.appendChild(container);
        
        // Bind events
        container.addEventListener('click', (e) => {
            if (e.target.getAttribute('data-remove') === 'true') {
                this.removeFromHistory(e.target.closest('.search-suggestion').getAttribute('data-value'));
                e.stopPropagation();
            } else {
                this.applySuggestion(e.target.closest('.search-suggestion'));
            }
        });
        
        // Hide on outside click
        setTimeout(() => {
            document.addEventListener('click', this.hideSuggestionsOnOutsideClick.bind(this), { once: true });
        }, 100);
    }

    applySuggestion(suggestionElement) {
        if (!suggestionElement) return;
        
        const value = suggestionElement.getAttribute('data-value');
        const inputId = suggestionElement.closest('.search-suggestions').id.replace('suggestions-', '');
        const input = document.getElementById(inputId);
        
        if (input && value) {
            input.value = value;
            input.focus();
            
            // Trigger search
            this.executeSearch(inputId, value);
        }
        
        this.hideSearchSuggestions();
    }

    removeFromHistory(value) {
        this.searchHistory = this.searchHistory.filter(item => item !== value);
        this.saveSearchHistory();
        
        // Refresh suggestions
        const activeInput = document.querySelector('input:focus');
        if (activeInput) {
            this.showSearchSuggestions(activeInput.id);
        }
    }

    hideSearchSuggestions(inputId = null) {
        if (inputId) {
            const suggestions = document.getElementById(`suggestions-${inputId}`);
            if (suggestions) {
                suggestions.remove();
            }
        } else {
            document.querySelectorAll('.search-suggestions').forEach(el => el.remove());
        }
    }

    hideSuggestionsOnOutsideClick(e) {
        if (!e.target.closest('.search-suggestions') && !e.target.closest('input')) {
            this.hideSearchSuggestions();
        }
    }

    navigateSuggestions(e, inputId) {
        e.preventDefault();
        
        const suggestions = document.getElementById(`suggestions-${inputId}`);
        if (!suggestions) return;
        
        const items = suggestions.querySelectorAll('.search-suggestion');
        const current = suggestions.querySelector('.search-suggestion.active');
        let index = current ? Array.from(items).indexOf(current) : -1;
        
        // Remove current active
        if (current) {
            current.classList.remove('active');
        }
        
        // Calculate new index
        if (e.key === 'ArrowDown') {
            index = (index + 1) % items.length;
        } else if (e.key === 'ArrowUp') {
            index = index <= 0 ? items.length - 1 : index - 1;
        }
        
        // Set new active
        if (items[index]) {
            items[index].classList.add('active');
            items[index].scrollIntoView({ block: 'nearest' });
        }
    }

    clearSearchHistory() {
        this.searchHistory = [];
        this.saveSearchHistory();
        this.hideSearchSuggestions();
    }

    // Advanced search with multiple criteria
    performAdvancedSearch(criteria) {
        const searchParams = {
            ...this.table.getBaseParams(),
            ...criteria
        };
        
        return this.table.loadTableDataWithParams(searchParams);
    }

    // Search analytics
    getSearchAnalytics() {
        return {
            historyCount: this.searchHistory.length,
            mostSearched: this.getMostSearchedTerms(),
            recentSearches: this.searchHistory.slice(0, 5)
        };
    }

    getMostSearchedTerms() {
        const frequency = {};
        this.searchHistory.forEach(term => {
            frequency[term] = (frequency[term] || 0) + 1;
        });
        
        return Object.entries(frequency)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5)
            .map(([term, count]) => ({ term, count }));
    }
}

// Enhanced table search functionality
InterestHistoryTable.prototype.getBaseParams = function() {
    return {
        page: this.currentPage,
        pageSize: this.pageSize,
        sortColumn: this.sortColumn,
        sortDirection: this.sortDirection,
        search: this.searchTerm
    };
};

InterestHistoryTable.prototype.loadTableDataWithParams = function(params) {
    return this.loadTableDataInternal(params);
};

InterestHistoryTable.prototype.loadTableDataInternal = async function(customParams = null) {
    try {
        this.showLoading();
        
        const params = customParams || {
            ...this.getBaseParams(),
            ...this.filters
        };

        const queryString = new URLSearchParams();
        
        // Add basic params
        Object.entries(params).forEach(([key, value]) => {
            if (value !== null && value !== undefined && value !== '') {
                if (typeof value === 'object') {
                    queryString.append(key, JSON.stringify(value));
                } else {
                    queryString.append(key, value);
                }
            }
        });

        const response = await fetch(`/MyAccount/RzwSavings/Details/${this.accountId}/InterestHistory?${queryString}`, {
            method: 'GET',
            headers: {
                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
            }
        });

        const result = await response.json();

        if (result.success) {
            this.updateTableContent(result.data);
            this.updatePagination(result.pagination);
            this.updateTableInfo(result.pagination);
            
            // Update search analytics
            if (this.searchManager) {
                this.searchManager.updateSearchResults(result.data.length, result.pagination.totalItems);
            }
        } else {
            this.showError(result.message);
        }
    } catch (error) {
        console.error('Error loading table data:', error);
        this.showError('Failed to load payment history');
    } finally {
        this.hideLoading();
    }
};

// Initialize advanced search
InterestHistoryTable.prototype.initAdvancedSearch = function() {
    this.searchManager = new AdvancedSearchManager(this);
};

// Update main initialization
document.addEventListener('DOMContentLoaded', function() {
    const accountId = window.rzwSavingsDetails?.accountId;
    if (accountId) {
        window.interestHistoryTable = new InterestHistoryTable(accountId);
        window.interestHistoryTable.initPagination();
        window.interestHistoryTable.initFilters();
        window.interestHistoryTable.initAdvancedSearch();
    }
});

// Global search functions
function clearSearchHistory() {
    if (window.interestHistoryTable?.searchManager) {
        window.interestHistoryTable.searchManager.clearSearchHistory();
        showToast('Search history cleared', 'success');
    }
}

function getSearchAnalytics() {
    if (window.interestHistoryTable?.searchManager) {
        return window.interestHistoryTable.searchManager.getSearchAnalytics();
    }
    return null;
}
```

#### *******.3.2 Search Suggestions CSS

**Dosya**: `src/wwwroot/css/rzw-savings-details.css` (Search suggestions styles ekleme)
```css
/* Search Suggestions */
.search-suggestions {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1050;
}

.search-suggestion {
    padding: 10px 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.2s;
}

.search-suggestion:last-child {
    border-bottom: none;
}

.search-suggestion:hover,
.search-suggestion.active {
    background-color: #f8f9fa;
}

.search-suggestion i:first-child {
    color: #6c757d;
    width: 16px;
    text-align: center;
}

.search-suggestion span {
    flex: 1;
    color: #495057;
    font-size: 0.9rem;
}

.suggestion-remove {
    color: #dc3545;
    opacity: 0.6;
    transition: opacity 0.2s;
    padding: 2px;
    border-radius: 3px;
}

.suggestion-remove:hover {
    opacity: 1;
    background-color: #f8d7da;
}

/* Filter Modal Enhancements */
.filter-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.filter-section-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1rem;
}

.filter-section-title i {
    color: #667eea;
    width: 18px;
    text-align: center;
}

/* Date Range Presets */
.date-range-presets .btn-group {
    display: flex;
    flex-wrap: wrap;
    gap: 2px;
}

.date-range-presets .btn {
    flex: 1;
    min-width: 0;
    font-size: 0.85rem;
    padding: 6px 8px;
}

/* Amount Presets */
.amount-presets .btn-group {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.amount-presets .btn {
    font-size: 0.8rem;
    padding: 4px 8px;
}

/* Status Checkboxes */
.status-checkboxes {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.status-checkboxes .form-check {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    transition: all 0.2s;
}

.status-checkboxes .form-check:hover {
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

.status-checkboxes .form-check-input:checked + .form-check-label {
    font-weight: 500;
}

/* Active Filters */
.active-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.active-filters .badge {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 10px;
    font-size: 0.8rem;
    font-weight: 500;
}

.active-filters .btn-close {
    font-size: 0.7rem;
    padding: 0;
    margin: 0;
    width: 14px;
    height: 14px;
    opacity: 0.8;
}

.active-filters .btn-close:hover {
    opacity: 1;
}

/* Validation Messages */
.date-validation-message,
.amount-validation-message {
    margin-top: 8px;
    font-size: 0.85rem;
    padding: 6px 10px;
    border-radius: 4px;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
}

.date-validation-message:empty,
.amount-validation-message:empty {
    display: none;
}

/* Filter Actions */
.filter-actions {
    background: #f8f9fa;
    margin: -1rem -1rem 0 -1rem;
    padding: 1rem;
    border-top: 1px solid #dee2e6;
}

.filter-info {
    display: flex;
    align-items: center;
}

.filter-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .search-suggestions {
        left: 10px !important;
        right: 10px !important;
        width: auto !important;
    }
    
    .filter-section {
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .date-range-presets .btn-group {
        flex-direction: column;
    }
    
    .date-range-presets .btn {
        flex: none;
    }
    
    .amount-presets .btn-group {
        justify-content: center;
    }
    
    .status-checkboxes .form-check {
        padding: 6px 10px;
    }
    
    .filter-actions {
        padding: 15px;
    }
    
    .filter-actions .d-flex {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .filter-buttons {
        justify-content: space-between;
        width: 100%;
    }
}

@media (max-width: 576px) {
    .search-suggestion {
        padding: 8px 12px;
    }
    
    .search-suggestion span {
        font-size: 0.85rem;
    }
    
    .filter-section-title {
        font-size: 0.9rem;
    }
    
    .filter-buttons {
        flex-direction: column;
        width: 100%;
    }
    
    .filter-buttons .btn {
        width: 100%;
    }
}

/* Dark Mode */
@media (prefers-color-scheme: dark) {
    .search-suggestions {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .search-suggestion {
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .search-suggestion:hover,
    .search-suggestion.active {
        background-color: #4a5568;
    }
    
    .filter-section {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .status-checkboxes .form-check {
        background: #1a202c;
        border-color: #4a5568;
    }
    
    .status-checkboxes .form-check:hover {
        background-color: #2d3748;
    }
    
    .filter-actions {
        background: #2d3748;
        border-color: #4a5568;
    }
}

/* Animation for search results */
.search-results-updating {
    opacity: 0.6;
}

.search-results-updated {
    animation: searchResultsUpdate 0.4s ease-out;
}

@keyframes searchResultsUpdate {
    0% { opacity: 0; transform: translateY(10px); }
    100% { opacity: 1; transform: translateY(0); }
}
```

## 📋 Parça Kontrol Listesi

### ✅ YAPILACAKLAR
- [ ] Advanced search manager
- [ ] Search suggestions system
- [ ] Search history management
- [ ] Filter combinations
- [ ] Performance optimization
- [ ] Search analytics
- [ ] CSS styling
- [ ] Responsive design

### 🔄 YAPILMAKTA OLANLAR
- (Henüz başlanmadı)

### ✅ YAPILMIŞLAR
- (Henüz tamamlanmadı)

## 📝 Notlar

### Advanced Search Features
- **Search suggestions**: History-based and contextual
- **Real-time search**: Debounced input handling
- **Search history**: Local storage persistence
- **Keyboard navigation**: Arrow keys and shortcuts
- **Multiple criteria**: Combined filter searches

### Performance Optimizations
- **Debounced input**: Prevents excessive API calls
- **Local storage**: Cached search history
- **Efficient rendering**: Optimized DOM updates
- **Memory management**: Proper cleanup

### User Experience
- **Intuitive interface**: Easy-to-use search
- **Visual feedback**: Loading and result states
- **Accessibility**: Keyboard navigation support
- **Mobile optimization**: Touch-friendly design

## 🏁 Alt Adım ******* Tamamlandı

Bu parça ile **Alt Adım *******: Pagination ve Filtering** tamamen tamamlanmış oldu.

### Tamamlanan Bileşenler:
1. ✅ **Parça *******.1**: Pagination Controls
2. ✅ **Parça *******.2**: Date Range Filtering  
3. ✅ **Parça *******.3**: Advanced Search

### Sonraki Alt Adım
Bu alt adım tamamlandıktan sonra **Alt Adım *******: Export Functionality** başlayacak.

---
**Tahmini Süre**: 15-20 dakika
**Öncelik**: Yüksek
**Bağımlılıklar**: Parça *******.1 ve *******.2 tamamlanmış olmalı
