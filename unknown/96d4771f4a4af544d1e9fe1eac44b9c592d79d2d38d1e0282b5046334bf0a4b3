@page
@using Microsoft.Extensions.Localization
@model RazeWinComTr.Areas.Admin.Pages.Withdrawal.CreateModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = $"{L["New"]} {L["Withdrawal"]}";
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

@if (Model.AlertMessage != null)
{
    <script id="scriptMessage" type="text/javascript">
        window.onload = function () {
            Swal.fire({
                title: '@Html.Raw(Model.AlertMessage.Title)',
                text: '@Html.Raw(Model.AlertMessage.Text)',
                icon: '@Html.Raw(Model.AlertMessage.Icon)'
            }).then((result) => {
                var redirectUrl = '@(Model.AlertMessage.RedirectUrl ?? string.Empty)';
                if (result.isConfirmed && redirectUrl !== '') {
                    location.href = redirectUrl;
                }
            });
            $('#scriptMessage').remove();
        }
    </script>
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["Create a New Withdrawal"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="/admin/withdrawal">@L["Withdrawals"]</a></li>
                    <li class="breadcrumb-item active">@L["New"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>
<div asp-validation-summary="ModelOnly" class="text-danger"></div>
<section class="content">
    <div class="container-fluid">
        <div class="card">
            <form method="post">
                <div class="card-body">
                    <div class="form-group">
                        <label asp-for="ViewEntity.UserId">@L["User"]</label>
                        <select asp-for="ViewEntity.UserId" class="form-control" required>
                            <option value="">@L["Select User"]</option>
                            @foreach (var user in Model.Users)
                            {
                                <option value="@user.UserId">@user.Email</option>
                            }
                        </select>
                    </div>
                    <div class="form-group">
                        <label asp-for="ViewEntity.AccountHolder">@L["Account Holder"]</label>
                        <input asp-for="ViewEntity.AccountHolder" class="form-control" required/>
                    </div>
                    <div class="form-group">
                        <label asp-for="ViewEntity.Iban">@L["IBAN"]</label>
                        <input asp-for="ViewEntity.Iban" class="form-control" required/>
                    </div>
                    <div class="form-group">
                        <label asp-for="ViewEntity.Amount">@L["Amount (TRY)"]</label>
                        <input asp-for="ViewEntity.Amount" class="form-control" type="number" step="0.01" required/>
                    </div>
                    <div class="form-group">
                        <label asp-for="ViewEntity.Status">@L["Status"]</label>
                        <select asp-for="ViewEntity.Status" class="form-control" required>
                            <option value="0">@L["Pending"]</option>
                            <option value="1">@L["Approved"]</option>
                            <option value="2">@L["Rejected"]</option>
                        </select>
                    </div>

                </div>
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-1"></i> @L["Save"]
                    </button>
                    <a href="/Admin/Withdrawal" class="btn btn-default">
                        <i class="fas fa-times mr-1"></i> @L["Cancel"]
                    </a>
                </div>
            </form>
        </div>
    </div>
</section>
