@page
@model RazeWinComTr.Areas.MyAccount.Pages.PaymentModel
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.DbModel
@using RazeWinComTr.Areas.Admin.Enums
@inject IStringLocalizer<SharedResource> Localizer
@{
    ViewData["Title"] = Localizer["My Payment"];
}

@section Styles {
    <!-- DataTables CSS from local files -->
    <link rel="stylesheet" href="/plugins/datatables/css/jquery.dataTables.min.css">
    <link rel="stylesheet" href="/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="/plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
    <link rel="stylesheet" href="/plugins/datatables-buttons/css/buttons.bootstrap4.min.css">

    <!-- Select2 CSS from local files -->
    <link rel="stylesheet" href="/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css">

    <style>
        /* Custom styles for better readability */
        body {
            color: #333 !important;
        }

        /* Custom styles for the payment table */
        .custom-payment-table {
            color: #333;
            background-color: #fff;
            border-collapse: collapse;
            width: 100%;
        }

        .custom-payment-table thead th {
            color: #fff !important;
            background-color: #343a40 !important;
            border-color: #454d55 !important;
            font-weight: bold;
            padding: 10px;
            text-align: left;
        }

        .custom-payment-table tbody td {
            color: #333 !important;
            background-color: #fff !important;
            padding: 8px;
            border: 1px solid #dee2e6;
        }

        .custom-payment-table tfoot th {
            color: #fff !important;
            background-color: #343a40 !important;
            border-color: #454d55 !important;
            font-weight: bold;
            padding: 10px;
        }

        /* Striped rows */
        .custom-payment-table tbody tr:nth-of-type(odd) {
            background-color: rgba(0, 0, 0, 0.05) !important;
        }

        /* Hover effect */
        .custom-payment-table tbody tr:hover {
            color: #333 !important;
            background-color: rgba(0, 0, 0, 0.075) !important;
        }

        /* DataTables specific styling */
        .dataTables_wrapper .dataTables_length,
        .dataTables_wrapper .dataTables_filter,
        .dataTables_wrapper .dataTables_info,
        .dataTables_wrapper .dataTables_processing,
        .dataTables_wrapper .dataTables_paginate {
            color: #333 !important;
            margin-bottom: 10px;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button {
            color: #333 !important;
            background-color: #f8f9fa !important;
            border: 1px solid #dee2e6 !important;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.current,
        .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
            color: #fff !important;
            background-color: #007bff !important;
            border-color: #007bff !important;
        }

        /* Fix for DataTables buttons */
        .dt-buttons .btn {
            color: #333 !important;
            background-color: #f8f9fa !important;
            border: 1px solid #dee2e6 !important;
            margin-right: 5px;
        }

        .dt-buttons .btn:hover {
            color: #333 !important;
            background-color: #e2e6ea !important;
            border-color: #dae0e5 !important;
        }

        /* Form elements */
        .form-control {
            color: #333 !important;
        }

        /* Card text */
        .card-body {
            color: #333 !important;
        }

        /* Alert text */
        .alert {
            color: #333 !important;
        }
    </style>
}
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@Localizer["My Payment"]</h1>
            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/MyAccount/Dashboard">@Localizer["Home"]</a></li>
                    <li class="breadcrumb-item active">@Localizer["My Payment"]</li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->
    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-6">
                <div class="card card-primary">
                    <div class="card-header">
                        <h3 class="card-title">@Localizer["Deposit Funds"]</h3>
                    </div>
                    <!-- /.card-header -->
                    <!-- form start -->
                    <form method="post">
                        <div class="card-body">
                            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                            <div class="form-group">
                                <label asp-for="PaymentInput.Amount">@Localizer["Amount"] (₺)</label>
                                <input asp-for="PaymentInput.Amount" class="form-control" placeholder="@Localizer["Enter amount"]">
                                <span asp-validation-for="PaymentInput.Amount" class="text-danger"></span>
                            </div>
                            <div class="form-group">
                                <label asp-for="PaymentInput.BankId">@Localizer["Bank Account"]</label>
                                <select asp-for="PaymentInput.BankId" class="form-control select2" style="width: 100%;">
                                    <option value="">@Localizer["Select Bank Account"]</option>
                                    @foreach (var bank in Model.Banks)
                                    {
                                        <option value="@bank.Id">@bank.BankName - @bank.AccountHolder</option>
                                    }
                                </select>
                                <span asp-validation-for="PaymentInput.BankId" class="text-danger"></span>
                            </div>
                            <div class="form-group">
                                <label asp-for="PaymentInput.TransactionReference">@Localizer["Transaction Reference"]</label>
                                <input asp-for="PaymentInput.TransactionReference" class="form-control" placeholder="@Localizer["Enter transaction reference"]">
                                <span asp-validation-for="PaymentInput.TransactionReference" class="text-danger"></span>
                                <small class="form-text text-muted">@Localizer["Please enter the reference number or description of your bank transfer."]</small>
                            </div>
                        </div>
                        <!-- /.card-body -->
                        <div class="card-footer">
                            <button type="submit" class="btn btn-primary">@Localizer["Submit"]</button>
                        </div>
                    </form>
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
            <div class="col-md-6">
                <div class="card card-info">
                    <div class="card-header">
                        <h3 class="card-title">@Localizer["Payment Instructions"]</h3>
                    </div>
                    <div class="card-body">
                        <p>@Localizer["To deposit funds into your account, please follow these steps:"]</p>
                        <ol>
                            <li>@Localizer["Select a bank account from the dropdown menu."]</li>
                            <li>@Localizer["Transfer the amount you wish to deposit to the selected bank account."]</li>
                            <li>@Localizer["Enter the amount and transaction reference in the form."]</li>
                            <li>@Localizer["Submit the form to notify us of your payment."]</li>
                            <li>@Localizer["Your account will be credited once the payment is verified."]</li>
                        </ol>
                        <div class="alert alert-warning">
                            <h5><i class="icon fas fa-exclamation-triangle"></i> @Localizer["Important!"]</h5>
                            @Localizer["Please make sure to include your email address or user ID in the transaction description to help us identify your payment."]
                        </div>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">@Localizer["Payment History"]</h3>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <table id="example1" class="table table-bordered table-striped custom-payment-table">
                            <thead>
                                <tr>
                                    <th>@Localizer["Date"]</th>
                                    <th>@Localizer["Payment Type"]</th>
                                    <th>@Localizer["Amount"]</th>
                                    <th>@Localizer["Status"]</th>
                                    <th>@Localizer["Process Status"]</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var deposit in Model.Deposits)
                                {
                                    <tr>
                                        <td>@deposit.CreatedDate.ToLocalTime().ToString("dd.MM.yyyy HH:mm")</td>
                                        <td>@deposit.DepositType</td>
                                        <td>@deposit.Amount.ToString("N2") ₺</td>
                                        <td>
                                            @if (deposit.Status == DepositStatus.Pending)
                                            {
                                                <span class="badge badge-warning">@Localizer["Pending"]</span>
                                            }
                                            else if (deposit.Status == DepositStatus.Approved)
                                            {
                                                <span class="badge badge-success">@Localizer["Approved"]</span>
                                            }
                                            else
                                            {
                                                <span class="badge badge-danger">@Localizer["Rejected"]</span>
                                            }
                                        </td>
                                        <td>@deposit.ProcessStatus</td>
                                    </tr>
                                }
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th>@Localizer["Date"]</th>
                                    <th>@Localizer["Payment Type"]</th>
                                    <th>@Localizer["Amount"]</th>
                                    <th>@Localizer["Status"]</th>
                                    <th>@Localizer["Process Status"]</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->

@section Scripts {
    <!-- jQuery first, then Bootstrap, then other libraries -->
    <script src="/plugins/jquery/jquery.min.js"></script>
    <script src="/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery Validation from CDN -->
    <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/additional-methods.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery-validation-unobtrusive@4.0.0/dist/jquery.validate.unobtrusive.min.js"></script>

    <!-- DataTables & Plugins from local files -->
    <script src="/plugins/datatables/js/jquery.dataTables.min.js"></script>
    <script src="/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="/plugins/datatables-responsive/js/dataTables.responsive.min.js"></script>
    <script src="/plugins/datatables-responsive/js/responsive.bootstrap4.min.js"></script>
    <script src="/plugins/datatables-buttons/js/dataTables.buttons.min.js"></script>
    <script src="/plugins/datatables-buttons/js/buttons.bootstrap4.min.js"></script>
    <script src="/plugins/jszip/jszip.min.js"></script>
    <script src="/plugins/pdfmake/pdfmake.min.js"></script>
    <script src="/plugins/pdfmake/vfs_fonts.js"></script>
    <script src="/plugins/datatables-buttons/js/buttons.html5.min.js"></script>
    <script src="/plugins/datatables-buttons/js/buttons.print.min.js"></script>
    <script src="/plugins/datatables-buttons/js/buttons.colVis.min.js"></script>

    <!-- Select2 from local files -->
    <script src="/plugins/select2/js/select2.full.min.js"></script>

    <script>
        // Add date sorting plugin for European date format (dd.MM.yyyy HH:mm)
        $.fn.dataTable.ext.type.order['date-eu-pre'] = function(data) {
            if (!data) {
                return 0;
            }

            // Extract date parts from format dd.MM.yyyy HH:mm
            var parts = data.split(' ');
            var dateParts = parts[0].split('.');
            var timeParts = parts[1].split(':');

            // Create a date object using the parts
            var day = parseInt(dateParts[0], 10);
            var month = parseInt(dateParts[1], 10) - 1; // Months are 0-based in JS
            var year = parseInt(dateParts[2], 10);
            var hour = parseInt(timeParts[0], 10);
            var minute = parseInt(timeParts[1], 10);

            // Create a sortable value (timestamp)
            var date = new Date(year, month, day, hour, minute, 0);
            return date.getTime();
        };

        $(document).ready(function() {
            try {
                //Initialize Select2 Elements
                $('.select2').select2();

                console.log("DataTable initialization starting...");
                var table = $("#example1").DataTable({
                    "responsive": true,
                    "lengthChange": false,
                    "autoWidth": false,
                    "buttons": ["copy", "csv", "excel", "pdf", "print", "colvis"],
                    "order": [[0, "desc"]],
                    "columnDefs": [
                        {
                            "targets": 0,
                            "type": "date-eu"
                        }
                    ],
                    "initComplete": function() {
                        // Force initial sort
                        this.api().order([0, 'desc']).draw();
                    },
                    "language": {
                        "search": "Ara:",
                        "paginate": {
                            "first": "İlk",
                            "last": "Son",
                            "next": "Sonraki",
                            "previous": "Önceki"
                        },
                        "info": "_TOTAL_ kayıttan _START_ - _END_ arası gösteriliyor",
                        "infoEmpty": "Kayıt yok",
                        "infoFiltered": "(_MAX_ kayıt içerisinden filtrelendi)",
                        "zeroRecords": "Eşleşen kayıt bulunamadı"
                    }
                });

                console.log("DataTable initialized successfully");

                // Add buttons to container
                table.buttons().container().appendTo('#example1_wrapper .col-md-6:eq(0)');

                // Apply custom styling to DataTables elements
                $('.dataTables_filter input').addClass('form-control').css({
                    'display': 'inline-block',
                    'width': 'auto',
                    'margin-left': '10px'
                });

                $('.dataTables_length select').addClass('form-control').css({
                    'display': 'inline-block',
                    'width': 'auto',
                    'margin-left': '10px',
                    'margin-right': '10px'
                });

                // Show success message if exists
                @if (!string.IsNullOrEmpty(Model.SuccessMessage))
                {
                    <text>
                    toastr.success('@Model.SuccessMessage');
                    </text>
                }

                // Show error message if exists
                @if (!string.IsNullOrEmpty(Model.ErrorMessage))
                {
                    <text>
                    toastr.error('@Model.ErrorMessage');
                    </text>
                }
            } catch (e) {
                console.error("Error initializing DataTable:", e);
            }
        });
    </script>
}
