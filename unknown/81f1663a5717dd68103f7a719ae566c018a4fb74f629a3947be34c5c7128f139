# Adım 5.3.1: PageModel ve Form Structure (2-3 saat)

## 📋 Adım Özeti
Yeni vadeli hesap oluşturma sayfası için PageModel, ViewModel'ler ve form yapısının oluşturulması. Validation rules ve service entegrasyonu.

## 🎯 Hedefler
- ✅ RzwSavingsCreateModel PageModel oluşturma
- ✅ Form ViewModel'leri oluşturma
- ✅ Validation attributes ekleme
- ✅ Service entegrasyonu
- ✅ Error handling

## 📊 Yapılacak İşler

### ✅ YAPILACAKLAR

#### 5.3.1.1 Create Form ViewModel'leri

**Dosya**: `src/Areas/MyAccount/ViewModels/RzwSavings/RzwSavingsCreateViewModel.cs`
```csharp
using System.ComponentModel.DataAnnotations;

namespace RazeWinComTr.Areas.MyAccount.ViewModels.RzwSavings
{
    public class RzwSavingsCreateViewModel
    {
        public List<RzwSavingsPlanOption> AvailablePlans { get; set; } = new();
        public RzwBalanceInfo UserBalance { get; set; } = new();
        public CreateSavingsForm Form { get; set; } = new();
        public SavingsCalculationResult? Calculation { get; set; }
        public string PageTitle { get; set; } = string.Empty;
        public string PageSubtitle { get; set; } = string.Empty;
        public bool HasAvailablePlans => AvailablePlans.Any();
        public bool HasSufficientBalance => UserBalance.AvailableBalance > 0;
        public decimal MinimumAmount => AvailablePlans.Any() ? AvailablePlans.Min(p => p.MinRzwAmount) : 0;
        public decimal MaximumAmount => UserBalance.AvailableBalance;
    }

    public class CreateSavingsForm
    {
        [Required(ErrorMessage = "Please select a savings plan")]
        [Range(1, int.MaxValue, ErrorMessage = "Please select a valid savings plan")]
        public int SelectedPlanId { get; set; }

        [Required(ErrorMessage = "Please enter the amount")]
        [Range(0.00000001, double.MaxValue, ErrorMessage = "Amount must be greater than 0")]
        public decimal Amount { get; set; }

        [Display(Name = "Auto Renew")]
        public bool AutoRenew { get; set; } = false;

        [Required(ErrorMessage = "You must accept the terms and conditions")]
        [Range(typeof(bool), "true", "true", ErrorMessage = "You must accept the terms and conditions")]
        public bool AcceptTerms { get; set; } = false;

        // Hesaplanmış alanlar
        public string FormattedAmount => Amount.ToString("N8");
        public bool IsValidAmount => Amount > 0;
        public bool HasSelectedPlan => SelectedPlanId > 0;
        public bool IsFormValid => HasSelectedPlan && IsValidAmount && AcceptTerms;
    }

    public class RzwSavingsPlanOption
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string TermType { get; set; } = string.Empty;
        public int TermDuration { get; set; }
        public decimal InterestRate { get; set; }
        public decimal MinRzwAmount { get; set; }
        public decimal? MaxRzwAmount { get; set; }
        public string? Description { get; set; }
        public bool IsActive { get; set; }
        public bool IsRecommended { get; set; }
        public bool IsPopular { get; set; }

        // Display properties
        public string FormattedInterestRate => (InterestRate * 100).ToString("N2") + "%";
        public string FormattedMinAmount => MinRzwAmount.ToString("N0");
        public string FormattedMaxAmount => MaxRzwAmount?.ToString("N0") ?? "Unlimited";
        
        public string TermDisplayText => TermType switch
        {
            RzwSavingsTermType.Daily => $"{TermDuration} Day{(TermDuration > 1 ? "s" : "")}",
            RzwSavingsTermType.Monthly => $"{TermDuration / 30} Month{(TermDuration > 30 ? "s" : "")}",
            RzwSavingsTermType.Yearly => $"{TermDuration / 365} Year{(TermDuration > 365 ? "s" : "")}",
            _ => $"{TermDuration} Days"
        };

        public decimal AnnualizedReturn => TermType switch
        {
            RzwSavingsTermType.Daily => InterestRate * 365,
            RzwSavingsTermType.Monthly => InterestRate * 12,
            RzwSavingsTermType.Yearly => InterestRate,
            _ => InterestRate
        };

        public string FormattedAnnualizedReturn => (AnnualizedReturn * 100).ToString("N1") + "% APY";

        public string PlanBadgeClass => TermType switch
        {
            RzwSavingsTermType.Daily => "badge-primary",
            RzwSavingsTermType.Monthly => "badge-success",
            RzwSavingsTermType.Yearly => "badge-info",
            _ => "badge-secondary"
        };

        public string PlanCardClass => TermType switch
        {
            RzwSavingsTermType.Daily => "plan-card-daily",
            RzwSavingsTermType.Monthly => "plan-card-monthly",
            RzwSavingsTermType.Yearly => "plan-card-yearly",
            _ => "plan-card-default"
        };

        // Validation
        public bool IsAmountValid(decimal amount) => 
            amount >= MinRzwAmount && (!MaxRzwAmount.HasValue || amount <= MaxRzwAmount.Value);

        public string GetAmountValidationMessage(decimal amount)
        {
            if (amount < MinRzwAmount)
                return $"Minimum amount is {FormattedMinAmount} RZW";
            
            if (MaxRzwAmount.HasValue && amount > MaxRzwAmount.Value)
                return $"Maximum amount is {FormattedMaxAmount} RZW";
            
            return string.Empty;
        }
    }

    public class SavingsCalculationResult
    {
        public decimal PrincipalAmount { get; set; }
        public decimal InterestRate { get; set; }
        public string TermType { get; set; } = string.Empty;
        public int TermDuration { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime MaturityDate { get; set; }
        
        // Calculated values
        public decimal DailyInterest { get; set; }
        public decimal MonthlyInterest { get; set; }
        public decimal TotalInterest { get; set; }
        public decimal MaturityAmount { get; set; }
        public decimal EffectiveAnnualRate { get; set; }

        // Formatted display values
        public string FormattedPrincipal => PrincipalAmount.ToString("N8");
        public string FormattedDailyInterest => DailyInterest.ToString("N8");
        public string FormattedMonthlyInterest => MonthlyInterest.ToString("N8");
        public string FormattedTotalInterest => TotalInterest.ToString("N8");
        public string FormattedMaturityAmount => MaturityAmount.ToString("N8");
        public string FormattedInterestRate => (InterestRate * 100).ToString("N2") + "%";
        public string FormattedEffectiveRate => (EffectiveAnnualRate * 100).ToString("N2") + "% APY";
        public string FormattedStartDate => StartDate.ToString("dd.MM.yyyy");
        public string FormattedMaturityDate => MaturityDate.ToString("dd.MM.yyyy");
        
        // Term information
        public int DaysToMaturity => (MaturityDate - StartDate).Days;
        public string TermDisplayText => TermType switch
        {
            RzwSavingsTermType.Daily => $"{TermDuration} Day{(TermDuration > 1 ? "s" : "")}",
            RzwSavingsTermType.Monthly => $"{TermDuration / 30} Month{(TermDuration > 30 ? "s" : "")}",
            RzwSavingsTermType.Yearly => $"{TermDuration / 365} Year{(TermDuration > 365 ? "s" : "")}",
            _ => $"{TermDuration} Days"
        };

        // ROI calculations
        public decimal ROIPercentage => PrincipalAmount > 0 ? (TotalInterest / PrincipalAmount * 100) : 0;
        public string FormattedROI => ROIPercentage.ToString("N2") + "%";
    }

    public class CreateSavingsValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        
        public void AddError(string error) => Errors.Add(error);
        public void AddWarning(string warning) => Warnings.Add(warning);
        public bool HasErrors => Errors.Any();
        public bool HasWarnings => Warnings.Any();
    }
}
```

#### ******* Create PageModel

**Dosya**: `src/Areas/MyAccount/Pages/RzwSavings/Create.cshtml.cs`
```csharp
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.MyAccount.ViewModels.RzwSavings;
using RazeWinComTr.Extensions;

namespace RazeWinComTr.Areas.MyAccount.Pages.RzwSavings
{
    [Authorize(Policy = StaticConfig.UserPolicyName)]
    public class CreateModel : PageModel
    {
        private readonly RzwSavingsService _savingsService;
        private readonly RzwSavingsPlanService _planService;
        private readonly RzwBalanceManagementService _balanceService;
        private readonly RzwSavingsInterestService _interestService;
        private readonly IStringLocalizer<SharedResource> _localizer;
        private readonly ILogger<CreateModel> _logger;

        public CreateModel(
            RzwSavingsService savingsService,
            RzwSavingsPlanService planService,
            RzwBalanceManagementService balanceService,
            RzwSavingsInterestService interestService,
            IStringLocalizer<SharedResource> localizer,
            ILogger<CreateModel> logger)
        {
            _savingsService = savingsService;
            _planService = planService;
            _balanceService = balanceService;
            _interestService = interestService;
            _localizer = localizer;
            _logger = logger;
        }

        public RzwSavingsCreateViewModel ViewModel { get; set; } = new();

        [BindProperty]
        public CreateSavingsForm Form { get; set; } = new();

        public async Task<IActionResult> OnGetAsync()
        {
            try
            {
                var userId = HttpContext.User.GetClaimUserId();
                if (!userId.HasValue)
                {
                    return RedirectToPage("/Account/Login");
                }

                await LoadViewModelAsync(userId.Value);
                return Page();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading create savings page");
                TempData["ErrorMessage"] = _localizer["An error occurred while loading the page"];
                return RedirectToPage("./Index");
            }
        }

        public async Task<IActionResult> OnPostAsync()
        {
            try
            {
                var userId = HttpContext.User.GetClaimUserId();
                if (!userId.HasValue)
                {
                    return RedirectToPage("/Account/Login");
                }

                // Load ViewModel for display
                await LoadViewModelAsync(userId.Value);

                // Model validation
                if (!ModelState.IsValid)
                {
                    return Page();
                }

                // Business validation
                var validationResult = await ValidateCreateRequestAsync(userId.Value, Form);
                if (!validationResult.IsValid)
                {
                    foreach (var error in validationResult.Errors)
                    {
                        ModelState.AddModelError(string.Empty, error);
                    }
                    return Page();
                }

                // Create savings account
                var (success, message, account) = await _savingsService.CreateSavingsAccountAsync(
                    userId.Value, Form.SelectedPlanId, Form.Amount, Form.AutoRenew);

                if (success && account != null)
                {
                    TempData["SuccessMessage"] = message;
                    return RedirectToPage("./Details", new { id = account.Id });
                }
                else
                {
                    ModelState.AddModelError(string.Empty, message);
                    return Page();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating savings account");
                ModelState.AddModelError(string.Empty, _localizer["An error occurred while creating your savings account"]);
                
                var userId = HttpContext.User.GetClaimUserId();
                if (userId.HasValue)
                {
                    await LoadViewModelAsync(userId.Value);
                }
                
                return Page();
            }
        }

        private async Task LoadViewModelAsync(int userId)
        {
            ViewModel.PageTitle = _localizer["Create New Savings Account"];
            ViewModel.PageSubtitle = _localizer["Choose a plan and start earning guaranteed interest on your RZW tokens"];

            // Load available plans
            var plans = await _planService.GetActivePlansAsync();
            ViewModel.AvailablePlans = plans.Select(p => new RzwSavingsPlanOption
            {
                Id = p.Id,
                Name = p.Name,
                TermType = p.TermType,
                TermDuration = p.TermDuration,
                InterestRate = p.InterestRate,
                MinRzwAmount = p.MinRzwAmount,
                MaxRzwAmount = p.MaxRzwAmount,
                Description = p.Description,
                IsActive = p.IsActive,
                IsRecommended = p.TermType == RzwSavingsTermType.Monthly, // Monthly is recommended
                IsPopular = p.TermType == RzwSavingsTermType.Yearly // Yearly is popular
            }).OrderBy(p => p.TermDuration).ToList();

            // Load user balance
            ViewModel.UserBalance = await _balanceService.GetRzwBalanceInfoAsync(userId);

            // Set form to ViewModel
            ViewModel.Form = Form;
        }

        private async Task<CreateSavingsValidationResult> ValidateCreateRequestAsync(int userId, CreateSavingsForm form)
        {
            var result = new CreateSavingsValidationResult { IsValid = true };

            try
            {
                // Plan validation
                var plan = await _planService.GetPlanByIdAsync(form.SelectedPlanId);
                if (plan == null)
                {
                    result.AddError(_localizer["Selected plan is not available"]);
                    result.IsValid = false;
                    return result;
                }

                // Plan amount validation
                if (!await _planService.ValidatePlanAsync(form.SelectedPlanId, form.Amount))
                {
                    result.AddError(_localizer["Amount does not meet plan requirements"]);
                    result.IsValid = false;
                }

                // Balance validation
                if (!await _balanceService.HasSufficientAvailableRzwAsync(userId, form.Amount))
                {
                    result.AddError(_localizer["Insufficient available RZW balance"]);
                    result.IsValid = false;
                }

                // Amount range validation
                if (form.Amount < plan.MinRzwAmount)
                {
                    result.AddError(string.Format(_localizer["Minimum amount for this plan is {0} RZW"], plan.MinRzwAmount.ToString("N0")));
                    result.IsValid = false;
                }

                if (plan.MaxRzwAmount.HasValue && form.Amount > plan.MaxRzwAmount.Value)
                {
                    result.AddError(string.Format(_localizer["Maximum amount for this plan is {0} RZW"], plan.MaxRzwAmount.Value.ToString("N0")));
                    result.IsValid = false;
                }

                // Warnings for large amounts
                var userBalance = await _balanceService.GetRzwBalanceInfoAsync(userId);
                if (form.Amount > userBalance.AvailableBalance * 0.8m)
                {
                    result.AddWarning(_localizer["You are investing more than 80% of your available RZW balance"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating create savings request");
                result.AddError(_localizer["An error occurred during validation"]);
                result.IsValid = false;
            }

            return result;
        }

        // AJAX Endpoints
        public async Task<IActionResult> OnGetCalculateAsync(int planId, decimal amount)
        {
            try
            {
                var userId = HttpContext.User.GetClaimUserId();
                if (!userId.HasValue)
                {
                    return new JsonResult(new { success = false, message = "Unauthorized" });
                }

                var plan = await _planService.GetPlanByIdAsync(planId);
                if (plan == null)
                {
                    return new JsonResult(new { success = false, message = "Plan not found" });
                }

                var calculation = CalculateInterest(amount, plan);
                
                return new JsonResult(new 
                { 
                    success = true, 
                    calculation = new
                    {
                        principalAmount = calculation.FormattedPrincipal,
                        dailyInterest = calculation.FormattedDailyInterest,
                        monthlyInterest = calculation.FormattedMonthlyInterest,
                        totalInterest = calculation.FormattedTotalInterest,
                        maturityAmount = calculation.FormattedMaturityAmount,
                        maturityDate = calculation.FormattedMaturityDate,
                        termDisplay = calculation.TermDisplayText,
                        roi = calculation.FormattedROI,
                        effectiveRate = calculation.FormattedEffectiveRate
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating interest");
                return new JsonResult(new { success = false, message = "Calculation error" });
            }
        }

        public async Task<IActionResult> OnGetValidateAmountAsync(int planId, decimal amount)
        {
            try
            {
                var plan = await _planService.GetPlanByIdAsync(planId);
                if (plan == null)
                {
                    return new JsonResult(new { success = false, message = "Plan not found" });
                }

                var isValid = amount >= plan.MinRzwAmount && 
                             (!plan.MaxRzwAmount.HasValue || amount <= plan.MaxRzwAmount.Value);

                var message = string.Empty;
                if (!isValid)
                {
                    if (amount < plan.MinRzwAmount)
                        message = $"Minimum amount is {plan.MinRzwAmount:N0} RZW";
                    else if (plan.MaxRzwAmount.HasValue && amount > plan.MaxRzwAmount.Value)
                        message = $"Maximum amount is {plan.MaxRzwAmount.Value:N0} RZW";
                }

                return new JsonResult(new { success = true, isValid, message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating amount");
                return new JsonResult(new { success = false, message = "Validation error" });
            }
        }

        private SavingsCalculationResult CalculateInterest(decimal amount, RzwSavingsPlan plan)
        {
            var startDate = DateTime.UtcNow;
            var maturityDate = startDate.AddDays(plan.TermDuration);

            var dailyInterest = plan.TermType switch
            {
                RzwSavingsTermType.Daily => amount * plan.InterestRate,
                RzwSavingsTermType.Monthly => amount * (plan.InterestRate / 30),
                RzwSavingsTermType.Yearly => amount * (plan.InterestRate / 365),
                _ => 0
            };

            var monthlyInterest = dailyInterest * 30;
            var totalInterest = dailyInterest * plan.TermDuration;
            var maturityAmount = amount + totalInterest;

            var effectiveAnnualRate = plan.TermType switch
            {
                RzwSavingsTermType.Daily => plan.InterestRate * 365,
                RzwSavingsTermType.Monthly => plan.InterestRate * 12,
                RzwSavingsTermType.Yearly => plan.InterestRate,
                _ => plan.InterestRate
            };

            return new SavingsCalculationResult
            {
                PrincipalAmount = amount,
                InterestRate = plan.InterestRate,
                TermType = plan.TermType,
                TermDuration = plan.TermDuration,
                StartDate = startDate,
                MaturityDate = maturityDate,
                DailyInterest = dailyInterest,
                MonthlyInterest = monthlyInterest,
                TotalInterest = totalInterest,
                MaturityAmount = maturityAmount,
                EffectiveAnnualRate = effectiveAnnualRate
            };
        }
    }
}
```

## 📋 Kontrol Listesi

### ✅ YAPILACAKLAR
- [ ] RzwSavingsCreateViewModel.cs oluşturma
- [ ] CreateModel PageModel oluşturma
- [ ] Validation logic ekleme
- [ ] AJAX endpoint'leri ekleme
- [ ] Error handling ekleme
- [ ] Unit testler yazma

### 🔄 YAPILMAKTA OLANLAR
- (Henüz başlanmadı)

### ✅ YAPILMIŞLAR
- (Henüz tamamlanmadı)

## 🧪 Test Kriterleri

### ViewModel Tests
- [ ] Form validation rules çalışıyor
- [ ] Calculation logic doğru
- [ ] Plan option mapping doğru
- [ ] Display formatting doğru

### PageModel Tests
- [ ] GET request handling çalışıyor
- [ ] POST request handling çalışıyor
- [ ] AJAX endpoints çalışıyor
- [ ] Validation logic doğru

## 📝 Notlar

### Validation Rules
- Plan selection required
- Amount must be > 0
- Amount must meet plan min/max
- Sufficient balance required
- Terms acceptance required

### AJAX Endpoints
- `/Calculate`: Real-time interest calculation
- `/ValidateAmount`: Amount validation
- Real-time form feedback

### Error Handling
- Model validation errors
- Business logic errors
- Service exceptions
- User-friendly messages

### Sonraki Adım
Bu adım tamamlandıktan sonra **Adım 5.3.2: Plan Selection Interface** başlayacak.

---
**Tahmini Süre**: 2-3 saat
**Öncelik**: Yüksek
**Bağımlılıklar**: Faz 1, 2, 3 tamamlanmış olmalı
