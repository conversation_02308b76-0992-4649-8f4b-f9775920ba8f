﻿@page
@model MarketModel
@using Microsoft.Extensions.Localization
@using RazeWinComTr
@inject IStringLocalizer<SharedResource> Localizer
@{
    ViewData["Title"] = "Market";
}


<div class="fw pageHeaderAll cover wow fadeInUp"
     style="background-image: url(/public/front/fxyatirim/assets/images/page-bg.jpg); visibility: visible; animation-name: fadeInUp;">
    <div class="container">
        <div class="fw pageHeader">
            <ul class="sul">
                <li class="icon"><span class="iconX"><i class="flaticon-star"></i></span></li>
                <li class="title">
                    <h1 class="titleX">Market</h1>
                </li>
                <li class="desc">Kripto Market</li>
            </ul>
        </div>
    </div>
</div>
<div class="container">
    <div class="fw pageAll">
        <div class="fw page contactPage">
            <div class="wow fadeInLeft contactPageLeft" style="padding:40px;">
                <div class="fw pageLeftBox">
                    <div class="fw simpleContentTitle">
                        <h2 class="title">Kripto Para Piyasası</h2>
                    </div>
                    <div class="fw simpleContent">
                        <p>Aşağıdaki tabloda güncel kripto para fiyatlarını görebilirsiniz. Alım ve satım işlemleri için hesabınıza giriş yapmanız gerekmektedir.</p>
                        <div class="mt-4">
                            @await Component.InvokeAsync("CoinList", Model.Markets)
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @if (User?.Identity?.IsAuthenticated == true)
    {
        <script src="/site/components/coinlist/coinlist_auth.js"></script>
    }
    else
    {
        <script src="/site/components/coinlist/coinlist_unauth.js"></script>
    }
}