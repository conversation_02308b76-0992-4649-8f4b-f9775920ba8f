@page
@model RazeWinComTr.Areas.MyAccount.Pages.WithdrawalModel
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.Helpers
@inject IStringLocalizer<SharedResource> Localizer
@{
    ViewData["Title"] = Localizer["My Withdrawals"];
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@Localizer["My Withdrawals"]</h1>
            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/MyAccount/Dashboard">@Localizer["Home"]</a></li>
                    <li class="breadcrumb-item active">@Localizer["My Withdrawals"]</li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->
    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-6">
                <div class="card card-primary">
                    <div class="card-header">
                        <h3 class="card-title">@Localizer["Withdraw Funds"]</h3>
                    </div>
                    <!-- /.card-header -->
                    <!-- form start -->
                    <form method="post">
                        <div class="card-body">
                            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                            <div class="form-group">
                                <label>@Localizer["Current Balance"]</label>
                                <input type="text" class="form-control" value="@NumberFormatHelper.FormatDecimalWithThousandSeparator(Model.CurrentBalance) @Localizer["Currency_Symbol"]" readonly>
                            </div>
                            <div class="form-group">
                                <label>@Localizer["Available Withdrawal Limit"]</label>
                                <input type="text" class="form-control" value="@NumberFormatHelper.FormatDecimalWithThousandSeparator(Model.AvailableWithdrawalLimit) @Localizer["Currency_Symbol"]" readonly>
                                <small class="form-text text-muted">@Localizer["This is your available balance after pending withdrawals"]</small>
                            </div>
                            <div class="form-group">
                                <label>@Localizer["Withdrawal Amount"] (@Localizer["Currency_Symbol"])</label>
                                <div class="input-group money-input-group">
                                    <input asp-for="WithdrawalInput.AmountInteger" class="form-control integer-part" placeholder="0" type="number" min="0" step="1" inputmode="numeric">
                                    <div class="input-group-append input-group-prepend decimal-separator">
                                        <span class="input-group-text">.</span>
                                    </div>
                                    <input asp-for="WithdrawalInput.AmountFraction" class="form-control fraction-part" placeholder="00" type="text" pattern="[0-9]*" inputmode="numeric">
                                    <div class="input-group-append">
                                        <span class="input-group-text">@Localizer["Currency_Symbol"]</span>
                                    </div>
                                </div>
                                <span asp-validation-for="WithdrawalInput.AmountInteger" class="text-danger"></span>
                                <span asp-validation-for="WithdrawalInput.AmountFraction" class="text-danger"></span>
                            </div>
                            <style>
                                /* Para girişi için özel stiller */
                                .money-input-group {
                                    display: flex;
                                    align-items: center;
                                    border-radius: 4px;
                                    overflow: hidden;
                                    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                                    max-width: 280px;
                                }

                                /* Mobil cihazlar için responsive tasarım */
                                @@media (max-width: 576px) {
                                    .money-input-group {
                                        max-width: 100%;
                                    }
                                    .money-input-group .integer-part {
                                        width: 100px;
                                    }
                                    .money-input-group .fraction-part {
                                        width: 65px; /* Mobil için 8 karakter */
                                    }
                                    .money-input-group .decimal-separator .input-group-text {
                                        width: 8px;
                                        min-width: 8px;
                                    }
                                }
                                .money-input-group .input-group-text {
                                    background-color: #f8f9fa;
                                    border-color: #ced4da;
                                    color: #495057;
                                    font-weight: bold;
                                    min-width: 40px;
                                    text-align: center;
                                }
                                .money-input-group .input-group-append:last-child .input-group-text {
                                    border-top-right-radius: 4px;
                                    border-bottom-right-radius: 4px;
                                }
                                .money-input-group .integer-part {
                                    width: 120px;
                                    text-align: right;
                                    border-right: none;
                                    border-top-left-radius: 4px;
                                    border-bottom-left-radius: 4px;
                                    color: #000;
                                }
                                .money-input-group .decimal-separator {
                                    margin: 0;
                                    padding: 0;
                                }
                                .money-input-group .decimal-separator .input-group-text {
                                    border-radius: 0;
                                    border-left: none;
                                    border-right: none;
                                    padding: 0;
                                    width: 10px;
                                    min-width: 10px;
                                    text-align: center;
                                    background-color: white;
                                    font-weight: bold;
                                    color: #000;
                                }
                                .money-input-group .fraction-part {
                                    width: 80px; /* 8 karakter için yeterli genişlik */
                                    border-left: none;
                                    border-radius: 0;
                                    color: #000;
                                }
                                /* Chrome için number input'taki ok butonlarını gizle */
                                .integer-part::-webkit-outer-spin-button,
                                .integer-part::-webkit-inner-spin-button {
                                    -webkit-appearance: none;
                                    margin: 0;
                                }
                                /* Firefox için number input'taki ok butonlarını gizle */
                                .integer-part {
                                    -moz-appearance: textfield;
                                }
                                /* Odaklanma durumu için stil */
                                .money-input-group.focused {
                                    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
                                }
                                .money-input-group.focused .input-group-text,
                                .money-input-group.focused input {
                                    border-color: #80bdff;
                                }
                            </style>
                            <div class="form-group">
                                <label asp-for="WithdrawalInput.AccountHolder">@Localizer["Account Holder"]</label>
                                <input asp-for="WithdrawalInput.AccountHolder" class="form-control" placeholder="@Localizer["Enter account holder name"]">
                                <span asp-validation-for="WithdrawalInput.AccountHolder" class="text-danger"></span>
                            </div>
                            <div class="form-group">
                                <label asp-for="WithdrawalInput.Iban">@Localizer["IBAN"]</label>
                                <input asp-for="WithdrawalInput.Iban" class="form-control" placeholder="TR00 0000 0000 0000 0000 0000 00" autocomplete="off">
                                <span asp-validation-for="WithdrawalInput.Iban" class="text-danger"></span>
                                <small class="form-text text-muted">@Localizer["IBAN will be automatically formatted"]</small>
                            </div>
                        </div>
                        <!-- /.card-body -->
                        <div class="card-footer">
                            <button type="submit" class="btn btn-primary">@Localizer["Submit"]</button>
                        </div>
                    </form>
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
            <div class="col-md-6">
                <div class="card card-info">
                    <div class="card-header">
                        <h3 class="card-title">@Localizer["Withdrawal Instructions"]</h3>
                    </div>
                    <div class="card-body">
                        <p>@Localizer["To withdraw funds from your account, please follow these steps:"]</p>
                        <ol>
                            <li>@Localizer["Enter the amount you wish to withdraw."]</li>
                            <li>@Localizer["Provide your bank account details."]</li>
                            <li>@Localizer["Submit the withdrawal request."]</li>
                            <li>@Localizer["Your request will be processed within 1-3 business days."]</li>
                        </ol>
                        <div class="alert alert-warning">
                            <h5><i class="icon fas fa-exclamation-triangle"></i> @Localizer["Important!"]</h5>
                            @Localizer["Please ensure that the bank account details are correct. We are not responsible for transfers to incorrect accounts."]
                        </div>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">@Localizer["Withdrawal History"]</h3>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <style>
                            /* Özel tablo stilleri */
                            #example1 {
                                color: #333 !important;
                                background-color: #fff !important;
                            }

                            #example1 thead th {
                                color: #fff !important;
                                background-color: #343a40 !important;
                                border-color: #454d55 !important;
                            }

                            #example1 tbody td {
                                color: #333 !important;
                                background-color: #fff !important;
                            }

                            #example1 tfoot th {
                                color: #fff !important;
                                background-color: #343a40 !important;
                                border-color: #454d55 !important;
                            }

                            .dataTables_wrapper .dataTables_length,
                            .dataTables_wrapper .dataTables_filter,
                            .dataTables_wrapper .dataTables_info,
                            .dataTables_wrapper .dataTables_processing,
                            .dataTables_wrapper .dataTables_paginate {
                                color: #333 !important;
                            }

                            .dataTables_wrapper .dataTables_paginate .paginate_button {
                                color: #333 !important;
                            }

                            .dataTables_wrapper .dataTables_paginate .paginate_button.current,
                            .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
                                color: #333 !important;
                                background-color: #f0f0f0 !important;
                            }
                        </style>
                        <table id="example1" class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>@Localizer["Date"]</th>
                                    <th>@Localizer["Amount"]</th>
                                    <th>@Localizer["Account Holder"]</th>
                                    <th>@Localizer["IBAN"]</th>
                                    <th>@Localizer["Status"]</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var withdrawal in Model.Withdrawals)
                                {
                                    <tr>
                                        <td>@withdrawal.CreatedDate.ToLocalTime().ToString("dd.MM.yyyy HH:mm")</td>
                                        <td>@NumberFormatHelper.FormatDecimalWithThousandSeparator(withdrawal.WithdrawalAmount) @Localizer["Currency_Symbol"]</td>
                                        <td>@withdrawal.AccountHolder</td>
                                        <td>@withdrawal.Iban</td>
                                        <td>
                                            @if (withdrawal.Status == WithdrawalStatus.Pending)
                                            {
                                                <span class="badge badge-warning">@Localizer["Pending"]</span>
                                            }
                                            else if (withdrawal.Status == WithdrawalStatus.Approved)
                                            {
                                                <span class="badge badge-success">@Localizer["Approved"]</span>
                                            }
                                            else
                                            {
                                                <span class="badge badge-danger">@Localizer["Rejected"]</span>
                                            }
                                        </td>
                                    </tr>
                                }
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th>@Localizer["Date"]</th>
                                    <th>@Localizer["Amount"]</th>
                                    <th>@Localizer["Account Holder"]</th>
                                    <th>@Localizer["IBAN"]</th>
                                    <th>@Localizer["Status"]</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        $(function () {
            // Initialize DataTable
            $("#example1").DataTable({
                "responsive": true,
                "lengthChange": false,
                "autoWidth": false,
                "buttons": ["copy", "csv", "excel", "pdf", "print", "colvis"],
                "order": [[0, "desc"]],
                "columnDefs": [
                    {
                        "targets": 0,
                        "type": "date-eu"
                    }
                ],
                "initComplete": function() {
                    // Force initial sort
                    this.api().order([0, 'desc']).draw();
                },
                "language": {
                    "search": "@Localizer["Search"]:",
                    "paginate": {
                        "first": "@Localizer["First"]",
                        "last": "@Localizer["Last"]",
                        "next": "@Localizer["Next"]",
                        "previous": "@Localizer["Previous"]"
                    },
                    "info": "@Localizer["Showing _START_ to _END_ of _TOTAL_ entries"]",
                    "infoEmpty": "@Localizer["No records available"]",
                    "infoFiltered": "@Localizer["(filtered from _MAX_ total records)"]",
                    "zeroRecords": "@Localizer["No matching records found"]"
                }
            }).buttons().container().appendTo('#example1_wrapper .col-md-6:eq(0)');

            // Show success message with SweetAlert if exists
            @if (!string.IsNullOrEmpty(Model.SuccessMessage))
            {
                <text>
                Swal.fire({
                    title: window.t["Success"] || 'Success',
                    text: '@Model.SuccessMessage',
                    icon: 'success',
                    confirmButtonText: window.t["OK"] || 'OK',
                    confirmButtonColor: '#28a745',
                    allowOutsideClick: false,
                    customClass: {
                        confirmButton: 'btn btn-success btn-lg',
                        popup: 'animated fadeInDown'
                    },
                    padding: '2em',
                    timer: 3000,
                    timerProgressBar: true
                }).then(function() {
                    // Redirect to withdrawal history or refresh the page
                    window.location.href = '/MyAccount/Withdrawal';
                });
                </text>
            }

            // Show error message if exists
            @if (!string.IsNullOrEmpty(Model.ErrorMessage))
            {
                <text>
                Swal.fire({
                    title: window.t["Error"] || 'Error',
                    text: '@Model.ErrorMessage',
                    icon: 'error',
                    confirmButtonText: window.t["OK"] || 'OK',
                    confirmButtonColor: '#dc3545'
                });
                </text>
            }

            // Custom client-side validation
            $("form").validate({
                rules: {
                    "WithdrawalInput.AmountInteger": {
                        required: true,
                        min: 0,
                        combinedAmountGreaterThanZero: true
                    },
                    "WithdrawalInput.AmountFraction": {
                        pattern: /^[0-9]*$/
                    },
                    "WithdrawalInput.AccountHolder": {
                        required: true,
                        maxlength: 100
                    },
                    "WithdrawalInput.Iban": {
                        required: true,
                        maxlength: 50,
                        pattern: /^[A-Z]{2}[0-9]{2}[A-Z0-9]{1,30}$/
                    }
                },
                messages: {
                    "WithdrawalInput.AmountInteger": {
                        required: window.t["Please Enter Valid Amount"],
                        min: window.t["Amount Cannot Be Negative"],
                        combinedAmountGreaterThanZero: window.t["Total Amount Must Be Greater Than Zero"]
                    },
                    "WithdrawalInput.AmountFraction": {
                        pattern: window.t["Only Digits For Decimal Part"]
                    },
                    "WithdrawalInput.AccountHolder": {
                        required: window.t["Enter Account Holder Name"],
                        maxlength: window.t["Account Holder Name Too Long"]
                    },
                    "WithdrawalInput.Iban": {
                        required: window.t["Enter IBAN"],
                        maxlength: window.t["IBAN Too Long"],
                        pattern: window.t["Enter Valid IBAN"]
                    }
                },
                errorElement: 'span',
                errorPlacement: function (error, element) {
                    error.addClass('text-danger');
                    element.closest('.form-group').append(error);
                },
                highlight: function (element, errorClass, validClass) {
                    $(element).addClass('is-invalid');
                },
                unhighlight: function (element, errorClass, validClass) {
                    $(element).removeClass('is-invalid');
                }
            });

            // Add pattern validation method if not already defined
            if ($.validator.methods.pattern === undefined) {
                $.validator.addMethod("pattern", function(value, element, param) {
                    if (this.optional(element)) {
                        return true;
                    }
                    if (typeof param === "string") {
                        param = new RegExp("^(?:" + param + ")$");
                    }
                    return param.test(value);
                }, window.t["Enter Valid Format"]);
            }

            // Add custom validation method for combined amount
            $.validator.addMethod("combinedAmountGreaterThanZero", function(value, element, param) {
                var integerPart = parseInt($("#WithdrawalInput_AmountInteger").val()) || 0;
                var fractionPart = $("#WithdrawalInput_AmountFraction").val() || '0';

                // If both parts are 0, validation fails
                if (integerPart === 0 && (fractionPart === '0' || fractionPart === '' || parseInt(fractionPart) === 0)) {
                    return false;
                }
                return true;
            }, window.t["Total Amount Must Be Greater Than Zero"]);

            // Handle decimal part input
            $("#WithdrawalInput_AmountFraction").on('input', function() {
                // Remove any non-digit characters
                var value = $(this).val().replace(/[^0-9]/g, '');
                $(this).val(value);

                // Limit to 8 digits for decimal part
                if (value.length > 8) {
                    $(this).val(value.substring(0, 8));
                }
            });

            // Format IBAN with spaces for better readability
            $("#WithdrawalInput_Iban").on('input', function() {
                // Remove spaces and convert to uppercase
                var iban = $(this).val().replace(/\s/g, '').toUpperCase();
                var formattedIban = '';

                // Format with spaces every 4 characters
                for (var i = 0; i < iban.length; i++) {
                    if (i > 0 && i % 4 === 0) {
                        formattedIban += ' ';
                    }
                    formattedIban += iban[i];
                }

                $(this).val(formattedIban);
            });

            // Format IBAN on page load
            $(document).ready(function() {
                // Trigger the input event to format the IBAN if it's pre-filled
                if ($("#WithdrawalInput_Iban").val()) {
                    $("#WithdrawalInput_Iban").trigger('input');
                }
            });

            // Format integer part with thousand separators on blur
            $("#WithdrawalInput_AmountInteger").on('blur', function() {
                var value = $(this).val();
                if (value && !isNaN(value)) {
                    // Store the cursor position
                    var cursorPos = this.selectionStart;

                    // Format the value
                    var formattedValue = parseInt(value).toLocaleString('en-US');

                    // Update the display value (but not the actual value)
                    $(this).attr('data-formatted-value', formattedValue);
                }
            });

            // Focus handling for better UX
            $("#WithdrawalInput_AmountInteger, #WithdrawalInput_AmountFraction").on('focus', function() {
                $(this).closest('.money-input-group').addClass('focused');
            }).on('blur', function() {
                $(this).closest('.money-input-group').removeClass('focused');
            });

            // Form submission handling - using the validator instead of custom code
            $("form").on('submit', function() {
                // The validation will be handled by jQuery Validate plugin
                // with our custom combinedAmountGreaterThanZero method
                return $("form").valid();
            });
        });
    </script>
}
