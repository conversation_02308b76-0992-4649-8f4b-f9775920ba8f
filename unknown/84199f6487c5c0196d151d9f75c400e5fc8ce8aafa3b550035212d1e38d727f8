$(function () {
    // Initialize select2 for user dropdown
    $('.select2').select2({
        placeholder: window.t["Select User"] || "Select User",
        allowClear: true
    });

    // Auto-submit form when user is selected from dropdown
    $('#SelectedUserId').on('change', function() {
        if ($(this).val()) {
            $(this).closest('form').submit();
        }
    });
});

/**
 * Copy text to clipboard
 * @param {string} elementId - ID of the element containing text to copy
 */
function copyToClipboard(elementId) {
    var element = document.getElementById(elementId);
    if (!element) {
        console.error(window.t["Element not found"] || 'Element not found:', elementId);
        return;
    }

    var text = element.textContent || element.innerText;
    
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        var tooltip = document.createElement('div');
        tooltip.className = 'copy-tooltip';
        tooltip.textContent = window.t["Copied"] || 'Copied';
        
        // Position the tooltip near the element
        var rect = element.getBoundingClientRect();
        tooltip.style.top = (rect.top - 30) + 'px';
        tooltip.style.left = (rect.left + rect.width / 2) + 'px';
        
        document.body.appendChild(tooltip);
        
        // Remove the tooltip after animation completes
        setTimeout(function() {
            document.body.removeChild(tooltip);
        }, 1500);
    }).catch(function(err) {
        console.error(window.t["Failed to copy text"] || 'Failed to copy text:', err);
    });
}
