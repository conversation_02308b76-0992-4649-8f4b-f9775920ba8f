﻿using RazeWinComTr.Areas.Admin.ViewModels.Common;
using System.Text.Json.Serialization;

namespace RazeWinComTr.Areas.Admin.ViewModels.Bitexen;


public class BitexenTickerApiResponse : ITickerResponse
{
    [JsonPropertyName("status")]
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// Implements ITickerResponse.IsSuccess
    /// </summary>
    public bool IsSuccess => Status == "success";

    [JsonPropertyName("data")]
    public Data Data { get; set; } = new Data();

    /// <summary>
    /// Gets the error message if the request was not successful
    /// </summary>
    public string? GetErrorMessage() => IsSuccess ? null : "Failed to retrieve data from Bitexen API";

    /// <summary>
    /// Gets the timestamp of when the data was retrieved
    /// </summary>
    public DateTime GetTimestamp()
    {
        // Try to get timestamp from the first ticker item
        if (Data.Ticker.Count > 0)
        {
            var firstTicker = Data.Ticker.Values.FirstOrDefault();
            if (firstTicker != null && !string.IsNullOrEmpty(firstTicker.Timestamp))
            {
                if (long.TryParse(firstTicker.Timestamp, out long unixTimestamp))
                {
                    // Convert Unix timestamp to DateTime
                    return DateTimeOffset.FromUnixTimeSeconds(unixTimestamp).DateTime;
                }
            }
        }
        return DateTime.UtcNow; // Fallback to current time if no timestamp is available
    }

    /// <summary>
    /// Gets the buy price (bid) for a specific market pair
    /// </summary>
    public decimal? GetBuyPrice(string marketPair)
    {
        if (Data.Ticker.TryGetValue(marketPair, out var ticker))
        {
            return ticker.Bid;
        }
        return null;
    }

    /// <summary>
    /// Gets the sell price (ask) for a specific market pair
    /// </summary>
    public decimal? GetSellPrice(string marketPair)
    {
        if (Data.Ticker.TryGetValue(marketPair, out var ticker))
        {
            return ticker.Ask;
        }
        return null;
    }

    /// <summary>
    /// Gets the 24-hour price change percentage for a specific market pair
    /// </summary>
    public decimal? GetPriceChangePercentage(string marketPair)
    {
        if (Data.Ticker.TryGetValue(marketPair, out var ticker))
        {
            return ticker.Change24h;
        }
        return null;
    }

    /// <summary>
    /// Gets a standardized ticker view model for a specific market pair
    /// </summary>
    public TickerViewModel? GetTickerViewModel(string marketPair)
    {
        if (!Data.Ticker.TryGetValue(marketPair, out var ticker))
        {
            return null;
        }

        long timestampMs = 0;
        if (!string.IsNullOrEmpty(ticker.Timestamp) && long.TryParse(ticker.Timestamp, out long unixTimestamp))
        {
            // Convert Unix timestamp in seconds to milliseconds
            timestampMs = unixTimestamp * 1000;
        }

        decimal lastPrice = 0;
        if (!string.IsNullOrEmpty(ticker.LastPrice) && decimal.TryParse(ticker.LastPrice, out decimal parsedLastPrice))
        {
            lastPrice = parsedLastPrice;
        }

        decimal high24h = 0;
        if (!string.IsNullOrEmpty(ticker.High24h) && decimal.TryParse(ticker.High24h, out decimal parsedHigh24h))
        {
            high24h = parsedHigh24h;
        }

        decimal low24h = 0;
        if (!string.IsNullOrEmpty(ticker.Low24h) && decimal.TryParse(ticker.Low24h, out decimal parsedLow24h))
        {
            low24h = parsedLow24h;
        }

        decimal volume24h = 0;
        if (!string.IsNullOrEmpty(ticker.Volume24h) && decimal.TryParse(ticker.Volume24h, out decimal parsedVolume24h))
        {
            volume24h = parsedVolume24h;
        }

        decimal avg24h = 0;
        if (!string.IsNullOrEmpty(ticker.Avg24h) && decimal.TryParse(ticker.Avg24h, out decimal parsedAvg24h))
        {
            avg24h = parsedAvg24h;
        }

        return new TickerViewModel
        {
            PairIdentifier = marketPair,
            TimestampMs = timestampMs,
            LastPrice = lastPrice,
            High24h = high24h,
            Low24h = low24h,
            BidPrice = ticker.Bid,
            AskPrice = ticker.Ask,
            Volume24h = volume24h,
            AveragePrice24h = avg24h,
            DailyChangePercent = ticker.Change24h
        };
    }

    /// <summary>
    /// Gets all available tickers as standardized view models
    /// </summary>
    public Dictionary<string, TickerViewModel> GetAllTickers()
    {
        var result = new Dictionary<string, TickerViewModel>();

        foreach (var pair in Data.Ticker)
        {
            var ticker = GetTickerViewModel(pair.Key);
            if (ticker != null)
            {
                result[pair.Key] = ticker;
            }
        }

        return result;
    }
}

public class Data
{
    [JsonPropertyName("ticker")]
    public Dictionary<string, TickerDetail> Ticker { get; set; } = new Dictionary<string, TickerDetail>();
}

public class TickerDetail
{
    // İç içe market nesnesi bazı kayıtlarda olmayabilir (örn: RIZETRY),
    // bu yüzden nullable (?) yaptık.
    [JsonPropertyName("market")]
    public MarketInfo? Market { get; set; }

    [JsonPropertyName("bid")]
    public decimal Bid { get; set; }

    [JsonPropertyName("ask")]
    public decimal Ask { get; set; }

    [JsonPropertyName("last_price")]
    public string LastPrice { get; set; } = string.Empty;

    [JsonPropertyName("last_size")]
    public string LastSize { get; set; } = string.Empty;

    [JsonPropertyName("volume_24h")]
    public string Volume24h { get; set; } = string.Empty;

    [JsonPropertyName("change_24h")]
    public decimal Change24h { get; set; }

    [JsonPropertyName("low_24h")]
    public string Low24h { get; set; } = string.Empty;

    [JsonPropertyName("high_24h")]
    public string High24h { get; set; } = string.Empty;

    [JsonPropertyName("avg_24h")]
    public string Avg24h { get; set; } = string.Empty;

    [JsonPropertyName("timestamp")]
    public string Timestamp { get; set; } = string.Empty;

    // Bazı kayıtlarda (örn: RIZETRY) market_code doğrudan TickerDetail içinde de yer alıyor.
    // Bu durumu kapsamak ve Market nesnesindeki ile çakışmayı önlemek için
    // farklı bir isimle (MarketCodeDirect) ekledik.
    // Market nesnesi null ise buradan okunabilir.
    [JsonPropertyName("market_code")]
    public string? MarketCodeDirect { get; set; }
}

public class MarketInfo
{
    [JsonPropertyName("market_code")]
    public string MarketCode { get; set; } = string.Empty;

    [JsonPropertyName("base_currency_code")]
    public string BaseCurrencyCode { get; set; } = string.Empty;

    [JsonPropertyName("counter_currency_code")]
    public string CounterCurrencyCode { get; set; } = string.Empty;
}
