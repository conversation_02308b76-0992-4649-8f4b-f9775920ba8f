using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;

namespace RazeWinComTr.Pages
{
    public class EarnMoneyModel : PageModel
    {
        private readonly RzwSavingsPlanService _rzwSavingsPlanService;
        private readonly IStringLocalizer<SharedResource> _localizer;

        public EarnMoneyModel(RzwSavingsPlanService rzwSavingsPlanService, IStringLocalizer<SharedResource> localizer)
        {
            _rzwSavingsPlanService = rzwSavingsPlanService;
            _localizer = localizer;
        }

        public List<RzwSavingsPlanDisplayModel> RzwSavingsPlans { get; set; } = new();

        public async Task OnGetAsync()
        {
            try
            {
                // Get active RZW savings plans from database
                var plans = await _rzwSavingsPlanService.GetActivePlansAsync();

                // Convert to display models with calculated values
                RzwSavingsPlans = plans.Select(plan => new RzwSavingsPlanDisplayModel
                {
                    Id = plan.Id,
                    Name = plan.Name,
                    TermType = plan.TermType,
                    TermDuration = plan.TermDuration,
                    InterestRate = plan.InterestRate,
                    MinRzwAmount = plan.MinRzwAmount,
                    MaxRzwAmount = plan.MaxRzwAmount,
                    Description = plan.Description,
                    IsActive = plan.IsActive,
                    DisplayOrder = plan.DisplayOrder,
                    // Calculated display values
                    DisplayName = GetPlanDisplayName(plan),
                    DisplayRate = GetPlanDisplayRate(plan),
                    ApyPercent = RzwSavingsCalculationHelper.CalculateAPY(plan)
                }).ToList();
            }
            catch (Exception)
            {
                // If there's an error, use empty list to prevent page crash
                RzwSavingsPlans = new List<RzwSavingsPlanDisplayModel>();
            }
        }

        private string GetPlanDisplayName(RzwSavingsPlan plan)
        {
            return plan.TermType switch
            {
                "Daily" => $"{plan.TermDuration} {_localizer["Days"]}",
                "Monthly" => $"{plan.TermDuration} {_localizer["Months"]}",
                "Yearly" => $"{plan.TermDuration} {_localizer["Years"]}",
                _ => plan.Name
            };
        }

        private string GetPlanDisplayRate(RzwSavingsPlan plan)
        {
            var apyPercent = RzwSavingsCalculationHelper.GetAPYAsFlooredPercentage(plan);
            return $"{apyPercent:F2}% {_localizer["APY"]}";
        }
    }

    public class RzwSavingsPlanDisplayModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string TermType { get; set; } = string.Empty;
        public int TermDuration { get; set; }
        public decimal InterestRate { get; set; }
        public decimal MinRzwAmount { get; set; }
        public decimal? MaxRzwAmount { get; set; }
        public string? Description { get; set; }
        public bool IsActive { get; set; }
        public int DisplayOrder { get; set; }

        // Calculated display properties
        public string DisplayName { get; set; } = string.Empty;
        public string DisplayRate { get; set; } = string.Empty;
        public decimal ApyPercent { get; set; }
    }
}
