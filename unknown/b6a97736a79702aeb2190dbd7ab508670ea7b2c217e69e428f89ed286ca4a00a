﻿@using System.Globalization
@using System.Security.Claims
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.DbModel
@using RazeWinComTr.Areas.Admin.Helpers
@inject AppDbContext _dbContext
@inject IStringLocalizer<SharedResource> Localizer
@inject RazeWinComTr.Areas.Admin.Services.SettingService _settingService
@{
    var userCompanyName = "";
    var userEmail = "";
    var currentCultureName = CultureInfo.CurrentCulture.Name;
    var currentUICultureName = CultureInfo.CurrentUICulture.Name;
    List<SelectListItem> languages = [];
    @foreach (var ci in StaticConfig.supportedCultures)
    {
        // Use only the language name without country
        string languageName = ci.NativeName.Split(' ')[0];
        languages.Add(new SelectListItem { Value = ci.Name, Text = languageName, Selected = currentCultureName == ci.Name });
    }
    // List<SelectListItem> languages = new List<SelectListItem>(){
    //  new SelectListItem { Value = "en-US", Text = "English", Selected = currentCultureName == "en-US" },
    //  new SelectListItem { Value = "tr-TR", Text = "Türkçe", Selected = currentCultureName == "tr-TR" }
    // };
}


<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Management | Dashboard</title>

    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet"
          href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="/plugins/fontawesome-free/css/all.min.css">
    <!-- Ionicons -->
    <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">
    <!-- Tempusdominus Bootstrap 4 -->
    <link rel="stylesheet" href="/plugins/tempusdominus-bootstrap-4/css/tempusdominus-bootstrap-4.min.css">
    <!-- iCheck -->
    <link rel="stylesheet" href="/plugins/icheck-bootstrap/icheck-bootstrap.min.css">
    <!-- JQVMap -->
    <link rel="stylesheet" href="/plugins/jqvmap/jqvmap.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="/dist/css/adminlte.min.css">
    <!-- overlayScrollbars -->
    <link rel="stylesheet" href="/plugins/overlayScrollbars/css/OverlayScrollbars.min.css">
    <!-- Daterange picker -->
    <link rel="stylesheet" href="/plugins/daterangepicker/daterangepicker.css">
    <!-- summernote -->
    <link rel="stylesheet" href="/plugins/summernote/summernote-bs4.min.css">
    <!-- sweet alert 2-->
    <link href="~/plugins/sweetalert2/sweetalert2.min.css" rel="stylesheet" />
    <!-- Bootstrap Switch-->
    <link href="~/plugins/bootstrap-switch/css/bootstrap3/bootstrap-switch.min.css" rel="stylesheet" />
    <!-- Required Field Indicator -->
    <link href="/css/required-field-indicator.css" rel="stylesheet" />

    <!-- Page specific styles -->
    @await RenderSectionAsync("Styles", false)

</head>
<body class="hold-transition sidebar-mini layout-fixed text-sm">
    <div class="wrapper">

        <!-- Preloader -->
        <div class="preloader flex-column justify-content-center align-items-center">
            <img class="animation__shake" src="/dist/img/AdminLTELogo.png" alt="AdminLTELogo" height="60" width="60">
        </div>

        <!-- Navbar -->
        <nav class="main-header navbar navbar-expand navbar-white navbar-light">
            <!-- Left navbar links -->
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
                </li>
                <li class="nav-item d-none d-sm-inline-block">
                    <a href="/admin/" class="nav-link">@Localizer["Home"]</a>
                </li>
                <li class="nav-item d-none d-sm-inline-block">
                    <a href="#" class="nav-link">@Localizer["Contact"]</a>
                </li>
            </ul>
            <span>   <partial name="_LoginPartial" /></span>
            <!-- Right navbar links -->
            <ul class="navbar-nav ml-auto">
                <!-- Navbar Search -->
                <li class="nav-item">
                    <a class="nav-link" data-widget="navbar-search" href="#" role="button">
                        <i class="fas fa-search"></i>
                    </a>
                    <div class="navbar-search-block">
                        <form class="form-inline">
                            <div class="input-group input-group-sm">
                                <input class="form-control form-control-navbar" type="search" placeholder="Search"
                                       aria-label="Search">
                                <div class="input-group-append">
                                    <button class="btn btn-navbar" type="submit">
                                        <i class="fas fa-search"></i>
                                    </button>
                                    <button class="btn btn-navbar" type="button" data-widget="navbar-search">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </li>

                <li class="nav-item">
                    <a class="nav-link" data-widget="fullscreen" href="#" role="button">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-widget="control-sidebar" data-slide="true" href="#" role="button">
                        <i class="fas fa-th-large"></i>
                    </a>
                </li>
            </ul>
        </nav>
        <!-- /.navbar -->
        <!-- Main Sidebar Container -->
        <aside class="main-sidebar sidebar-dark-primary elevation-4">
            <!-- Brand Logo -->
            <a href="/admin/" class="brand-link">
                <img src="/dist/img/AdminLTELogo.png" alt="AdminLTE Logo" class="brand-image img-circle elevation-3"
                     style="opacity: .8">
                <span class="brand-text font-weight-light">@Localizer["Management Panel"] 3.2</span>

            </a>

            @{
                var userName = User.FindFirstValue(ClaimTypes.Name);
                var userId = User.GetClaimUserId();
            }
            <!-- Sidebar -->
            <div class="sidebar">
                <!-- Sidebar user panel (optional) -->
                <div class="user-panel mt-2 pb-2 mb-2 d-flex">

                    <div class="info">
                        <select class="form-control" name="languageSelect" title="Language Selection"
                                onchange="changeLanguage(this.value)" asp-items="@languages">
                        </select>
                    </div>
                </div>
                <div class="user-panel mt-1 pb-1 mb-1 d-flex">
                    <div class="image">
                        <img src="/dist/img/user2-160x160.jpg" class="img-circle elevation-2" alt="User Image">
                    </div>
                    <div class="info">
                        <a href="#" class="d-block">@userName</a>
                    </div>
                </div>
                @*  <div class="user-panel mt-3 pb-3 mb-3 d-flex">
                <div class="info">
                @Localizer["Current Culture"]: @currentCultureName
                </div>
                </div>
                <div class="user-panel mt-3 pb-3 mb-3 d-flex">
                <div class="info">
                @Localizer["Current UI Culture"]: @currentUICultureName
                </div>
                </div> *@


                @if (userId.HasValue)
                {
                    userCompanyName = await _settingService.GetValueAsync("site_title") ?? "RAZEWİN HOLDİNG";

                    <div class="user-panel mt-1 pb-1 mb-1 d-flex">
                        <div class="info">
                            <span class="badge badge-info right">@Localizer["Company"]</span>
                            <a href="#" class="d-block">@userCompanyName</a>
                        </div>
                    </div>
                    userEmail = User.GetClaimValue(ClaimTypes.Email);
                    if (userEmail != null)
                    {
                        <div class="user-panel mt-1 pb-3 mb-3 d-flex">
                            <div class="info">
                                <span class="badge badge-info right">@Localizer["Email"]</span>
                                <a href="#" class="d-block">@userEmail</a>
                            </div>
                        </div>
                    }
                }

                <!-- SidebarSearch Form -->
                <div class="form-inline">
                    <div class="input-group" data-widget="sidebar-search">
                        <input class="form-control form-control-sidebar" type="search" placeholder="Search"
                               aria-label="Search">
                        <div class="input-group-append">
                            <button class="btn btn-sidebar">
                                <i class="fas fa-search fa-fw"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Sidebar Menu -->
                <nav class="mt-2">
                    <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu"
                        data-accordion="false">
                        <!-- Add icons to the links using the .nav-icon class
                        with font-awesome or any other icon font library -->

                        <li class="nav-header">@Localizer["Entities"]</li>
                        <li class="nav-item">
                            <a href="/Admin/Bank" class="nav-link">
                                <i class="nav-icon fas fa-university"></i>
                                <p>
                                    @Localizer["Banks"]
                                </p>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="/Admin/Market" class="nav-link">
                                <i class="nav-icon fas fa-coins"></i>
                                <p>
                                    @Localizer["Markets"]
                                </p>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="/Admin/Wallet" class="nav-link">
                                <i class="nav-icon fas fa-wallet"></i>
                                <p>
                                    @Localizer["Wallets"]
                                </p>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="/Admin/Trade" class="nav-link">
                                <i class="nav-icon fas fa-exchange-alt"></i>
                                <p>
                                    @Localizer["Trades"]
                                </p>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="/Admin/Deposit" class="nav-link">
                                <i class="nav-icon fas fa-money-bill-wave"></i>
                                <p>
                                    @Localizer["Deposits"]
                                </p>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="/Admin/Withdrawal" class="nav-link">
                                <i class="nav-icon fas fa-hand-holding-usd"></i>
                                <p>
                                    @Localizer["Withdrawals"]
                                </p>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="/Admin/BalanceHistory" class="nav-link">
                                <i class="nav-icon fas fa-history"></i>
                                <p>
                                    @Localizer["Balance History"]
                                </p>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="/Admin/TransactionReversal" class="nav-link">
                                <i class="nav-icon fas fa-undo"></i>
                                <p>
                                    @Localizer["Transaction Reversal"]
                                </p>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="/Admin/Setting" class="nav-link">
                                <i class="nav-icon fas fa-cogs"></i>
                                <p>
                                    @Localizer["Settings"]
                                </p>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="/Admin/User" class="nav-link">
                                <i class="nav-icon fas fa-users"></i>
                                <p>
                                    @Localizer["Users"]
                                </p>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="nav-icon fas fa-gift"></i>
                                <p>
                                    @Localizer["Referral System"]
                                    <i class="fas fa-angle-left right"></i>
                                </p>
                            </a>
                            <ul class="nav nav-treeview">
                                <li class="nav-item">
                                    <a href="/Admin/Package" class="nav-link">
                                        <i class="nav-icon fas fa-box"></i>
                                        <p>@Localizer["Packages"]</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="/Admin/PackageRewardPercentage" class="nav-link">
                                        <i class="nav-icon fas fa-percentage"></i>
                                        <p>@Localizer["Reward Percentages"]</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="/Admin/UserPackage" class="nav-link">
                                        <i class="nav-icon fas fa-user-tag"></i>
                                        <p>@Localizer["User Packages"]</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="/Admin/ReferralTree" class="nav-link">
                                        <i class="nav-icon fas fa-sitemap"></i>
                                        <p>@Localizer["Referral Tree"]</p>
                                    </a>
                                </li>
                            </ul>
                        </li>

                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="nav-icon fas fa-gem"></i>
                                <p>
                                    @Localizer["RZW Savings"]
                                    <i class="fas fa-angle-left right"></i>
                                </p>
                            </a>
                            <ul class="nav nav-treeview">
                                <li class="nav-item">
                                    <a href="/Admin/RzwSavingsPlans" class="nav-link">
                                        <i class="nav-icon fas fa-clipboard-list"></i>
                                        <p>@Localizer["Savings Plans"]</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="/Admin/RzwSavingsAccounts" class="nav-link">
                                        <i class="nav-icon fas fa-piggy-bank"></i>
                                        <p>@Localizer["Savings Accounts"]</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="/Admin/RzwSavingsReports" class="nav-link">
                                        <i class="nav-icon fas fa-chart-bar"></i>
                                        <p>@Localizer["Reports"]</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="/Admin/RzwSavingsMonitoring" class="nav-link">
                                        <i class="nav-icon fas fa-heartbeat"></i>
                                        <p>@Localizer["Monitoring"]</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="/Admin/RzwSavings/DebugTest" class="nav-link">
                                        <i class="nav-icon fas fa-bug text-warning"></i>
                                        <p>@Localizer["Debug Test"] <span class="badge badge-warning">DEBUG</span></p>
                                    </a>
                                </li>
                            </ul>
                        </li>


                        <li class="nav-item">
                            <a href="/Admin/Account/Logout" class="nav-link">
                                <i class="nav-icon fas fa-sign-out-alt"></i>
                                <p>
                                    @Localizer["Exit"]
                                </p>
                            </a>
                        </li>
                    </ul>
                </nav>
                <!-- /.sidebar-menu -->
            </div>
            <!-- /.sidebar -->
        </aside>

        <!-- Content Wrapper. Contains page content -->
        <div class="content-wrapper">
            @RenderBody()
        </div>

        <!-- /.content-wrapper -->
        <footer class="main-footer">
            <strong>Copyright &copy; 2025 @*<a href="https://adminlte.io">AdminLTE.io</a>*@.</strong>
            All rights reserved.
            <div class="float-right d-none d-sm-inline-block">
                <b>Version</b> 3.2.0
            </div>
        </footer>

        <!-- Control Sidebar -->
        <aside class="control-sidebar control-sidebar-dark">
            <!-- Control sidebar content goes here -->
        </aside>
        <!-- /.control-sidebar -->
    </div>
    <!-- ./wrapper -->
    <!-- jQuery -->
    <script src="/plugins/jquery/jquery.min.js"></script>
    <!-- jQuery UI 1.11.4 -->
    <script src="/plugins/jquery-ui/jquery-ui.min.js"></script>
    <!-- Resolve conflict in jQuery UI tooltip with Bootstrap tooltip -->
    <script>$.widget.bridge('uibutton', $.ui.button)</script>
    <!-- Bootstrap 4 -->
    <script src="/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- ChartJS -->
    <script src="/plugins/chart.js/Chart.min.js"></script>
    <!-- Sparkline -->
    <script src="/plugins/sparklines/sparkline.js"></script>
    <!-- JQVMap -->
    <script src="/plugins/jqvmap/jquery.vmap.min.js"></script>
    <script src="/plugins/jqvmap/maps/jquery.vmap.usa.js"></script>
    <!-- jQuery Knob Chart -->
    <script src="/plugins/jquery-knob/jquery.knob.min.js"></script>
    <!-- daterangepicker -->
    <script src="/plugins/moment/moment.min.js"></script>
    <script src="/plugins/daterangepicker/daterangepicker.js"></script>
    <!-- Tempusdominus Bootstrap 4 -->
    <script src="/plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js"></script>
    <!-- Summernote -->
    <script src="/plugins/summernote/summernote-bs4.min.js"></script>
    <!-- sweet alert 2-->
    <script src="~/plugins/sweetalert2/sweetalert2.min.js"></script>
    <!-- Bootstrap Switch-->
    <script src="~/plugins/bootstrap-switch/js/bootstrap-switch.min.js"></script>
    <!-- overlayScrollbars -->
    <script src="/plugins/overlayScrollbars/js/jquery.overlayScrollbars.min.js"></script>
    <!-- AdminLTE App -->
    <script src="/dist/js/adminlte.js"></script>

    <!-- Translations for JavaScript -->
    <script type="text/javascript">
        // Global translations object for multilanguage support
        window.t = {
            // Background service status translations
            "No background services found": "@Html.Raw(Localizer["No background services found"].Value)",
            "Running": "@Html.Raw(Localizer["Running"].Value)",
            "Failed": "@Html.Raw(Localizer["Failed"].Value)",
            "Not Started": "@Html.Raw(Localizer["Not Started"].Value)"
        };
    </script>
    <script type="text/javascript">
        // Set the correct selected language based on the current culture
        const currentCulture = '@CultureInfo.CurrentCulture.Name';
        if (currentCulture === 'en') {
            document.getElementById('lang-en').selected = true;
        } else if (currentCulture === 'tr') {
            document.getElementById('lang-tr').selected = true;
        }
        // function changeLanguage(language) {
        //     // Redirect to the server-side handler to change the language
        //     window.location.href = '/SetLanguage?lang=' + language;
        // }
        function changeLanguage(language) {
            // Get the current page's relative URL (path + query string)
            const currentUrl = window.location.pathname + window.location.search;

            // Redirect to the SetLanguage page with language and returnUrl
            window.location.href = `/SetLanguage?lang=${language}&returnUrl=${encodeURIComponent(currentUrl)}`;
        }

        $(document).ready(function () {
            $("input[data-bootstrap-switch]").each(function () {
                $(this).bootstrapSwitch();
                $(this).on('switchChange.bootstrapSwitch', function (event, state) {
                    console.log(event);
                    console.log(state);
                    console.log(event.target.id);
                    // Update the underlying checkbox state
                    if (state) {
                        event.target.setAttribute('checked', 'checked');
                    } else {

                        event.target.removeAttribute('checked');
                    }
                });
            });
        });

    </script>
    <!-- Required Field Indicator -->
    <script src="/js/required-field-indicator.js"></script>
    @await RenderSectionAsync("Scripts", false)
</body>
</html>
