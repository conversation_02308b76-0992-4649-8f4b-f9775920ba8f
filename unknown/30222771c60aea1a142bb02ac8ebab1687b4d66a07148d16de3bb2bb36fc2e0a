# Parça *******.2: Date Range Filtering (15-20 dakika)

## 📋 <PERSON><PERSON><PERSON>
Date range filtering functionality'nin implementasyonu. Date picker integration, filter modal ve date range validation.

## 🎯 Hedefler
- ✅ Filter modal oluşturma
- ✅ Date picker integration
- ✅ Date range validation
- ✅ Filter state management

## 📊 Yapılacak İşler

### ✅ YAPILACAKLAR

#### *******.2.1 Filter Modal HTML

**Dosya**: `src/Areas/MyAccount/Pages/RzwSavings/Details.cshtml` (Filter modal ekleme)
```html
<!-- Filter Modal (Details.cshtml'in sonuna ekleme) -->
<div class="modal fade" id="filterModal" tabindex="-1" aria-labelledby="filterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="filterModalLabel">
                    <i class="fas fa-filter text-primary"></i>
                    @Localizer["Filter Payment History"]
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="filterForm">
                    <div class="row">
                        <!-- Date Range Filter -->
                        <div class="col-md-6">
                            <div class="filter-section">
                                <h6 class="filter-section-title">
                                    <i class="fas fa-calendar-alt"></i>
                                    @Localizer["Date Range"]
                                </h6>
                                
                                <div class="date-range-presets mb-3">
                                    <div class="btn-group btn-group-sm w-100" role="group">
                                        <input type="radio" class="btn-check" name="datePreset" id="preset-all" value="all" checked>
                                        <label class="btn btn-outline-secondary" for="preset-all">@Localizer["All Time"]</label>
                                        
                                        <input type="radio" class="btn-check" name="datePreset" id="preset-today" value="today">
                                        <label class="btn btn-outline-secondary" for="preset-today">@Localizer["Today"]</label>
                                        
                                        <input type="radio" class="btn-check" name="datePreset" id="preset-week" value="week">
                                        <label class="btn btn-outline-secondary" for="preset-week">@Localizer["This Week"]</label>
                                        
                                        <input type="radio" class="btn-check" name="datePreset" id="preset-month" value="month">
                                        <label class="btn btn-outline-secondary" for="preset-month">@Localizer["This Month"]</label>
                                    </div>
                                </div>

                                <div class="custom-date-range" id="customDateRange" style="display: none;">
                                    <div class="row">
                                        <div class="col-6">
                                            <label for="startDate" class="form-label">@Localizer["Start Date"]</label>
                                            <input type="date" 
                                                   class="form-control" 
                                                   id="startDate" 
                                                   name="startDate"
                                                   max="@DateTime.UtcNow.ToString("yyyy-MM-dd")">
                                        </div>
                                        <div class="col-6">
                                            <label for="endDate" class="form-label">@Localizer["End Date"]</label>
                                            <input type="date" 
                                                   class="form-control" 
                                                   id="endDate" 
                                                   name="endDate"
                                                   max="@DateTime.UtcNow.ToString("yyyy-MM-dd")">
                                        </div>
                                    </div>
                                    <div class="date-validation-message" id="dateValidationMessage"></div>
                                </div>

                                <div class="mt-2">
                                    <button type="button" class="btn btn-link btn-sm p-0" onclick="toggleCustomDateRange()">
                                        <i class="fas fa-calendar-plus"></i>
                                        <span id="customDateToggleText">@Localizer["Custom Date Range"]</span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Amount Range Filter -->
                        <div class="col-md-6">
                            <div class="filter-section">
                                <h6 class="filter-section-title">
                                    <i class="fas fa-coins"></i>
                                    @Localizer["Amount Range"]
                                </h6>
                                
                                <div class="amount-range-inputs">
                                    <div class="row">
                                        <div class="col-6">
                                            <label for="minAmount" class="form-label">@Localizer["Minimum Amount"]</label>
                                            <div class="input-group">
                                                <input type="number" 
                                                       class="form-control" 
                                                       id="minAmount" 
                                                       name="minAmount"
                                                       step="0.00000001"
                                                       min="0"
                                                       placeholder="0.00000000">
                                                <span class="input-group-text">RZW</span>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <label for="maxAmount" class="form-label">@Localizer["Maximum Amount"]</label>
                                            <div class="input-group">
                                                <input type="number" 
                                                       class="form-control" 
                                                       id="maxAmount" 
                                                       name="maxAmount"
                                                       step="0.00000001"
                                                       min="0"
                                                       placeholder="0.00000000">
                                                <span class="input-group-text">RZW</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="amount-validation-message" id="amountValidationMessage"></div>
                                </div>

                                <div class="amount-presets mt-3">
                                    <small class="text-muted d-block mb-2">@Localizer["Quick amounts"]:</small>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-info" onclick="setAmountFilter('min', 0.001)">
                                            > 0.001
                                        </button>
                                        <button type="button" class="btn btn-outline-info" onclick="setAmountFilter('min', 0.01)">
                                            > 0.01
                                        </button>
                                        <button type="button" class="btn btn-outline-info" onclick="setAmountFilter('min', 0.1)">
                                            > 0.1
                                        </button>
                                        <button type="button" class="btn btn-outline-info" onclick="setAmountFilter('min', 1)">
                                            > 1.0
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <!-- Transaction Status Filter -->
                        <div class="col-md-6">
                            <div class="filter-section">
                                <h6 class="filter-section-title">
                                    <i class="fas fa-check-circle"></i>
                                    @Localizer["Transaction Status"]
                                </h6>
                                
                                <div class="status-checkboxes">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="statusConfirmed" name="status" value="confirmed" checked>
                                        <label class="form-check-label" for="statusConfirmed">
                                            <span class="badge badge-success badge-sm me-2">
                                                <i class="fas fa-check-circle"></i>
                                            </span>
                                            @Localizer["Confirmed"]
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="statusPending" name="status" value="pending" checked>
                                        <label class="form-check-label" for="statusPending">
                                            <span class="badge badge-warning badge-sm me-2">
                                                <i class="fas fa-clock"></i>
                                            </span>
                                            @Localizer["Pending"]
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="statusRecent" name="status" value="recent" checked>
                                        <label class="form-check-label" for="statusRecent">
                                            <span class="badge badge-info badge-sm me-2">
                                                <i class="fas fa-clock"></i>
                                            </span>
                                            @Localizer["Recent"]
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Search Filter -->
                        <div class="col-md-6">
                            <div class="filter-section">
                                <h6 class="filter-section-title">
                                    <i class="fas fa-search"></i>
                                    @Localizer["Search"]
                                </h6>
                                
                                <div class="search-inputs">
                                    <div class="mb-3">
                                        <label for="descriptionSearch" class="form-label">@Localizer["Description"]</label>
                                        <input type="text" 
                                               class="form-control" 
                                               id="descriptionSearch" 
                                               name="descriptionSearch"
                                               placeholder="@Localizer["Search in payment descriptions..."]">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="transactionHashSearch" class="form-label">@Localizer["Transaction Hash"]</label>
                                        <input type="text" 
                                               class="form-control" 
                                               id="transactionHashSearch" 
                                               name="transactionHashSearch"
                                               placeholder="@Localizer["Enter transaction hash..."]">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Active Filters Display -->
                    <div class="active-filters-section" id="activeFiltersSection" style="display: none;">
                        <hr>
                        <h6 class="filter-section-title">
                            <i class="fas fa-tags"></i>
                            @Localizer["Active Filters"]
                        </h6>
                        <div class="active-filters" id="activeFiltersList">
                            <!-- Active filters will be displayed here -->
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <div class="filter-actions w-100">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="filter-info">
                            <small class="text-muted" id="filterResultCount">
                                @Localizer["Filters will be applied to"] @Model.ViewModel.Statistics.TotalPaymentCount @Localizer["payments"]
                            </small>
                        </div>
                        <div class="filter-buttons">
                            <button type="button" class="btn btn-outline-secondary" onclick="clearAllFilters()">
                                <i class="fas fa-eraser"></i>
                                @Localizer["Clear All"]
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                @Localizer["Cancel"]
                            </button>
                            <button type="button" class="btn btn-primary" onclick="applyFilters()">
                                <i class="fas fa-filter"></i>
                                @Localizer["Apply Filters"]
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
```

#### *******.2.2 Filter Management JavaScript

**Dosya**: `src/wwwroot/js/rzw-savings-details.js` (Filter functionality ekleme)
```javascript
// Filter Management
class FilterManager {
    constructor(tableInstance) {
        this.table = tableInstance;
        this.filters = {
            dateRange: {
                preset: 'all',
                startDate: null,
                endDate: null
            },
            amountRange: {
                minAmount: null,
                maxAmount: null
            },
            status: ['confirmed', 'pending', 'recent'],
            search: {
                description: '',
                transactionHash: ''
            }
        };
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.initializeDateInputs();
    }

    bindEvents() {
        // Date preset changes
        document.querySelectorAll('input[name="datePreset"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.handleDatePresetChange(e.target.value);
            });
        });

        // Custom date range inputs
        document.getElementById('startDate')?.addEventListener('change', () => {
            this.validateDateRange();
        });

        document.getElementById('endDate')?.addEventListener('change', () => {
            this.validateDateRange();
        });

        // Amount range inputs
        document.getElementById('minAmount')?.addEventListener('input', () => {
            this.validateAmountRange();
        });

        document.getElementById('maxAmount')?.addEventListener('input', () => {
            this.validateAmountRange();
        });

        // Status checkboxes
        document.querySelectorAll('input[name="status"]').forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.updateActiveFilters();
            });
        });

        // Search inputs
        document.getElementById('descriptionSearch')?.addEventListener('input', () => {
            this.updateActiveFilters();
        });

        document.getElementById('transactionHashSearch')?.addEventListener('input', () => {
            this.updateActiveFilters();
        });
    }

    initializeDateInputs() {
        const today = new Date();
        const accountStartDate = new Date('@Model.ViewModel.Account.StartDate.ToString("yyyy-MM-dd")');
        
        // Set min date to account start date
        document.getElementById('startDate')?.setAttribute('min', accountStartDate.toISOString().split('T')[0]);
        document.getElementById('endDate')?.setAttribute('min', accountStartDate.toISOString().split('T')[0]);
    }

    handleDatePresetChange(preset) {
        this.filters.dateRange.preset = preset;
        
        const customDateRange = document.getElementById('customDateRange');
        const startDateInput = document.getElementById('startDate');
        const endDateInput = document.getElementById('endDate');
        
        if (preset === 'custom') {
            customDateRange.style.display = 'block';
            this.updateCustomDateToggle(true);
        } else {
            customDateRange.style.display = 'none';
            this.updateCustomDateToggle(false);
            
            // Clear custom date inputs
            startDateInput.value = '';
            endDateInput.value = '';
            
            // Set preset dates
            const dates = this.getPresetDates(preset);
            this.filters.dateRange.startDate = dates.start;
            this.filters.dateRange.endDate = dates.end;
        }
        
        this.updateActiveFilters();
    }

    getPresetDates(preset) {
        const today = new Date();
        const accountStart = new Date('@Model.ViewModel.Account.StartDate.ToString("yyyy-MM-dd")');
        
        switch (preset) {
            case 'today':
                return {
                    start: today.toISOString().split('T')[0],
                    end: today.toISOString().split('T')[0]
                };
            case 'week':
                const weekStart = new Date(today);
                weekStart.setDate(today.getDate() - today.getDay());
                return {
                    start: weekStart.toISOString().split('T')[0],
                    end: today.toISOString().split('T')[0]
                };
            case 'month':
                const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
                return {
                    start: monthStart.toISOString().split('T')[0],
                    end: today.toISOString().split('T')[0]
                };
            case 'all':
            default:
                return {
                    start: null,
                    end: null
                };
        }
    }

    validateDateRange() {
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        const messageElement = document.getElementById('dateValidationMessage');
        
        let isValid = true;
        let message = '';
        
        if (startDate && endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            
            if (start > end) {
                isValid = false;
                message = 'Start date cannot be after end date';
            } else if (end > new Date()) {
                isValid = false;
                message = 'End date cannot be in the future';
            }
        }
        
        if (isValid) {
            messageElement.textContent = '';
            messageElement.className = 'date-validation-message';
            this.filters.dateRange.startDate = startDate || null;
            this.filters.dateRange.endDate = endDate || null;
            
            // Set preset to custom if dates are manually entered
            if (startDate || endDate) {
                document.getElementById('preset-custom')?.click();
            }
        } else {
            messageElement.textContent = message;
            messageElement.className = 'date-validation-message text-danger';
        }
        
        this.updateActiveFilters();
        return isValid;
    }

    validateAmountRange() {
        const minAmount = parseFloat(document.getElementById('minAmount').value) || null;
        const maxAmount = parseFloat(document.getElementById('maxAmount').value) || null;
        const messageElement = document.getElementById('amountValidationMessage');
        
        let isValid = true;
        let message = '';
        
        if (minAmount !== null && minAmount < 0) {
            isValid = false;
            message = 'Minimum amount cannot be negative';
        } else if (maxAmount !== null && maxAmount < 0) {
            isValid = false;
            message = 'Maximum amount cannot be negative';
        } else if (minAmount !== null && maxAmount !== null && minAmount > maxAmount) {
            isValid = false;
            message = 'Minimum amount cannot be greater than maximum amount';
        }
        
        if (isValid) {
            messageElement.textContent = '';
            messageElement.className = 'amount-validation-message';
            this.filters.amountRange.minAmount = minAmount;
            this.filters.amountRange.maxAmount = maxAmount;
        } else {
            messageElement.textContent = message;
            messageElement.className = 'amount-validation-message text-danger';
        }
        
        this.updateActiveFilters();
        return isValid;
    }

    updateActiveFilters() {
        const activeFilters = this.getActiveFilters();
        const section = document.getElementById('activeFiltersSection');
        const list = document.getElementById('activeFiltersList');
        
        if (activeFilters.length > 0) {
            section.style.display = 'block';
            list.innerHTML = activeFilters.map(filter => 
                `<span class="badge bg-primary me-2 mb-2">
                    ${filter}
                    <button type="button" class="btn-close btn-close-white btn-sm ms-1" onclick="removeFilter('${filter}')"></button>
                </span>`
            ).join('');
        } else {
            section.style.display = 'none';
        }
    }

    getActiveFilters() {
        const filters = [];
        
        // Date range filters
        if (this.filters.dateRange.preset !== 'all') {
            if (this.filters.dateRange.preset === 'custom') {
                if (this.filters.dateRange.startDate || this.filters.dateRange.endDate) {
                    const start = this.filters.dateRange.startDate || 'Start';
                    const end = this.filters.dateRange.endDate || 'End';
                    filters.push(`Date: ${start} - ${end}`);
                }
            } else {
                filters.push(`Date: ${this.filters.dateRange.preset}`);
            }
        }
        
        // Amount range filters
        if (this.filters.amountRange.minAmount !== null) {
            filters.push(`Min Amount: ${this.filters.amountRange.minAmount} RZW`);
        }
        if (this.filters.amountRange.maxAmount !== null) {
            filters.push(`Max Amount: ${this.filters.amountRange.maxAmount} RZW`);
        }
        
        // Status filters
        const allStatuses = ['confirmed', 'pending', 'recent'];
        const selectedStatuses = Array.from(document.querySelectorAll('input[name="status"]:checked')).map(cb => cb.value);
        if (selectedStatuses.length < allStatuses.length) {
            filters.push(`Status: ${selectedStatuses.join(', ')}`);
        }
        
        // Search filters
        const description = document.getElementById('descriptionSearch')?.value.trim();
        if (description) {
            filters.push(`Description: "${description}"`);
        }
        
        const transactionHash = document.getElementById('transactionHashSearch')?.value.trim();
        if (transactionHash) {
            filters.push(`Transaction: "${transactionHash}"`);
        }
        
        return filters;
    }

    applyFilters() {
        // Validate all inputs
        const dateValid = this.validateDateRange();
        const amountValid = this.validateAmountRange();
        
        if (!dateValid || !amountValid) {
            return;
        }
        
        // Collect all filter values
        this.collectFilterValues();
        
        // Apply filters to table
        this.table.filters = this.filters;
        this.table.currentPage = 1; // Reset to first page
        this.table.loadTableData();
        
        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('filterModal'));
        modal.hide();
        
        // Show success message
        showToast('Filters applied successfully', 'success');
    }

    collectFilterValues() {
        // Status filters
        this.filters.status = Array.from(document.querySelectorAll('input[name="status"]:checked')).map(cb => cb.value);
        
        // Search filters
        this.filters.search.description = document.getElementById('descriptionSearch')?.value.trim() || '';
        this.filters.search.transactionHash = document.getElementById('transactionHashSearch')?.value.trim() || '';
    }

    clearAllFilters() {
        // Reset all form inputs
        document.getElementById('filterForm').reset();
        
        // Reset filter object
        this.filters = {
            dateRange: { preset: 'all', startDate: null, endDate: null },
            amountRange: { minAmount: null, maxAmount: null },
            status: ['confirmed', 'pending', 'recent'],
            search: { description: '', transactionHash: '' }
        };
        
        // Check all status checkboxes
        document.querySelectorAll('input[name="status"]').forEach(cb => cb.checked = true);
        
        // Hide custom date range
        document.getElementById('customDateRange').style.display = 'none';
        this.updateCustomDateToggle(false);
        
        // Clear validation messages
        document.getElementById('dateValidationMessage').textContent = '';
        document.getElementById('amountValidationMessage').textContent = '';
        
        // Update active filters display
        this.updateActiveFilters();
    }

    updateCustomDateToggle(isCustom) {
        const toggleText = document.getElementById('customDateToggleText');
        if (toggleText) {
            toggleText.textContent = isCustom ? 'Hide Custom Range' : 'Custom Date Range';
        }
    }
}

// Global functions for filter modal
function showFilterModal() {
    const modal = new bootstrap.Modal(document.getElementById('filterModal'));
    modal.show();
}

function toggleCustomDateRange() {
    const customRange = document.getElementById('customDateRange');
    const isVisible = customRange.style.display !== 'none';
    
    if (isVisible) {
        customRange.style.display = 'none';
        document.getElementById('preset-all').checked = true;
    } else {
        customRange.style.display = 'block';
        // Create custom preset option if it doesn't exist
        let customPreset = document.getElementById('preset-custom');
        if (!customPreset) {
            const presetsContainer = document.querySelector('.date-range-presets .btn-group');
            presetsContainer.insertAdjacentHTML('beforeend', `
                <input type="radio" class="btn-check" name="datePreset" id="preset-custom" value="custom">
                <label class="btn btn-outline-secondary" for="preset-custom">Custom</label>
            `);
            customPreset = document.getElementById('preset-custom');
            customPreset.addEventListener('change', (e) => {
                if (window.interestHistoryTable?.filterManager) {
                    window.interestHistoryTable.filterManager.handleDatePresetChange('custom');
                }
            });
        }
        customPreset.checked = true;
    }
    
    if (window.interestHistoryTable?.filterManager) {
        window.interestHistoryTable.filterManager.updateCustomDateToggle(!isVisible);
    }
}

function setAmountFilter(type, value) {
    const input = document.getElementById(type === 'min' ? 'minAmount' : 'maxAmount');
    if (input) {
        input.value = value;
        if (window.interestHistoryTable?.filterManager) {
            window.interestHistoryTable.filterManager.validateAmountRange();
        }
    }
}

function applyFilters() {
    if (window.interestHistoryTable?.filterManager) {
        window.interestHistoryTable.filterManager.applyFilters();
    }
}

function clearAllFilters() {
    if (window.interestHistoryTable?.filterManager) {
        window.interestHistoryTable.filterManager.clearAllFilters();
    }
}

function removeFilter(filterText) {
    // Implementation for removing specific filters
    console.log('Remove filter:', filterText);
}

// Update table initialization to include filter manager
InterestHistoryTable.prototype.initFilters = function() {
    this.filterManager = new FilterManager(this);
};

// Update the main initialization
document.addEventListener('DOMContentLoaded', function() {
    const accountId = window.rzwSavingsDetails?.accountId;
    if (accountId) {
        window.interestHistoryTable = new InterestHistoryTable(accountId);
        window.interestHistoryTable.initPagination();
        window.interestHistoryTable.initFilters();
    }
});
```

## 📋 Parça Kontrol Listesi

### ✅ YAPILACAKLAR
- [ ] Filter modal HTML oluşturma
- [ ] Date range filtering
- [ ] Amount range filtering
- [ ] Status filtering
- [ ] Search functionality
- [ ] Filter validation
- [ ] Active filters display
- [ ] Filter state management

### 🔄 YAPILMAKTA OLANLAR
- (Henüz başlanmadı)

### ✅ YAPILMIŞLAR
- (Henüz tamamlanmadı)

## 📝 Notlar

### Filter Features
- **Date range presets**: All time, today, this week, this month
- **Custom date range**: Manual date selection
- **Amount filtering**: Min/max amount ranges
- **Status filtering**: Confirmed, pending, recent
- **Search functionality**: Description and transaction hash
- **Active filters**: Visual display of applied filters

### Validation
- **Date validation**: Start/end date logic
- **Amount validation**: Min/max range validation
- **Real-time feedback**: Immediate validation messages
- **Form state**: Proper form state management

### User Experience
- **Quick presets**: Common date ranges
- **Visual feedback**: Active filter badges
- **Clear all**: Reset all filters
- **Modal interface**: Clean, organized layout

### Sonraki Parça
Bu parça tamamlandıktan sonra **Parça *******.3: Advanced Search** başlayacak.

---
**Tahmini Süre**: 15-20 dakika
**Öncelik**: Yüksek
**Bağımlılıklar**: Parça *******.1 tamamlanmış olmalı
