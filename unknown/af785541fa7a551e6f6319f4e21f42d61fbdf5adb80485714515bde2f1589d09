using System.ComponentModel.DataAnnotations;
using RazeWinComTr.Helpers;

namespace RazeWinComTr.Attributes;

public class IbanAttribute : ValidationAttribute
{
    public IbanAttribute()
    {
        ErrorMessageResourceType = typeof(SharedResource);
        ErrorMessageResourceName = "Enter a valid IBAN";
    }

    public override bool IsValid(object? value)
    {
        if (value == null)
            return true; // [Required] ile kombinleyin

        var iban = value.ToString();

        // Use the ValidationHelper for IBAN validation
        return ValidationHelper.IsValidIban(iban);
    }
}
