using System.ComponentModel.DataAnnotations;

namespace RazeWinComTr.Areas.Admin.ViewModels.Bank
{
    public class BankViewModel
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string BankName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string AccountHolder { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string Iban { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;

        public int Order { get; set; }

        public DateTime CreatedDate { get; set; }

        public DateTime? ModifiedDate { get; set; }
    }
}
