namespace RazeWinComTr.Models;

/// <summary>
/// RZW-specific balance information for a user
/// </summary>
public class RzwBalanceInfo
{
    /// <summary>
    /// The user ID
    /// </summary>
    public int UserId { get; set; }
    
    /// <summary>
    /// Available RZW balance (can be used for trading/savings)
    /// </summary>
    public decimal AvailableRzw { get; set; }
    
    /// <summary>
    /// Locked RZW balance (cannot be used for trading)
    /// </summary>
    public decimal LockedRzw { get; set; }
    
    /// <summary>
    /// Total RZW balance (Available + Locked)
    /// </summary>
    public decimal TotalRzw => AvailableRzw + LockedRzw;
    
    /// <summary>
    /// Number of active savings accounts
    /// </summary>
    public int ActiveSavingsAccountCount { get; set; }

    // Computed properties

    /// <summary>
    /// Whether the user has any RZW balance
    /// </summary>
    public bool HasRzwBalance => TotalRzw > 0;

    /// <summary>
    /// Whether the user has any available RZW for new savings
    /// </summary>
    public bool HasAvailableRzw => AvailableRzw > 0;

    /// <summary>
    /// Whether the user has any locked RZW
    /// </summary>
    public bool HasLockedRzw => LockedRzw > 0;

    /// <summary>
    /// Whether the user has any savings accounts
    /// </summary>
    public bool HasSavingsAccounts => ActiveSavingsAccountCount > 0;
}
