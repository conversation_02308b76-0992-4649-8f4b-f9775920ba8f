using RazeWinComTr.Areas.Admin.DbModel;

namespace RazeWinComTr.Areas.Admin.ViewModels.Withdrawal
{
    public class WithdrawalViewModel
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string UserEmail { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public decimal Balance { get; set; }
        public decimal WithdrawalAmount { get; set; }
        public string AccountHolder { get; set; } = string.Empty;
        public string Iban { get; set; } = string.Empty;
        public WithdrawalStatus Status { get; set; }
        public DateTime CreatedDate { get; set; }
    }
}
