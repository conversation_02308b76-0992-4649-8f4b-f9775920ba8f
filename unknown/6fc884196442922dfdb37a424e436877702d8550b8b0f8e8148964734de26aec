using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;

namespace RazeWinComTr.Tests.TestInfrastructure.Utilities
{
    /// <summary>
    /// Simple helper methods for creating test data using natural .NET object initialization.
    /// Compatible with existing TestDataGenerator - preserves all existing test data patterns.
    /// </summary>
    public static class TestDataHelpers
    {
        // ==================== USER HELPERS ====================

        /// <summary>
        /// Creates a simple test user with basic properties.
        /// Compatible with existing TestDataGenerator.CreateUser method.
        /// </summary>
        public static User CreateUser(int userId = 1, string? email = null, string? referralCode = null, int? referrerId = null)
        {
            return new User
            {
                UserId = userId,
                Email = email ?? $"user{userId}@test.com",
                Name = $"User{userId}",
                Surname = "Test",
                PhoneNumber = "1234567890",
                IdentityNumber = $"1234567890{userId}",
                ReferralCode = referralCode ?? $"REF{userId:D3}",
                ReferrerId = referrerId,
                IsActive = 1,
                CrDate = DateTime.UtcNow,
                ModDate = DateTime.UtcNow,
                BirthDate = DateTime.UtcNow.AddYears(-25),
                PasswordHash = "test-hash"
            };
        }

        /// <summary>
        /// Creates a user using the existing TestDataGenerator pattern for backward compatibility.
        /// </summary>
        public static User CreateUserLegacy(int userId, string? referralCode = null, int? referrerId = null)
        {
            return TestDataGenerator.CreateUser(userId, referralCode, referrerId);
        }

        /// <summary>
        /// Creates a list of users forming a referral chain.
        /// User 1 has no referrer, User 2 referred by User 1, etc.
        /// </summary>
        public static List<User> CreateReferralChain(int count, int startUserId = 1)
        {
            var users = new List<User>();
            
            for (int i = 0; i < count; i++)
            {
                var userId = startUserId + i;
                var referrerId = i > 0 ? startUserId + i - 1 : (int?)null;
                
                users.Add(CreateUser(userId, referrerId: referrerId));
            }
            
            return users;
        }

        // ==================== PACKAGE HELPERS ====================

        /// <summary>
        /// Creates a package using existing TestDataGenerator pattern for backward compatibility.
        /// </summary>
        public static Package CreatePackage(int packageId, string name, decimal price)
        {
            return TestDataGenerator.CreatePackage(packageId, name, price);
        }

        /// <summary>
        /// Creates a Bronze package (1 level, 5% reward).
        /// </summary>
        public static Package CreateBronzePackage()
        {
            return CreatePackage(1, "Bronze", 100m);
        }

        /// <summary>
        /// Creates a Silver package (2 levels, 10% and 5% rewards).
        /// </summary>
        public static Package CreateSilverPackage()
        {
            return CreatePackage(2, "Silver", 250m);
        }

        /// <summary>
        /// Creates a Gold package (3 levels, 15%, 10%, and 5% rewards).
        /// </summary>
        public static Package CreateGoldPackage()
        {
            return CreatePackage(3, "Gold", 500m);
        }

        /// <summary>
        /// Creates a Platinum package (4 levels, 20%, 15%, 10%, and 5% rewards).
        /// </summary>
        public static Package CreatePlatinumPackage()
        {
            return CreatePackage(4, "Platinum", 1000m);
        }

        /// <summary>
        /// Creates all standard packages.
        /// </summary>
        public static List<Package> CreateAllStandardPackages()
        {
            return new List<Package>
            {
                CreateBronzePackage(),
                CreateSilverPackage(),
                CreateGoldPackage(),
                CreatePlatinumPackage()
            };
        }

        // ==================== PACKAGE REWARD PERCENTAGE HELPERS ====================

        /// <summary>
        /// Creates a package reward percentage using existing TestDataGenerator pattern.
        /// </summary>
        public static PackageRewardPercentage CreatePackageRewardPercentage(int id, int packageId, int level, decimal rzwPercentage, decimal tlPercentage = 0m)
        {
            return TestDataGenerator.CreatePackageRewardPercentage(id, packageId, level, rzwPercentage, tlPercentage);
        }

        /// <summary>
        /// Creates reward percentages for Bronze package.
        /// </summary>
        public static List<PackageRewardPercentage> CreateBronzeRewardPercentages()
        {
            return new List<PackageRewardPercentage>
            {
                CreatePackageRewardPercentage(1, 1, 1, 5m, 0m)
            };
        }

        /// <summary>
        /// Creates reward percentages for Silver package.
        /// </summary>
        public static List<PackageRewardPercentage> CreateSilverRewardPercentages()
        {
            return new List<PackageRewardPercentage>
            {
                CreatePackageRewardPercentage(2, 2, 1, 10m, 0m),
                CreatePackageRewardPercentage(3, 2, 2, 5m, 0m)
            };
        }

        /// <summary>
        /// Creates reward percentages for Gold package.
        /// </summary>
        public static List<PackageRewardPercentage> CreateGoldRewardPercentages()
        {
            return new List<PackageRewardPercentage>
            {
                CreatePackageRewardPercentage(4, 3, 1, 15m, 0m),
                CreatePackageRewardPercentage(5, 3, 2, 10m, 0m),
                CreatePackageRewardPercentage(6, 3, 3, 5m, 0m)
            };
        }

        /// <summary>
        /// Creates reward percentages for Platinum package.
        /// </summary>
        public static List<PackageRewardPercentage> CreatePlatinumRewardPercentages()
        {
            return new List<PackageRewardPercentage>
            {
                CreatePackageRewardPercentage(7, 4, 1, 20m, 0m),
                CreatePackageRewardPercentage(8, 4, 2, 15m, 0m),
                CreatePackageRewardPercentage(9, 4, 3, 10m, 0m),
                CreatePackageRewardPercentage(10, 4, 4, 5m, 0m)
            };
        }

        /// <summary>
        /// Creates all standard reward percentages for all packages.
        /// </summary>
        public static List<PackageRewardPercentage> CreateAllStandardRewardPercentages()
        {
            var percentages = new List<PackageRewardPercentage>();
            percentages.AddRange(CreateBronzeRewardPercentages());
            percentages.AddRange(CreateSilverRewardPercentages());
            percentages.AddRange(CreateGoldRewardPercentages());
            percentages.AddRange(CreatePlatinumRewardPercentages());
            return percentages;
        }

        // ==================== WALLET HELPERS ====================

        /// <summary>
        /// Creates a simple wallet for testing.
        /// </summary>
        public static Wallet CreateWallet(int userId, int coinId, decimal balance = 0m)
        {
            return new Wallet
            {
                UserId = userId,
                CoinId = coinId,
                Balance = balance,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow
            };
        }

        // ==================== DEPOSIT HELPERS ====================

        /// <summary>
        /// Creates a deposit using existing TestDataGenerator pattern for backward compatibility.
        /// </summary>
        public static Deposit CreateDeposit(int id, int userId, decimal amount, DepositStatus status = DepositStatus.Approved, DepositRewardStatus rewardStatus = DepositRewardStatus.Pending)
        {
            return TestDataGenerator.CreateDeposit(id, userId, amount, status, rewardStatus);
        }

        /// <summary>
        /// Creates a simple deposit for testing (without ID for new entities).
        /// </summary>
        public static Deposit CreateSimpleDeposit(int userId, decimal amount, string processStatus = "Completed")
        {
            return new Deposit
            {
                UserId = userId,
                Amount = amount,
                ProcessStatus = processStatus,
                DepositType = "DEPOSIT",
                FullName = $"User{userId} Test",
                IpAddress = "127.0.0.1",
                CreatedDate = DateTime.UtcNow
            };
        }

        // ==================== USER PACKAGE HELPERS ====================

        /// <summary>
        /// Creates a user package using existing TestDataGenerator pattern.
        /// </summary>
        public static UserPackage CreateUserPackage(int id, int userId, int packageId, DateTime? purchaseDate = null)
        {
            return TestDataGenerator.CreateUserPackage(id, userId, packageId, purchaseDate);
        }

        // ==================== REFERRAL REWARD HELPERS ====================

        /// <summary>
        /// Creates a referral reward using existing TestDataGenerator pattern.
        /// </summary>
        public static ReferralReward CreateReferralReward(int id, int userId, int referredUserId, int packageId, int level, decimal amount, decimal percentage, decimal originalAmount)
        {
            return TestDataGenerator.CreateReferralReward(id, userId, referredUserId, packageId, level, amount, percentage, originalAmount);
        }

        /// <summary>
        /// Creates a referral reward with deposit ID using existing TestDataGenerator pattern.
        /// </summary>
        public static ReferralReward CreateReferralReward(int depositId, int id, int userId, int referredUserId, int packageId, int level, decimal amount)
        {
            return TestDataGenerator.CreateReferralReward(depositId, id, userId, referredUserId, packageId, level, amount);
        }
    }
}
