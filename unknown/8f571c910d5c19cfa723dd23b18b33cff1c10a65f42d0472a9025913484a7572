/**
 * Packages Page JavaScript
 * 
 * This script handles functionality specific to the packages page,
 * including package selection, UI interactions, and animations.
 */
(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        // Add hover effects to package cards
        $('.package-card').hover(
            function() {
                $(this).addClass('hover');
            },
            function() {
                $(this).removeClass('hover');
            }
        );

        // Smooth scroll to package section when clicking on navigation links
        $('a[href="#packages"]').on('click', function(e) {
            e.preventDefault();
            $('html, body').animate({
                scrollTop: $('.packages-container').offset().top - 100
            }, 800);
        });

        // Initialize tooltips
        $('[data-toggle="tooltip"]').tooltip();

        // Handle package selection
        $('.btn-select-package').on('click', function() {
            const packageId = $(this).data('package-id');
            const packageName = $(this).data('package-name');
            const packagePrice = $(this).data('package-price');
            
            // If we have a purchase modal, populate it
            if ($('#purchaseModal').length) {
                $('#selectedPackageId').val(packageId);
                $('#selectedPackageName').text(packageName);
                $('#selectedPackagePrice').text(packagePrice);
                $('#purchaseModal').modal('show');
            }
        });
    });

})(jQuery);
