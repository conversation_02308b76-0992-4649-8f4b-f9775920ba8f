using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.Constants;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Net;

namespace RazeWinComTr.Areas.Admin.Pages.Market;

public class CreateModel : PageModel
{
    private readonly IMarketService _marketService;
    private readonly string _fileStoragePath;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ILogger<CreateModel> _logger;

    public CreateModel(
        IMarketService marketService,
        IStringLocalizer<SharedResource> localizer,
        IConfiguration configuration,
        IHttpClientFactory httpClientFactory,
        ILogger<CreateModel> logger)
    {
        _marketService = marketService;
        _localizer = localizer;
        _fileStoragePath = configuration["FileStoragePath"]!;
        _httpClientFactory = httpClientFactory;
        _logger = logger;
    }

    public SweetAlert2Message? AlertMessage { get; set; }

    [BindProperty] public MarketCreateViewModel ViewEntity { get; set; } = new();

    [BindProperty] public IFormFile? ImageFile { get; set; }



    public async Task<IActionResult> OnPostAsync()
    {
        try
        {
            // Ensure ApiServiceName is null when IsApi is false
            if (!ViewEntity.IsApi)
            {
                ViewEntity.ApiServiceName = null;
            }

            var userId = User.GetClaimUserId();
            if (!userId.HasValue) return Unauthorized();

            // Parse decimal values from string inputs with comma as decimal separator
            if (ModelState.IsValid)
            {
                // Convert any comma-formatted decimal values to proper decimal values
                ConvertDecimalFields();
            }

            if (!ModelState.IsValid) return Page();

            string? iconUrl = null;

            // Create the markets directory if it doesn't exist
            var marketsDirectory = Path.Combine(_fileStoragePath, "markets");
            Directory.CreateDirectory(marketsDirectory);

            // Determine the file name based on the coin name
            var fileName = $"{ViewEntity.Coin.ToLower()}.png";
            var filePath = Path.Combine(marketsDirectory, fileName);

            if (ImageFile != null)
            {
                // User uploaded an image, save it
                using var stream = new FileStream(filePath, FileMode.Create);
                await ImageFile.CopyToAsync(stream);
            }
            else
            {
                // No image uploaded, try to get from Bitexen if applicable or use default
                if (ViewEntity.IsApi && ViewEntity.ApiServiceName == ApiServiceNames.Bitexen)
                {
                    // Try to get the image from Bitexen
                    var coinName = ViewEntity.Coin.ToLowerInvariant();
                    var bitexenImageUrl = $"https://statics.bitexen.com/logos/color/{coinName}.png";

                    if (await TryDownloadImageAsync(bitexenImageUrl, filePath))
                    {
                        _logger.LogInformation("Downloaded coin logo from Bitexen for {Coin}", ViewEntity.Coin);
                    }
                    else
                    {
                        // If download fails, copy the default image
                        await CopyDefaultImageAsync(filePath);
                    }
                }
                else
                {
                    // Copy the default image
                    await CopyDefaultImageAsync(filePath);
                }
            }

            // Set the icon URL to the relative path
            iconUrl = Path.GetRelativePath(_fileStoragePath, filePath).Replace("\\", "/");

            var entity = new DbModel.Market
            {
                Coin = ViewEntity.Coin,
                Name = ViewEntity.Name,
                ShortName = ViewEntity.ShortName,
                PairCode = ViewEntity.PairCode,
                BuyPrice = ViewEntity.BuyPrice ?? 0,
                SellPrice = ViewEntity.SellPrice ?? 0,
                DecimalPlaces = ViewEntity.DecimalPlaces,
                MinimumBuy = ViewEntity.MinimumBuy ?? 0,
                MaximumBuy = ViewEntity.MaximumBuy ?? 0,
                MinimumSell = ViewEntity.MinimumSell ?? 0,
                MaximumSell = ViewEntity.MaximumSell ?? 0,
                Order = ViewEntity.Order,
                IsApi = ViewEntity.IsApi ? 1 : 0,
                ApiServiceName = ViewEntity.ApiServiceName, // ApiServiceName is already set to null if IsApi is false
                IsActive = ViewEntity.IsActive ? 1 : 0,
                IconUrl = iconUrl,
                CrDate = DateTime.UtcNow
            };
            await _marketService.CreateAsync(entity);

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Success"],
                Text = _localizer["Successfully saved"],
                Icon = "success",
                RedirectUrl = "/Admin/Market"
            };

            return Page();
        }
        catch (Exception ex)
        {
            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Error"],
                Text = ex.Message,
                Icon = "error"
            };
            return Page();
        }
    }

    private void ConvertDecimalFields()
    {
        // This method handles conversion of string inputs with comma as decimal separator
        // to proper decimal values using invariant culture

        // Get form values directly to handle comma decimal separators
        if (Request.Form.TryGetValue("ViewEntity.BuyPrice", out var buyPriceStr) && !string.IsNullOrEmpty(buyPriceStr))
        {
            string normalizedValue = buyPriceStr.ToString().Replace(',', '.');
            if (decimal.TryParse(normalizedValue, NumberStyles.Any, CultureInfo.InvariantCulture, out decimal result))
            {
                ViewEntity.BuyPrice = result;
            }
        }

        if (Request.Form.TryGetValue("ViewEntity.SellPrice", out var sellPriceStr) && !string.IsNullOrEmpty(sellPriceStr))
        {
            string normalizedValue = sellPriceStr.ToString().Replace(',', '.');
            if (decimal.TryParse(normalizedValue, NumberStyles.Any, CultureInfo.InvariantCulture, out decimal result))
            {
                ViewEntity.SellPrice = result;
            }
        }

        if (Request.Form.TryGetValue("ViewEntity.MinimumBuy", out var minBuyStr) && !string.IsNullOrEmpty(minBuyStr))
        {
            string normalizedValue = minBuyStr.ToString().Replace(',', '.');
            if (decimal.TryParse(normalizedValue, NumberStyles.Any, CultureInfo.InvariantCulture, out decimal result))
            {
                ViewEntity.MinimumBuy = result;
            }
        }

        if (Request.Form.TryGetValue("ViewEntity.MaximumBuy", out var maxBuyStr) && !string.IsNullOrEmpty(maxBuyStr))
        {
            string normalizedValue = maxBuyStr.ToString().Replace(',', '.');
            if (decimal.TryParse(normalizedValue, NumberStyles.Any, CultureInfo.InvariantCulture, out decimal result))
            {
                ViewEntity.MaximumBuy = result;
            }
        }

        if (Request.Form.TryGetValue("ViewEntity.MinimumSell", out var minSellStr) && !string.IsNullOrEmpty(minSellStr))
        {
            string normalizedValue = minSellStr.ToString().Replace(',', '.');
            if (decimal.TryParse(normalizedValue, NumberStyles.Any, CultureInfo.InvariantCulture, out decimal result))
            {
                ViewEntity.MinimumSell = result;
            }
        }

        if (Request.Form.TryGetValue("ViewEntity.MaximumSell", out var maxSellStr) && !string.IsNullOrEmpty(maxSellStr))
        {
            string normalizedValue = maxSellStr.ToString().Replace(',', '.');
            if (decimal.TryParse(normalizedValue, NumberStyles.Any, CultureInfo.InvariantCulture, out decimal result))
            {
                ViewEntity.MaximumSell = result;
            }
        }
    }

    /// <summary>
    /// Tries to download an image from the specified URL and save it to the specified path
    /// </summary>
    /// <param name="imageUrl">The URL of the image to download</param>
    /// <param name="savePath">The path where the image should be saved</param>
    /// <returns>True if the download was successful, false otherwise</returns>
    private async Task<bool> TryDownloadImageAsync(string imageUrl, string savePath)
    {
        try
        {
            var client = _httpClientFactory.CreateClient();
            // Set a timeout to avoid hanging if the server doesn't respond
            client.Timeout = TimeSpan.FromSeconds(5);

            // First check if the image exists by sending a HEAD request
            var headResponse = await client.SendAsync(new HttpRequestMessage(HttpMethod.Head, imageUrl));
            if (!headResponse.IsSuccessStatusCode)
            {
                return false;
            }

            // Download the image
            var response = await client.GetAsync(imageUrl);
            if (response.IsSuccessStatusCode)
            {
                using var stream = await response.Content.ReadAsStreamAsync();
                using var fileStream = new FileStream(savePath, FileMode.Create);
                await stream.CopyToAsync(fileStream);
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error downloading image from {ImageUrl}", imageUrl);
            return false;
        }
    }

    /// <summary>
    /// Copies the default coin image to the specified path
    /// </summary>
    /// <param name="destinationPath">The path where the default image should be copied</param>
    private async Task CopyDefaultImageAsync(string destinationPath)
    {
        try
        {
            var defaultImagePath = Path.Combine("wwwroot", "site", "images", "noimg_coin.png");

            if (System.IO.File.Exists(defaultImagePath))
            {
                using var sourceStream = new FileStream(defaultImagePath, FileMode.Open, FileAccess.Read);
                using var destinationStream = new FileStream(destinationPath, FileMode.Create);
                await sourceStream.CopyToAsync(destinationStream);
                _logger.LogInformation("Copied default coin image to {DestinationPath}", destinationPath);
            }
            else
            {
                _logger.LogWarning("Default coin image not found at {DefaultImagePath}", defaultImagePath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error copying default coin image to {DestinationPath}", destinationPath);
        }
    }
}

public class MarketCreateViewModel
{
    [Required(ErrorMessage = "Coin Symbol is required")]
    [StringLength(50, ErrorMessage = "Coin Symbol cannot exceed 50 characters")]
    public string Coin { get; set; } = null!;

    [Required(ErrorMessage = "Name is required")]
    [StringLength(255, ErrorMessage = "Name cannot exceed 255 characters")]
    public string Name { get; set; } = null!;

    [Required(ErrorMessage = "Short Name is required")]
    [StringLength(255, ErrorMessage = "Short Name cannot exceed 255 characters")]
    public string ShortName { get; set; } = null!;

    [Required(ErrorMessage = "Pair Code is required")]
    [StringLength(50, ErrorMessage = "Pair Code cannot exceed 50 characters")]
    public string PairCode { get; set; } = null!;

    [Range(0, double.MaxValue, ErrorMessage = "Buy Price must be a positive number")]
    public decimal? BuyPrice { get; set; } = 0;

    [Range(0, double.MaxValue, ErrorMessage = "Sell Price must be a positive number")]
    public decimal? SellPrice { get; set; } = 0;

    [Required(ErrorMessage = "Decimal Places is required")]
    [Range(0, 8, ErrorMessage = "Decimal Places must be between 0 and 8")]
    public int DecimalPlaces { get; set; } = 8;

    [Range(0, double.MaxValue, ErrorMessage = "Minimum Buy must be a positive number")]
    public decimal? MinimumBuy { get; set; } = 0;

    [Range(0, double.MaxValue, ErrorMessage = "Maximum Buy must be a positive number")]
    public decimal? MaximumBuy { get; set; } = decimal.MaxValue;

    [Range(0, double.MaxValue, ErrorMessage = "Minimum Sell must be a positive number")]
    public decimal? MinimumSell { get; set; } = 0;

    [Range(0, double.MaxValue, ErrorMessage = "Maximum Sell must be a positive number")]
    public decimal? MaximumSell { get; set; } = 0;

    [Required(ErrorMessage = "Order is required")]
    public int Order { get; set; } = 0;

    public bool IsApi { get; set; } = false;

    [StringLength(50, ErrorMessage = "API Service Name cannot exceed 50 characters")]
    public string? ApiServiceName { get; set; }

    public bool IsActive { get; set; } = true;
}
