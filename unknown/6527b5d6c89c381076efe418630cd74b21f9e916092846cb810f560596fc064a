$(function () {
    // IBAN validation using the mod-97 check algorithm
    function isValidIBAN(iban) {
        // Handle empty input
        if (!iban) return false;

        // Clean and normalize the IBAN
        iban = iban.replace(/\s+/g, '').toUpperCase();

        // Basic length check
        if (iban.length < 15 || iban.length > 34) return false;

        // Country-specific length checks
        if (iban.startsWith('TR') && iban.length !== 26) {
            return false;
        }

        // Check that the first two characters are letters
        if (!/^[A-Z]{2}/.test(iban)) {
            return false;
        }

        // Check that characters 3 and 4 are digits
        if (!/^[A-Z]{2}[0-9]{2}/.test(iban)) {
            return false;
        }

        try {
            // Rearrange the IBAN: move the first 4 characters to the end
            const rearranged = iban.slice(4) + iban.slice(0, 4);

            // Convert letters to numbers (A=10, B=11, ..., Z=35)
            const converted = rearranged.replace(/[A-Z]/g, ch => (ch.charCodeAt(0) - 55).toString());

            // Process the string in chunks to avoid integer overflow
            let remainder = '';
            for (let i = 0; i < converted.length; i += 7) {
                remainder = remainder + converted.substring(i, i + 7);
                remainder = (parseInt(remainder, 10) % 97).toString();
            }

            // The IBAN is valid if the final remainder is 1
            return parseInt(remainder, 10) === 1;
        } catch (e) {
            console.error('Error validating IBAN:', e);
            return false;
        }
    }

    // Add validation rule to the form
    $.validator.addMethod("validIban", function(value, element) {
        // Skip validation if empty
        if (!value) return true;

        return isValidIBAN(value);
    }, "");

    // Format IBAN with spaces for better readability
    $("#ViewModelItem_Iban").on("input", function() {
        var iban = $(this).val().replace(/\s/g, '').toUpperCase();
        var formattedIban = '';

        // Format with spaces every 4 characters
        for (var i = 0; i < iban.length; i++) {
            if (i > 0 && i % 4 === 0) {
                formattedIban += ' ';
            }
            formattedIban += iban[i];
        }

        $(this).val(formattedIban);
    });

    // Format IBAN on page load
    $(document).ready(function() {
        var iban = $("#ViewModelItem_Iban").val();
        if (iban) {
            // Trigger the input event to format the IBAN
            $("#ViewModelItem_Iban").trigger("input");
        }
    });

    // Add validation rule to the form
    $("form").validate({
        rules: {
            "ViewModelItem.Iban": {
                validIban: true
            }
        }
    });
});
