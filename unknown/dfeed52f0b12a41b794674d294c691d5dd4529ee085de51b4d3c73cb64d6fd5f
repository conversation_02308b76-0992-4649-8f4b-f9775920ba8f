using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels;

namespace RazeWinComTr.Areas.Admin.Pages.Withdrawal;

public class EditModel : PageModel
{
    private readonly WithdrawalService _withdrawalService;
    private readonly IWalletService _walletService;
    private readonly AppDbContext _context;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public EditModel(
        WithdrawalService withdrawalService,
        IWalletService walletService,
        AppDbContext context,
        IStringLocalizer<SharedResource> localizer)
    {
        _withdrawalService = withdrawalService;
        _walletService = walletService;
        _context = context;
        _localizer = localizer;
    }

    public SweetAlert2Message? AlertMessage { get; set; }

    [BindProperty] public WithdrawalEditViewModel Entity { get; set; } = new();

    public string UserEmail { get; set; } = string.Empty;
    public string BankName { get; set; } = string.Empty;
    public decimal UserBalance { get; set; }
    public DateTime CreatedDate { get; set; }

    public string? ErrorMessage { get; set; }

    public async Task<IActionResult> OnGetAsync(int id)
    {
        var entity = await _withdrawalService.GetByIdAsync(id);

        if (entity == null) return NotFound();

        // Check if withdrawal is already approved or rejected
        if (entity.Status == WithdrawalStatus.Approved || entity.Status == WithdrawalStatus.Rejected)
        {
            // Use localized message
            TempData["ErrorMessage"] = _localizer["Approved or rejected withdrawals cannot be edited."].Value;
            return RedirectToPage("./Index");
        }

        Entity = new WithdrawalEditViewModel
        {
            Id = entity.Id,
            UserId = entity.UserId,
            AccountHolder = entity.AccountHolder,
            Iban = entity.Iban,
            WithdrawalAmount = entity.WithdrawalAmount,
            Status = (int)entity.Status,
            PreviousStatus = (int)entity.Status
        };

        var user = await _context.Users.FirstOrDefaultAsync(u => u.UserId == entity.UserId);
        UserEmail = user?.Email ?? "Unknown";
        UserBalance = user?.Balance ?? 0;
        CreatedDate = entity.CreatedDate;

        BankName = $"{entity.AccountHolder} - {entity.Iban}";

        return Page();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        try
        {
            if (!ModelState.IsValid) return Page();
            var userId = User.GetClaimUserId();
            if (!userId.HasValue) return Unauthorized();

            var entity = await _withdrawalService.GetByIdAsync(Entity.Id);
            if (entity == null) return NotFound();

            // Double-check if withdrawal is already approved or rejected (security measure)
            if (entity.Status == WithdrawalStatus.Approved || entity.Status == WithdrawalStatus.Rejected)
            {
                // Use localized message
                TempData["ErrorMessage"] = _localizer["Approved or rejected withdrawals cannot be edited."].Value;
                return RedirectToPage("./Index");
            }

            // Handle status change
            if (Entity.Status != Entity.PreviousStatus)
            {
                try
                {
                    // Use the new service method to update status and record balance transaction
                    bool success = await _withdrawalService.UpdateWithdrawalStatusAsync(Entity.Id, (WithdrawalStatus)Entity.Status);
                    if (!success)
                    {
                        ModelState.AddModelError("", _localizer["Failed to update withdrawal status"]);
                        return Page();
                    }

                    entity.Status = (WithdrawalStatus)Entity.Status;
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("", ex.Message);
                    return Page();
                }
            }

            // If status hasn't changed, just update the other fields
            if (Entity.Status == Entity.PreviousStatus)
            {
                entity.AccountHolder = Entity.AccountHolder;
                entity.Iban = Entity.Iban;
                entity.WithdrawalAmount = Entity.WithdrawalAmount;
                await _withdrawalService.UpdateAsync(entity);
            }
            else
            {
                // Status was already updated in the transaction above
                entity.AccountHolder = Entity.AccountHolder;
                entity.Iban = Entity.Iban;
                entity.WithdrawalAmount = Entity.WithdrawalAmount;
            }

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Success"],
                Text = _localizer["Successfully updated"],
                Icon = "success",
                RedirectUrl = "/Admin/Withdrawal"
            };

            return Page();
        }
        catch (Exception ex)
        {
            ErrorMessage = _localizer["An error occurred: {0}", ex.Message].Value;
            return Page();
        }
    }
}

public class WithdrawalEditViewModel
{
    public int Id { get; set; }
    public int UserId { get; set; }
    public string AccountHolder { get; set; } = string.Empty;
    public string Iban { get; set; } = string.Empty;
    public decimal WithdrawalAmount { get; set; }
    public int Status { get; set; }
    public int PreviousStatus { get; set; }
}
