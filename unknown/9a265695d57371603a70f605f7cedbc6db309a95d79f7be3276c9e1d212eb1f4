@page
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.DbModel
@model EditModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["Edit Payment"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

@if (Model.AlertMessage != null)
{
    <script id="scriptMessage" type="text/javascript">
        window.onload = function () {
            Swal.fire({
                title: '@Html.Raw(Model.AlertMessage.Title)',
                text: '@Html.Raw(Model.AlertMessage.Text)',
                icon: '@Html.Raw(Model.AlertMessage.Icon)'
            }).then((result) => {
                var redirectUrl = '@(Model.AlertMessage.RedirectUrl ?? string.Empty)';
                if (result.isConfirmed && redirectUrl !== '') {
                    location.href = redirectUrl;
                }
            });
            $('#scriptMessage').remove();
        }
    </script>
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["Edit Payment"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="/admin/Deposit">@L["Deposits"]</a></li>
                    <li class="breadcrumb-item active">@L["Edit"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>
<div asp-validation-summary="ModelOnly" class="text-danger"></div>
<section class="content">
    <div class="container-fluid">
        <div class="card">
            <form method="post">
                <input type="hidden" asp-for="Entity.Id"/>
                <input type="hidden" asp-for="Entity.UserId"/>
                <input type="hidden" asp-for="Entity.PreviousStatus"/>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card card-primary">
                                <div class="card-header">
                                    <h3 class="card-title">@L["Payment Information"]</h3>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <label>@L["User"]</label>
                                        <input type="text" class="form-control" value="@Model.UserEmail" readonly />
                                    </div>
                                    <div class="form-group">
                                        <label>@L["User Balance"]</label>
                                        <input type="text" class="form-control" value="@Model.UserBalance.ToString("N2") @L["Currency_Symbol"]" readonly />
                                    </div>
                                    <div class="form-group">
                                        <label>@L["Payment Type"]</label>
                                        <input type="text" class="form-control" value="@Model.DepositType" readonly />
                                    </div>
                                    <div class="form-group">
                                        <label>@L["Created Date"]</label>
                                        <input type="text" class="form-control" value="@Model.CreatedDate.ToLocalTime().ToString("g")" readonly />
                                    </div>

                                    @if (Model.IsCryptoDeposit)
                                    {
                                        <div class="card card-info mt-3 mb-3">
                                            <div class="card-header">
                                                <h3 class="card-title">@L["Cryptocurrency Information"]</h3>
                                            </div>
                                            <div class="card-body">
                                                <div class="form-group">
                                                    <label>@L["Cryptocurrency Type"]</label>
                                                    <input type="text" class="form-control" value="@Model.CryptoType" readonly />
                                                </div>
                                                <div class="form-group">
                                                    <label>@L["Transaction Hash"]</label>
                                                    <input type="text" class="form-control" value="@Model.TransactionHash" readonly />
                                                </div>
                                                @if (!string.IsNullOrEmpty(Model.SenderAddress))
                                                {
                                                    <div class="form-group">
                                                        <label>@L["Sender Address"]</label>
                                                        <input type="text" class="form-control" value="@Model.SenderAddress" readonly />
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    }

                                    @if (!string.IsNullOrEmpty(Model.ExtraData))
                                    {
                                        <div class="form-group">
                                            <label>@L["Extra Data"]</label>
                                            <textarea class="form-control" rows="3" readonly>@Model.ExtraData</textarea>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card card-success">
                                <div class="card-header">
                                    <h3 class="card-title">@L["Edit Payment"]</h3>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <label asp-for="Entity.Amount">
                                            @if (Model.IsCryptoDeposit)
                                            {
                                                @L["Amount ({0})", Model.CoinCode]
                                            }
                                            else
                                            {
                                                @L["Amount (TRY)"]
                                            }
                                        </label>
                                        <input asp-for="Entity.Amount" class="form-control" type="number" step="0.00000001" required/>
                                    </div>
                                    <div class="form-group">
                                        <label asp-for="Entity.Status">@L["Status"]</label>
                                        <select asp-for="Entity.Status" class="form-control" required>
                                            <option value="0">@L["Pending"]</option>
                                            <option value="1">@L["Approved"]</option>
                                            <option value="2">@L["Rejected"]</option>
                                        </select>
                                        <small class="form-text text-muted">
                                            @if (Model.Entity.Status == 0)
                                            {
                                                <span>@L["Current status"]: <span class="badge badge-warning">@L["Pending"]</span></span>
                                            }
                                            else if (Model.Entity.Status == 1)
                                            {
                                                <span>@L["Current status"]: <span class="badge badge-success">@L["Approved"]</span></span>
                                            }
                                            else if (Model.Entity.Status == 2)
                                            {
                                                <span>@L["Current status"]: <span class="badge badge-danger">@L["Rejected"]</span></span>
                                            }
                                        </small>
                                    </div>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        @if (Model.IsCryptoDeposit)
                                        {
                                            @L["Note: When you approve a cryptocurrency deposit, the amount will be added to the user's cryptocurrency wallet automatically."]
                                        }
                                        else
                                        {
                                            @L["Note: When you approve a payment, the amount will be added to the user's balance automatically."]
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-1"></i> @L["Save"]
                    </button>
                    <a href="/Admin/Deposit" class="btn btn-default">
                        <i class="fas fa-times mr-1"></i> @L["Cancel"]
                    </a>
                </div>
            </form>
        </div>
    </div>
</section>
