using System.ComponentModel.DataAnnotations;

namespace RazeWinComTr.Areas.Admin.ViewModels.User;

public class UserViewModel
{
    public int UserId { get; set; }

    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;
    public string FullName { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Surname { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public string IdentityNumber { get; set; } = string.Empty;
    public string? Iban { get; set; }
    public decimal Balance { get; set; }

    public int IsActive { get; set; } = 1;

    public DateTime CrDate { get; set; }
}