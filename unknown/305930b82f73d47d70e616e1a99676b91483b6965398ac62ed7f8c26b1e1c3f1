using Microsoft.AspNetCore.Mvc.RazorPages;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.Wallet;

namespace RazeWinComTr.Areas.Admin.Pages.Wallet;

public class IndexModel : PageModel
{
    private readonly IWalletService _walletService;

    public IndexModel(IWalletService walletService)
    {
        _walletService = walletService;
    }

    public List<WalletViewModel> Wallets { get; set; } = new();

    public async Task OnGetAsync()
    {
        Wallets = await _walletService.GetListAsync();
    }
}
