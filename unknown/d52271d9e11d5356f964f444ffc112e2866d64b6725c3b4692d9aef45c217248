using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using RazeWinComTr.Areas.Admin.Services;
using System;
using System.Threading.Tasks;

namespace RazeWinComTr.Middleware
{
    public class MaintenanceModeMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IServiceScopeFactory _serviceScopeFactory;

        public MaintenanceModeMiddleware(RequestDelegate next, IServiceScopeFactory serviceScopeFactory)
        {
            _next = next;
            _serviceScopeFactory = serviceScopeFactory;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Her istek için yeni bir scope oluştur
            using var scope = _serviceScopeFactory.CreateScope();
            var settingService = scope.ServiceProvider.GetRequiredService<SettingService>();

            var maintenanceMode = await settingService.GetIntSettingAsync("maintenance_mode", 0);

            // Admin alanına erişim kontrolü
            bool isAdminArea = context.Request.Path.StartsWithSegments("/admin", StringComparison.OrdinalIgnoreCase);
            bool isAuthenticated = context.User.Identity?.IsAuthenticated ?? false;

            if (maintenanceMode == 1 && !isAdminArea && !isAuthenticated)
            {
                context.Response.StatusCode = 503; // Service Unavailable
                context.Response.ContentType = "text/html";
                await context.Response.WriteAsync("<html><head><title>Bakım Modu</title></head><body>");
                await context.Response.WriteAsync("<h1>Site bakım modundadır</h1>");
                await context.Response.WriteAsync("<p>Lütfen daha sonra tekrar deneyiniz.</p>");
                await context.Response.WriteAsync("</body></html>");
                return;
            }

            await _next(context);
        }
    }

    // Extension method for middleware
    public static class MaintenanceModeMiddlewareExtensions
    {
        public static IApplicationBuilder UseMaintenanceMode(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<MaintenanceModeMiddleware>();
        }
    }
}
