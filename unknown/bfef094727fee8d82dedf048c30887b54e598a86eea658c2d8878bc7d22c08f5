using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels.Bank;
using System.ComponentModel.DataAnnotations;
using System.Web;

namespace RazeWinComTr.Areas.MyAccount.Pages
{
    public class DepositBankTransferModel : PageModel
    {
        private readonly SettingService _settingService;
        private readonly IStringLocalizer<SharedResource> _localizer;
        private readonly AppDbContext _context;
        private readonly BankService _bankService;
        private readonly DepositService _depositService;

        public DepositBankTransferModel(
            SettingService settingService,
            IStringLocalizer<SharedResource> localizer,
            AppDbContext context,
            BankService bankService,
            DepositService depositService)
        {
            _settingService = settingService;
            _localizer = localizer;
            _context = context;
            _bankService = bankService;
            _depositService = depositService;
        }

        [BindProperty]
        public BankTransferDepositViewModel DepositInfo { get; set; } = new();

        public List<BankViewModel> Banks { get; set; } = new();
        public string? AlertMessage { get; set; }
        public string? AlertType { get; set; }
        public int? CurrentUserId { get; set; }
        public string? UserFullName { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            var userId = User.GetClaimUserId();
            if (!userId.HasValue)
            {
                return Unauthorized();
            }

            CurrentUserId = userId.Value;

            // Get user information
            var user = await _context.Users.FirstOrDefaultAsync(u => u.UserId == userId);
            if (user == null)
            {
                return NotFound();
            }

            UserFullName = $"{user.Name} {user.Surname}";

            // Pre-fill the form with user data
            DepositInfo.FullName = UserFullName;
            DepositInfo.Phone = user.PhoneNumber ?? string.Empty;

            // Generate default description and decode HTML entities
            DepositInfo.CustomerNote = LocalizerHelper.getDecodedFormat(_localizer,
                "Deposit Transaction - User ID: {0}, User Name: {1}. Transaction Date: {2}",
                userId.Value, UserFullName, DateTime.UtcNow.ToLocalTime().ToString("dd.MM.yyyy"));

            // Get active bank accounts
            Banks = await _bankService.GetActiveListAsync();

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            var userId = User.GetClaimUserId();
            if (!userId.HasValue)
            {
                return Unauthorized();
            }

            if (!ModelState.IsValid)
            {
                await OnGetAsync();
                return Page();
            }

            try
            {
                var user = await _context.Users.FirstOrDefaultAsync(u => u.UserId == userId);
                if (user == null)
                {
                    return NotFound();
                }

                var bank = await _bankService.GetByIdAsync(DepositInfo.BankId);
                if (bank == null)
                {
                    ModelState.AddModelError("DepositInfo.BankId", LocalizerHelper.getDecoded(_localizer, "Selected bank not found."));
                    await OnGetAsync();
                    return Page();
                }

                // Get the combined amount from the integer and fractional parts
                decimal amount = DepositInfo.GetCombinedAmount();

                // Validate Amount
                if (amount <= 0)
                {
                    ModelState.AddModelError("DepositInfo.AmountInteger", LocalizerHelper.getDecoded(_localizer, "Please enter a valid amount"));
                    await OnGetAsync();
                    return Page();
                }

                // Create deposit record
                var deposit = new Deposit
                {
                    UserId = userId.Value,
                    DepositType = LocalizerHelper.getDecoded(_localizer, "Bank Transfer"),
                    Amount = amount,
                    FullName = $"{user.Name} {user.Surname}",
                    ExtraData = LocalizerHelper.getDecodedFormat(_localizer,
                        "Bank: {0}, IBAN: {1}, Transfer Time: {2}, Customer Note: {3}",
                        bank.BankName, bank.Iban, DepositInfo.TransferTime, DepositInfo.CustomerNote),
                    IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? LocalizerHelper.getDecoded(_localizer, "Unknown"),
                    CreatedDate = DateTime.UtcNow,
                    ProcessStatus = LocalizerHelper.getDecoded(_localizer, "Submitted"),
                    Status = DepositStatus.Pending
                };

                // Use the deposit service to create the deposit
                // This ensures it's only added to balance history when approved
                await _depositService.CreateAsync(deposit);

                // Set success message to be displayed with SweetAlert
                AlertMessage = _localizer["Your deposit request has been received. It will be processed as soon as possible."].Value;
                AlertType = "success";

                return Page();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError(string.Empty, LocalizerHelper.getDecodedFormat(_localizer, "An error occurred: {0}", ex.Message));
                await OnGetAsync();
                return Page();
            }
        }
    }

    public class BankTransferDepositViewModel
    {
        [Required]
        [Display(Name = "Sender Full Name")]
        public string FullName { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Phone")]
        public string Phone { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Select Bank")]
        public int BankId { get; set; }

        [Required]
        [Display(Name = "Transfer Time")]
        public string TransferTime { get; set; } = string.Empty;

        [Display(Name = "Description")]
        [StringLength(500)]
        public string CustomerNote { get; set; } = string.Empty;

        // Integer part of the amount
        [Required]
        [Display(Name = "Amount")]
        [Range(0, int.MaxValue)]
        public int AmountInteger { get; set; }

        // Fractional part of the amount (as string to preserve leading zeros)
        [Display(Name = "Amount (Cents)")]
        public string AmountFraction { get; set; } = "00";

        /// <summary>
        /// Gets the combined decimal amount from the integer and fractional parts
        /// </summary>
        /// <returns>The combined decimal amount in invariant culture</returns>
        public decimal GetCombinedAmount()
        {
            // Combine the integer and fractional parts
            string combinedString = $"{AmountInteger}.{AmountFraction}";

            // Parse using invariant culture
            if (decimal.TryParse(combinedString, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out decimal result))
            {
                return result;
            }

            // Fallback to just the integer part if parsing fails
            return AmountInteger;
        }
    }
}
