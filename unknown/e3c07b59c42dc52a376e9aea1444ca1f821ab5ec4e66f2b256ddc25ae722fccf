namespace RazeWinComTr.Areas.Admin.Enums
{
    /// <summary>
    /// Ödeme ödül dağıtım durumu
    /// </summary>
    public enum DepositRewardStatus
    {
        Pending = 0,      // Ödül dağıtımı henüz yapılmadı
        Processed = 1,    // Ödül dağıtım işlemi tamamlandı
        Distributed = 2,  // Ödüller dağıtıldı (en az bir kişiye)
        NoRewards = 3,    // Ödül dağıtılacak kimse bulunamadı
        Failed = 4        // Ödül dağıtım işlemi sırasında hata oluştu
    }
}
