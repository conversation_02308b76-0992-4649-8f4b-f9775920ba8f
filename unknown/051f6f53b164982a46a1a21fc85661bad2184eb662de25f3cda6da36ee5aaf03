using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels.RzwSavings;
using RazeWinComTr.Areas.Admin.DbModel;

namespace RazeWinComTr.Areas.Admin.Pages.RzwSavingsAccounts;

public class DetailsModel : PageModel
{
    private readonly RzwSavingsService _savingsService;
    private readonly RzwSavingsInterestService _interestService;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public DetailsModel(
        RzwSavingsService savingsService,
        RzwSavingsInterestService interestService,
        IStringLocalizer<SharedResource> localizer)
    {
        _savingsService = savingsService;
        _interestService = interestService;
        _localizer = localizer;
    }

    public RzwSavingsAccountViewModel Account { get; set; } = new();
    public List<RzwSavingsInterestPayment> InterestHistory { get; set; } = new();
    public IStringLocalizer<SharedResource> Localizer => _localizer;

    public async Task<IActionResult> OnGetAsync(int? id)
    {
        if (id == null)
        {
            return NotFound();
        }

        var account = await _savingsService.GetAccountDetailsForAdminAsync(id.Value);
        if (account == null)
        {
            return NotFound();
        }

        Account = account;
        InterestHistory = await _interestService.GetInterestHistoryAsync(id.Value);

        return Page();
    }

    public async Task<IActionResult> OnPostForceMaturityAsync(int id)
    {
        try
        {
            var result = await _savingsService.ProcessMaturityAsync(id);
            if (result.Success)
            {
                TempData["SuccessMessage"] = _localizer["Account maturity processed successfully"].Value;
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }
        }
        catch (Exception ex)
        {
            TempData["ErrorMessage"] = _localizer["An error occurred while processing maturity"].Value + ": " + ex.Message;
        }

        return RedirectToPage("./Details", new { id });
    }

    public async Task<IActionResult> OnPostEmergencyCloseAsync(int id)
    {
        try
        {
            var result = await _savingsService.EarlyWithdrawAsync(id, 0); // Admin withdrawal
            if (result.Success)
            {
                TempData["SuccessMessage"] = _localizer["Account closed successfully"].Value;
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }
        }
        catch (Exception ex)
        {
            TempData["ErrorMessage"] = _localizer["An error occurred while closing the account"].Value + ": " + ex.Message;
        }

        return RedirectToPage("./Details", new { id });
    }
}
