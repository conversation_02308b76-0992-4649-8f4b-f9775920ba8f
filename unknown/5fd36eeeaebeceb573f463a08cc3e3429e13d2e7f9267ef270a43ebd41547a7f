@page
@model RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.HistoryModel
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<SharedResource> Localizer
@{
    ViewData["Title"] = Localizer["Savings History"];
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@Localizer["Savings History"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/MyAccount">@Localizer["Dashboard"]</a></li>
                    <li class="breadcrumb-item"><a href="/MyAccount/RzwSavings">@Localizer["RZW Savings"]</a></li>
                    <li class="breadcrumb-item active">@Localizer["Savings History"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        
        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-6">
                <div class="small-box bg-info">
                    <div class="inner">
                        <h3>@Model.Summary.TotalAccounts</h3>
                        <p>@Localizer["Total Accounts"]</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-piggy-bank"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3>@Model.Summary.ActiveAccounts</h3>
                        <p>@Localizer["Active"]</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-warning">
                    <div class="inner">
                        <h3>@Model.Summary.TotalInvested.ToString("N0")</h3>
                        <p>@Localizer["Total Invested"] (RZW)</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-coins"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-danger">
                    <div class="inner">
                        <h3>@Model.Summary.TotalEarned.ToString("N2")</h3>
                        <p>@Localizer["Total Earned"] (RZW)</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">@Localizer["Filters"]</h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <form method="get" class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="StatusFilter">@Localizer["Status"]</label>
                            <select asp-for="StatusFilter" class="form-control">
                                <option value="">@Localizer["All Statuses"]</option>
                                <option value="Active">@Localizer["Active"]</option>
                                <option value="Matured">@Localizer["Matured"]</option>
                                <option value="Withdrawn">@Localizer["Withdrawn"]</option>
                                <option value="Cancelled">@Localizer["Cancelled"]</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="FromDate">@Localizer["From Date"]</label>
                            <input asp-for="FromDate" type="date" class="form-control" />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="ToDate">@Localizer["To Date"]</label>
                            <input asp-for="ToDate" type="date" class="form-control" />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> @Localizer["Filter"]
                                </button>
                                <a href="/MyAccount/RzwSavings/History" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> @Localizer["Clear"]
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Accounts Table -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">@Localizer["All Savings Accounts"]</h3>
                <div class="card-tools">
                    <a href="/MyAccount/RzwSavings" class="btn btn-primary btn-sm">
                        <i class="fas fa-arrow-left"></i> @Localizer["Back to Dashboard"]
                    </a>
                </div>
            </div>
            <div class="card-body table-responsive p-0">
                @if (Model.AllAccounts.Any())
                {
                    <table class="table table-hover text-nowrap">
                        <thead>
                            <tr>
                                <th>@Localizer["Plan"]</th>
                                <th>@Localizer["Amount"]</th>
                                <th>@Localizer["Term"]</th>
                                <th>@Localizer["Start Date"]</th>
                                <th>@Localizer["Maturity Date"]</th>
                                <th>@Localizer["Status"]</th>
                                <th>@Localizer["Total Earned"]</th>
                                <th>@Localizer["Actions"]</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var account in Model.AllAccounts)
                            {
                                <tr>
                                    <td>
                                        <strong>@account.PlanName</strong><br>
                                        <small class="text-muted">@account.TermDisplayText</small>
                                    </td>
                                    <td>
                                        <strong>@account.RzwAmount.ToString("N8") RZW</strong><br>
                                        <small class="text-muted">@((account.InterestRate * 100).ToString("N2"))% @Localizer["Daily"]</small>
                                    </td>
                                    <td>@account.TermDisplayText</td>
                                    <td>@account.StartDate.ToString("dd.MM.yyyy")</td>
                                    <td>@account.MaturityDate.ToString("dd.MM.yyyy")</td>
                                    <td>
                                        <span class="badge @account.StatusBadgeClass">
                                            @Localizer[account.Status]
                                        </span>
                                    </td>
                                    <td>
                                        <strong class="text-success">@account.TotalEarnedRzw.ToString("N8") RZW</strong>
                                    </td>
                                    <td>
                                        <a href="/MyAccount/RzwSavings/Details?id=@account.Id" class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i> @Localizer["Details"]
                                        </a>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                }
                else
                {
                    <div class="text-center p-4">
                        <i class="fas fa-piggy-bank fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">@Localizer["No savings accounts found"]</h5>
                        <p class="text-muted">@Localizer["You haven't created any savings accounts yet."]</p>
                        <a href="/MyAccount/RzwSavings/Create" class="btn btn-primary">
                            <i class="fas fa-plus"></i> @Localizer["Create Your First Account"]
                        </a>
                    </div>
                }
            </div>
        </div>

        <!-- Pagination -->
        @if (Model.TotalPages > 1)
        {
            <div class="card">
                <div class="card-body">
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            @if (Model.HasPreviousPage)
                            {
                                <li class="page-item">
                                    <a class="page-link" href="?PageNumber=@(Model.PageNumber - 1)&StatusFilter=@Model.StatusFilter&FromDate=@Model.FromDate&ToDate=@Model.ToDate">
                                        @Localizer["Previous"]
                                    </a>
                                </li>
                            }
                            
                            @for (int i = Math.Max(1, Model.PageNumber - 2); i <= Math.Min(Model.TotalPages, Model.PageNumber + 2); i++)
                            {
                                <li class="page-item @(i == Model.PageNumber ? "active" : "")">
                                    <a class="page-link" href="?PageNumber=@i&StatusFilter=@Model.StatusFilter&FromDate=@Model.FromDate&ToDate=@Model.ToDate">@i</a>
                                </li>
                            }
                            
                            @if (Model.HasNextPage)
                            {
                                <li class="page-item">
                                    <a class="page-link" href="?PageNumber=@(Model.PageNumber + 1)&StatusFilter=@Model.StatusFilter&FromDate=@Model.FromDate&ToDate=@Model.ToDate">
                                        @Localizer["Next"]
                                    </a>
                                </li>
                            }
                        </ul>
                    </nav>
                </div>
            </div>
        }

    </div>
</section>
