@page
@model RazeWinComTr.Areas.Admin.Pages.ReferralTree.IndexModel
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.Helpers
@inject IStringLocalizer<SharedResource> Localizer
@{
    ViewData["Title"] = Localizer["Referral Tree"];
    Layout = "_LayoutAdminLte";
}

@section Styles {
    <!-- Select2 -->
    <link href="~/plugins/select2/css/select2.min.css" rel="stylesheet" />
    <link href="~/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css" rel="stylesheet" />
    <!-- Page specific styles -->
    <link rel="stylesheet" href="~/site/pages/admin/referraltree/referraltree.css" />
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@Localizer["Referral Tree"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/Admin">@Localizer["Home"]</a></li>
                    <li class="breadcrumb-item active">@Localizer["Referral Tree"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">@Localizer["Search Users"]</h3>
            </div>
            <div class="card-body">
                <form method="get" class="mb-4">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" asp-for="SearchTerm" placeholder="@Localizer["Search by name, email, phone or referral code"]">
                                <div class="input-group-append">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> @Localizer["Search"]
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            @if (Model.Users.Any())
                            {
                                <div class="form-group">
                                    <select asp-for="SelectedUserId" class="form-control select2" onchange="this.form.submit()">
                                        <option value="">@Localizer["Select User"]</option>
                                        @foreach (var user in Model.Users)
                                        {
                                            <option value="@user.UserId" selected="@(Model.SelectedUserId == user.UserId)">
                                                @user.Name @user.Surname (@user.Email)
                                            </option>
                                        }
                                    </select>
                                </div>
                            }
                        </div>
                    </div>
                </form>

                @if (Model.SelectedUser != null)
                {
                    <div class="user-details mb-4">
                        <div class="card">
                            <div class="card-header bg-primary">
                                <h3 class="card-title">@Localizer["Selected User"]</h3>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <dl class="row">
                                            <dt class="col-sm-4">@Localizer["Name"]:</dt>
                                            <dd class="col-sm-8">@Model.SelectedUser.Name @Model.SelectedUser.Surname</dd>

                                            <dt class="col-sm-4">@Localizer["Email"]:</dt>
                                            <dd class="col-sm-8">@Model.SelectedUser.Email</dd>

                                            <dt class="col-sm-4">@Localizer["Phone"]:</dt>
                                            <dd class="col-sm-8">@Model.SelectedUser.PhoneNumber</dd>

                                            <dt class="col-sm-4">@Localizer["Package"]:</dt>
                                            <dd class="col-sm-8">
                                                @if (Model.ActivePackage != null)
                                                {
                                                    <span class="badge badge-success">@Model.ActivePackage.Name</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">@Localizer["None"]</span>
                                                }
                                            </dd>
                                        </dl>
                                    </div>
                                    <div class="col-md-6">
                                        <dl class="row">
                                            <dt class="col-sm-4">@Localizer["Registration Date"]:</dt>
                                            <dd class="col-sm-8">@Model.SelectedUser.CrDate.ToLocalTime().ToString("dd.MM.yyyy")</dd>

                                            <dt class="col-sm-4">@Localizer["Referral Code"]:</dt>
                                            <dd class="col-sm-8">
                                                <span id="referralCode">@Model.SelectedUser.ReferralCode</span>
                                                <button type="button" class="btn btn-sm btn-outline-primary ml-2" onclick="copyToClipboard('referralCode')">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </dd>

                                            <dt class="col-sm-4">@Localizer["Referred By"]:</dt>
                                            <dd class="col-sm-8">
                                                @if (Model.SelectedUser.ReferrerId.HasValue)
                                                {
                                                    <a href="?SelectedUserId=@Model.SelectedUser.ReferrerId" class="btn btn-sm btn-outline-info">
                                                        <i class="fas fa-user"></i> @Localizer["View Referrer"]
                                                    </a>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">@Localizer["None"]</span>
                                                }
                                            </dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>

        @if (Model.SelectedUser != null)
        {
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">@Localizer["Referral Hierarchy"] - @Localizer["Who Referred Whom"]</h3>
                </div>
                <div class="card-body">
                    @if (Model.ReferralHierarchy.Any())
                    {
                        <div class="mb-3">
                            <p class="text-muted">@Localizer["This tree shows the users referred by"] @Model.SelectedUser.Name @Model.SelectedUser.Surname @Localizer["and their referrals."]</p>
                        </div>
                        <div class="referral-tree">
                            <ul class="tree">
                                @foreach (var item in Model.ReferralHierarchy)
                                {
                                    @await Html.PartialAsync("_ReferralTreeItem", item, new ViewDataDictionary(ViewData) { { "ShowSearchButton", true } })
                                }
                            </ul>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-info">
                            @Localizer["This user hasn't referred any users yet."]
                        </div>
                    }
                </div>
            </div>
        }
    </div>
</section>

@section Scripts {
    <!-- Select2 -->
    <script src="~/plugins/select2/js/select2.full.min.js"></script>

    <script>
        // Extend the global translations object with page-specific translations
        window.t = window.t || {};

        // Extend with referral tree page specific translations
        Object.assign(window.t, {
            "Copied": "@Html.Raw(Localizer["Copied"])",
            "Element not found": "@Html.Raw(Localizer["Element not found"])",
            "Failed to copy text": "@Html.Raw(Localizer["Failed to copy text"])",
            "Select User": "@Html.Raw(Localizer["Select User"])"
        });
    </script>
    <script src="~/site/pages/admin/referraltree/referraltree.js"></script>
}


