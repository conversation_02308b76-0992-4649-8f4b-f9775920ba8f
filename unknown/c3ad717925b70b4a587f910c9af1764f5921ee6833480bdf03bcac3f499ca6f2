﻿using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Resources;

namespace RazeWinComTr.Attributes;

/// <summary>
/// Provides localized string comparison validation, similar to CompareAttribute 
/// but with culture and localization support.
/// </summary>
[AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter, AllowMultiple = false)]
public class LocalizedCompareAttribute : ValidationAttribute
{
    private static readonly ResourceManager ResourceManager = new ResourceManager("RazeWinComTr.Areas.Admin.Resources.SharedResource", typeof(LocalizedMinLengthAttribute).Assembly);

    /// <summary>
    /// Gets the property to compare with.
    /// </summary>
    public string OtherProperty { get; }

    /// <summary>
    /// Gets or sets the comparison type to use.
    /// </summary>
    public ComparisonType ComparisonType { get; set; } = ComparisonType.Equal;

    /// <summary>
    /// Gets or sets whether the comparison should be case-sensitive.
    /// </summary>
    public bool IgnoreCase { get; set; } = false;

    /// <summary>
    /// Gets or sets the culture to use for string comparisons.
    /// If null, CultureInfo.CurrentCulture will be used.
    /// </summary>
    public string? CultureName { get; set; }

    /// <summary>
    /// Gets or sets whether to ignore whitespace in the comparison.
    /// </summary>
    public bool IgnoreWhitespace { get; set; } = false;


    /// <summary>
    /// Initializes a new instance of the LocalizedCompareAttribute.
    /// </summary>
    /// <param name="otherProperty">The property to compare with.</param>
    public LocalizedCompareAttribute(string otherProperty) : base()
    {
        OtherProperty = otherProperty ?? throw new ArgumentNullException(nameof(otherProperty));
    }

    /// <summary>
    /// Formats the error message to be displayed.
    /// </summary>
    public override string FormatErrorMessage(string name)
    {
        string? template = ResourceManager.GetString("Fields do not match", CultureInfo.CurrentUICulture);

        if (template == null)
        {
            throw new InvalidOperationException("The resource string 'Fields do not match' could not be found.");
        }

        return string.Format(CultureInfo.CurrentCulture, template, name, OtherProperty);
    }

    /// <summary>
    /// Validates the property value against the comparison property.
    /// </summary>
    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        // Get the property info for the other property
        var otherPropertyInfo = validationContext.ObjectType.GetProperty(OtherProperty);
        if (otherPropertyInfo == null)
        {
            throw new ArgumentException($"Property {OtherProperty} not found on type {validationContext.ObjectType.FullName}");
        }

        // Get the other property value
        var otherValue = otherPropertyInfo.GetValue(validationContext.ObjectInstance);

        // If both values are null, they're equal
        if (value == null && otherValue == null)
        {
            if (ComparisonType == ComparisonType.Equal || ComparisonType == ComparisonType.GreaterThanOrEqual || ComparisonType == ComparisonType.LessThanOrEqual)
            {
                return ValidationResult.Success;
            }
            else
            {
                return new ValidationResult(FormatErrorMessage(validationContext.DisplayName));
            }
        }

        // If only one value is null, they're not equal
        if (value == null || otherValue == null)
        {
            if (ComparisonType == ComparisonType.NotEqual)
            {
                return ValidationResult.Success;
            }
            else
            {
                return new ValidationResult(FormatErrorMessage(validationContext.DisplayName));
            }
        }

        // If both values are strings, perform string comparison with culture
        if (value is string stringValue && otherValue is string otherStringValue)
        {
            // Apply whitespace handling if specified
            if (IgnoreWhitespace)
            {
                stringValue = stringValue.Trim();
                otherStringValue = otherStringValue.Trim();
            }

            // Get the culture to use for comparison
            var culture = string.IsNullOrEmpty(CultureName)
                ? CultureInfo.CurrentCulture
                : CultureInfo.GetCultureInfo(CultureName);

            // Perform the comparison based on specified options
            var result = string.Compare(stringValue, otherStringValue, IgnoreCase, culture);

            bool isValid = ComparisonType switch
            {
                ComparisonType.Equal => result == 0,
                ComparisonType.NotEqual => result != 0,
                ComparisonType.GreaterThan => result > 0,
                ComparisonType.GreaterThanOrEqual => result >= 0,
                ComparisonType.LessThan => result < 0,
                ComparisonType.LessThanOrEqual => result <= 0,
                _ => result == 0
            };

            return isValid ? ValidationResult.Success : new ValidationResult(FormatErrorMessage(validationContext.DisplayName));
        }

        // For non-string values, use IComparable if available
        if (value is IComparable comparable && otherValue is IComparable)
        {
            try
            {
                var result = comparable.CompareTo(otherValue);

                bool isValid = ComparisonType switch
                {
                    ComparisonType.Equal => result == 0,
                    ComparisonType.NotEqual => result != 0,
                    ComparisonType.GreaterThan => result > 0,
                    ComparisonType.GreaterThanOrEqual => result >= 0,
                    ComparisonType.LessThan => result < 0,
                    ComparisonType.LessThanOrEqual => result <= 0,
                    _ => result == 0
                };

                return isValid ? ValidationResult.Success : new ValidationResult(FormatErrorMessage(validationContext.DisplayName));
            }
            catch (ArgumentException)
            {
                // Types are not comparable
                return new ValidationResult($"Cannot compare values of types {value.GetType().Name} and {otherValue.GetType().Name}");
            }
        }

        // If both values are the same type, use Equals
        if (value.GetType() == otherValue.GetType())
        {
            bool areEqual = value.Equals(otherValue);

            bool isValid = ComparisonType switch
            {
                ComparisonType.Equal => areEqual,
                ComparisonType.NotEqual => !areEqual,
                _ => throw new InvalidOperationException($"Comparison type {ComparisonType} is not supported for non-comparable types")
            };

            return isValid ? ValidationResult.Success : new ValidationResult(FormatErrorMessage(validationContext.DisplayName));
        }

        // If we get here, the values are not comparable
        return new ValidationResult($"Cannot compare values of types {value.GetType().Name} and {otherValue.GetType().Name}");
    }
}

/// <summary>
/// Defines the type of comparison to perform.
/// </summary>
public enum ComparisonType
{
    /// <summary>
    /// Require the values to be equal.
    /// </summary>
    Equal,

    /// <summary>
    /// Require the values to be not equal.
    /// </summary>
    NotEqual,

    /// <summary>
    /// Require the value to be greater than the comparison value.
    /// </summary>
    GreaterThan,

    /// <summary>
    /// Require the value to be greater than or equal to the comparison value.
    /// </summary>
    GreaterThanOrEqual,

    /// <summary>
    /// Require the value to be less than the comparison value.
    /// </summary>
    LessThan,

    /// <summary>
    /// Require the value to be less than or equal to the comparison value.
    /// </summary>
    LessThanOrEqual
}