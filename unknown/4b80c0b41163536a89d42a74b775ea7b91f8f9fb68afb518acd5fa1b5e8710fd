using RazeWinComTr.Areas.Admin.DbModel;

namespace RazeWinComTr.Areas.Admin.ViewModels.ReferralReward
{
    public class ReferralRewardDetailsViewModel
    {
        public int? ReferralRewardId { get; set; }
        public string? ReferredUserEmail { get; set; }
        public string? ReferredUserFullName { get; set; }
        public int? ReferralLevel { get; set; }
        public decimal? ReferralRzwAmount { get; set; }
        public decimal? ReferralTlAmount { get; set; }
        public decimal? ReferralRzwPercentage { get; set; }
        public decimal? ReferralTlPercentage { get; set; }
        public string? ReferralPackageName { get; set; }
        public decimal? ReferralDepositAmount { get; set; }
        public DateTime? ReferralDepositDate { get; set; }

        // Helper property to get formatted referral reward description
        public string? GetReferralRewardDescription()
        {
            if (!ReferralRewardId.HasValue) return null;

            var parts = new List<string>();
            parts.Add($"Seviye {ReferralLevel} Referans Ödülü");
            parts.Add($"Referans Edilen: {ReferredUserFullName} ({ReferredUserEmail})");
            parts.Add($"Paket: {ReferralPackageName}");
            parts.Add($"Yatırım: {ReferralDepositAmount:N2} TL");
            parts.Add($"Ödül: {ReferralTlAmount:N2} TL + {ReferralRzwAmount:N8} RZW");
            parts.Add($"Oran: %{ReferralTlPercentage:N2} TL + %{ReferralRzwPercentage:N2} RZW");

            return string.Join(" | ", parts);
        }

        // Factory method to create from ReferralReward entity
        public static ReferralRewardDetailsViewModel? FromEntity(DbModel.ReferralReward? reward)
        {
            if (reward == null) return null;

            return new ReferralRewardDetailsViewModel
            {
                ReferralRewardId = reward.Id,
                ReferredUserEmail = reward.ReferredUser?.Email,
                ReferredUserFullName = reward.ReferredUser != null ? $"{reward.ReferredUser.Name} {reward.ReferredUser.Surname}" : null,
                ReferralLevel = reward.Level,
                ReferralRzwAmount = reward.RzwAmount,
                ReferralTlAmount = reward.TlAmount,
                ReferralRzwPercentage = reward.RzwPercentage,
                ReferralTlPercentage = reward.TlPercentage,
                ReferralPackageName = reward.Package?.Name,
                ReferralDepositAmount = reward.DepositAmount,
                ReferralDepositDate = reward.CreatedDate
            };
        }
    }
} 