using RazeWinComTr.Areas.Admin.Constants;
using RazeWinComTr.Areas.Admin.Services.Interfaces;

namespace RazeWinComTr.Areas.Admin.Services
{
    public class CryptoExchangeServiceFactory
    {
        private readonly BitexenService _bitexenService;
        private readonly BtcTurkService _btcTurkService;

        public CryptoExchangeServiceFactory(BitexenService bitexenService, BtcTurkService btcTurkService)
        {
            _bitexenService = bitexenService;
            _btcTurkService = btcTurkService;
        }

        public ICryptoExchangeService? GetService(string? serviceName)
        {
            if (string.IsNullOrEmpty(serviceName))
            {
                return null; // Don't default to any service if not specified
            }

            if (serviceName.ToLower() == ApiServiceNames.BTCTurk.ToLower())
            {
                return _btcTurkService;
            }

            if (serviceName.ToLower() == ApiServiceNames.Bitexen.ToLower())
            {
                return _bitexenService;
            }

            return null; // Return null for unknown service names
        }
    }
}
