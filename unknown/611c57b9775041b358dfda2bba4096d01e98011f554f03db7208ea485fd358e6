@page
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.DbModel
@model IndexModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["Withdrawals"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["Withdrawals"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item active">@L["Withdrawals"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        @if (TempData["ErrorMessage"] != null)
        {
            <div class="alert alert-danger alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h5><i class="icon fas fa-ban"></i> @L["Error"]!</h5>
                @(TempData["ErrorMessage"]?.ToString() ?? string.Empty)
            </div>
        }
        <div class="card">
            <div class="card-header">
                <a id="btnCreateNew" asp-page="Create" class="btn btn-success" role="button"
                   aria-label="@($"{L["Create a New"]} {L["Withdrawal"]}")" title="@($"{L["Create a New"]} {L["Withdrawal"]}")" >
                    <i class="fas fa-plus fa-2x"></i>
                </a>
                <h3 class="card-title float-right">@L["Count"]: @(Model.Withdrawals.Count)</h3>
            </div>
            <div class="card-body">
                <table class="table table-bordered table-striped datatable">
                    <thead>
                    <tr>
                        <th>@L["User"]</th>
                        <th>@L["Bank Account"]</th>
                        <th>@L["Amount"]</th>
                        <th>@L["Status"]</th>
                        <th>@L["Created Date"]</th>

                        <th style="width: 150px">@L["Actions"]</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach (var item in Model.Withdrawals)
                    {
                        <tr>
                            <td>@item.UserEmail</td>
                            <td>@item.AccountHolder - @item.Iban</td>
                                <td>@item.WithdrawalAmount.ToString("N2") @L["Currency_Symbol"]</td>
                            <td>
                                @if (item.Status == WithdrawalStatus.Pending)
                                {
                                    <span class="badge badge-warning">@L["Pending"]</span>
                                }
                                else if (item.Status == WithdrawalStatus.Approved)
                                {
                                    <span class="badge badge-success">@L["Approved"]</span>
                                }
                                else if (item.Status == WithdrawalStatus.Rejected)
                                {
                                    <span class="badge badge-danger">@L["Rejected"]</span>
                                }
                            </td>
                                <td>@item.CreatedDate.ToLocalTime().ToString("g")</td>
                            <td>
                                <a href="/Admin/Withdrawal/Edit?id=@item.Id" class="btn btn-info btn-sm">
                                    <i class="fas fa-pencil-alt"></i>
                                </a>
                            </td>
                        </tr>
                    }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>
