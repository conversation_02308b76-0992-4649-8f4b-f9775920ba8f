using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.ViewModels.Package;

namespace RazeWinComTr.Models
{
    public class PackageDisplayViewModel
    {
        // Package information
        public PackageViewModel Package { get; set; } = null!;

        // RZW token price
        public decimal RzwBuyPrice { get; set; }

        // User package information
        public UserPackageViewModel? CurrentUserPackage { get; set; }
        public int? CurrentPackageOrder { get; set; }

        // Package status flags
        public bool IsCurrentPackage { get; set; }
        public bool IsLowerPackage { get; set; }
        public bool IsHigherPackage { get; set; }
        public bool CanAfford { get; set; } = true;

        // Page context
        public bool IsPublicPackagesPage { get; set; }

        // Reward percentages for different levels
        public List<PackageRewardPercentage> RewardPercentages { get; set; } = new List<PackageRewardPercentage>();

        public bool IsPublicPage
        {
            get => IsPublicPackagesPage;
            set => IsPublicPackagesPage = value;
        }
    }
}
