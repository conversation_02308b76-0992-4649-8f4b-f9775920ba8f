using Microsoft.AspNetCore.Mvc.RazorPages;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.Trade;

namespace RazeWinComTr.Areas.Admin.Pages.Trade;

public class IndexModel : PageModel
{
    private readonly ITradeService _tradeService;

    public IndexModel(ITradeService tradeService)
    {
        _tradeService = tradeService;
    }

    public List<TradeViewModel> Trades { get; set; } = new();

    public async Task OnGetAsync()
    {
        Trades = await _tradeService.GetListAsync();
    }
}
