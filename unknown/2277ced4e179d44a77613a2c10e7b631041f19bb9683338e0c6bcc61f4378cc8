using Microsoft.AspNetCore.Mvc.RazorPages;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels.Package;

namespace RazeWinComTr.Areas.Admin.Pages.Package;

public class IndexModel : PageModel
{
    private readonly PackageService _packageService;

    public IndexModel(PackageService packageService)
    {
        _packageService = packageService;
    }

    public List<PackageViewModel> Packages { get; set; } = new();

    public async Task OnGetAsync()
    {
        Packages = await _packageService.GetListAsync();
    }
}
