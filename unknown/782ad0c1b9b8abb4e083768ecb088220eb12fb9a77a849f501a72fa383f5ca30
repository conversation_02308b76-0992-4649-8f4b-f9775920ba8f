@page
@using Microsoft.Extensions.Localization
@model RazeWinComTr.Areas.Admin.Pages.RzwSavingsPlans.DeleteModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["Delete Plan"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

@if (Model.AlertMessage != null)
{
    <script id="scriptMessage" type="text/javascript">
        window.onload = function () {
            Swal.fire({
                title: '@Html.Raw(Model.AlertMessage.Title)',
                text: '@Html.Raw(Model.AlertMessage.Text)',
                icon: '@Html.Raw(Model.AlertMessage.Icon)'
            }).then((result) => {
                var redirectUrl = '@(Model.AlertMessage.RedirectUrl ?? string.Empty)';
                if (result.isConfirmed && redirectUrl !== '') {
                    location.href = redirectUrl;
                }
            });
            $('#scriptMessage').remove();
        }
    </script>
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["Delete Plan"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="#">@L["RZW Savings"]</a></li>
                    <li class="breadcrumb-item"><a href="/Admin/RzwSavingsPlans">@L["Savings Plans"]</a></li>
                    <li class="breadcrumb-item active">@L["Delete Plan"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">@L["Confirm Deletion"]</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>@L["Warning"]!</strong> @L["Are you sure you want to delete this plan?"]
                </div>

                @if (Model.Plan.TotalAccounts > 0)
                {
                    <div class="alert alert-danger">
                        <i class="fas fa-ban"></i>
                        <strong>@L["Cannot Delete"]!</strong> @L["This plan has active accounts and cannot be deleted."]
                    </div>
                }

                <dl class="row">
                    <dt class="col-sm-3">@L["Name"]</dt>
                    <dd class="col-sm-9">@Model.Plan.Name</dd>

                    <dt class="col-sm-3">@L["Term Duration"]</dt>
                    <dd class="col-sm-9">@Model.Plan.TermDisplayText</dd>

                    <dt class="col-sm-3">@L["Interest Rate"]</dt>
                    <dd class="col-sm-9">@Model.Plan.InterestRateDisplayText</dd>

                    <dt class="col-sm-3">@L["Min Amount"]</dt>
                    <dd class="col-sm-9">@Model.Plan.MinRzwAmount.ToString("N8") RZW</dd>

                    <dt class="col-sm-3">@L["Max Amount"]</dt>
                    <dd class="col-sm-9">@Model.Plan.MaxAmountDisplayText RZW</dd>

                    <dt class="col-sm-3">@L["Status"]</dt>
                    <dd class="col-sm-9">
                        <span class="badge @(Model.Plan.IsActive ? "badge-success" : "badge-danger")">
                            @Model.Plan.StatusDisplayText
                        </span>
                    </dd>

                    <dt class="col-sm-3">@L["Total Accounts"]</dt>
                    <dd class="col-sm-9">@Model.Plan.TotalAccounts</dd>

                    <dt class="col-sm-3">@L["Total Locked RZW"]</dt>
                    <dd class="col-sm-9">@Model.Plan.TotalLockedRzw.ToString("N8") RZW</dd>

                    @if (!string.IsNullOrEmpty(Model.Plan.Description))
                    {
                        <dt class="col-sm-3">@L["Description"]</dt>
                        <dd class="col-sm-9">@Model.Plan.Description</dd>
                    }
                </dl>
            </div>
            <div class="card-footer">
                @if (Model.Plan.TotalAccounts == 0)
                {
                    <form method="post">
                        <button type="submit" class="btn btn-danger">@L["Delete Plan"]</button>
                        <a asp-page="./Index" class="btn btn-secondary">@L["Cancel"]</a>
                    </form>
                }
                else
                {
                    <a asp-page="./Index" class="btn btn-secondary">@L["Back to List"]</a>
                }
            </div>
        </div>
    </div>
</section>
