namespace RazeWinComTr.Areas.Admin.ViewModels.Referral
{
    /// <summary>
    /// Represents an item in the referral hierarchy
    /// </summary>
    public class ReferralHierarchyItem
    {
        /// <summary>
        /// The user in this hierarchy item
        /// </summary>
        public ReferralHierarchyItemUser User { get; set; } = null!;

        /// <summary>
        /// The level in the hierarchy (1 = direct referral, 2 = second level, etc.)
        /// </summary>
        public int Level { get; set; }

        /// <summary>
        /// The active package of the user, if any
        /// </summary>
        public DbModel.Package? ActivePackage { get; set; }

        /// <summary>
        /// The children of this hierarchy item (users referred by this user)
        /// </summary>
        public List<ReferralHierarchyItem> Children { get; set; } = new List<ReferralHierarchyItem>();

        /// <summary>
        /// Total deposits made by this user after their referrer purchased a package
        /// </summary>
        public decimal TotalDeposits { get; set; }

        /// <summary>
        /// Total RZW earnings from this user's deposits
        /// </summary>
        public decimal TotalRzwEarnings { get; set; }
    }

    public class ReferralHierarchyItemUser
    {
        public int UserId { get; set; }
        public string Email { get; set; } = null!;
        public string Name { get; set; } = null!;
        public string Surname { get; set; } = null!;
        public int? ReferrerId { get; set; }
    }
}
