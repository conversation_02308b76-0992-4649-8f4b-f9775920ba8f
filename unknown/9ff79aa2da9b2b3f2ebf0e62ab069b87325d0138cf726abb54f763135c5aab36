/* iOS Date Input Fix */
@supports (-webkit-touch-callout: none) {
    /* iOS specific styles */
    input[type="date"].loginFormText {
        /* Adjust padding for better alignment */
        padding-top: 25px !important;
        padding-right: 10px !important;
        /* Ensure text is properly aligned */
        text-align: left;
        /* Fix for appearance */
        -webkit-appearance: none;
        appearance: none;
        /* Ensure proper height */
        min-height: 70px;
        /* Ensure text doesn't overflow */
        text-overflow: ellipsis;
    }
    
    /* Fix for the calendar icon positioning */
    input[type="date"].loginFormText::-webkit-calendar-picker-indicator {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        margin: 0;
    }
}
