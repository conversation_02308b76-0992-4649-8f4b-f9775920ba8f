﻿﻿using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using RazeWinComTr.Areas.Admin.ViewModels.Common;

namespace RazeWinComTr.Areas.Admin.ViewModels.BTCTurk
{
    public class BTCTurkTickerApiResponse : ITickerResponse
    {
        [JsonPropertyName("data")]
        public List<BTCTurkTickerData> Data { get; set; } = new List<BTCTurkTickerData>();

        [JsonPropertyName("success")]
        public bool Success { get; set; }

        /// <summary>
        /// Implements ITickerResponse.IsSuccess
        /// </summary>
        public bool IsSuccess => Success;

        [JsonPropertyName("message")]
        public string? Message { get; set; }

        [JsonPropertyName("code")]
        public int Code { get; set; }

        /// <summary>
        /// Gets the error message if the request was not successful
        /// </summary>
        public string? GetErrorMessage() => IsSuccess ? null : Message ?? $"Error code: {Code}";

        /// <summary>
        /// Gets the timestamp of when the data was retrieved
        /// </summary>
        public DateTime GetTimestamp()
        {
            // Try to get timestamp from the first ticker item
            if (Data.Count > 0 && Data[0].Timestamp > 0)
            {
                // Convert Unix timestamp (in milliseconds) to DateTime
                return DateTimeOffset.FromUnixTimeMilliseconds(Data[0].Timestamp).DateTime;
            }
            return DateTime.UtcNow; // Fallback to current time if no timestamp is available
        }

        /// <summary>
        /// Gets the buy price (bid) for a specific market pair
        /// </summary>
        public decimal? GetBuyPrice(string marketPair)
        {
            var ticker = Data.FirstOrDefault(t => t.Pair == marketPair);
            return ticker != null ? ticker.Bid : null;
        }

        /// <summary>
        /// Gets the sell price (ask) for a specific market pair
        /// </summary>
        public decimal? GetSellPrice(string marketPair)
        {
            var ticker = Data.FirstOrDefault(t => t.Pair == marketPair);
            return ticker != null ? ticker.Ask : null;
        }

        /// <summary>
        /// Gets the 24-hour price change percentage for a specific market pair
        /// </summary>
        public decimal? GetPriceChangePercentage(string marketPair)
        {
            var ticker = Data.FirstOrDefault(t => t.Pair == marketPair);
            return ticker != null ? ticker.DailyPercent : null;
        }

        /// <summary>
        /// Gets a standardized ticker view model for a specific market pair
        /// </summary>
        public TickerViewModel? GetTickerViewModel(string marketPair)
        {
            var ticker = Data.FirstOrDefault(t => t.Pair == marketPair);
            if (ticker == null)
            {
                return null;
            }

            return new TickerViewModel
            {
                PairIdentifier = ticker.Pair,
                TimestampMs = ticker.Timestamp,
                LastPrice = ticker.Last,
                High24h = ticker.High,
                Low24h = ticker.Low,
                BidPrice = ticker.Bid,
                AskPrice = ticker.Ask,
                Volume24h = ticker.Volume,
                AveragePrice24h = ticker.Average,
                DailyChangePercent = ticker.DailyPercent
            };
        }

        /// <summary>
        /// Gets all available tickers as standardized view models
        /// </summary>
        public Dictionary<string, TickerViewModel> GetAllTickers()
        {
            var result = new Dictionary<string, TickerViewModel>();

            foreach (var ticker in Data)
            {
                var viewModel = GetTickerViewModel(ticker.Pair);
                if (viewModel != null)
                {
                    result[ticker.Pair] = viewModel;
                }
            }

            return result;
        }
    }

    public class BTCTurkTickerData
    {
        [JsonPropertyName("pair")]
        public string Pair { get; set; } = string.Empty;

        [JsonPropertyName("pairNormalized")]
        public string PairNormalized { get; set; } = string.Empty;

        [JsonPropertyName("timestamp")]
        public long Timestamp { get; set; }

        [JsonPropertyName("last")]
        public decimal Last { get; set; }

        [JsonPropertyName("high")]
        public decimal High { get; set; }

        [JsonPropertyName("low")]
        public decimal Low { get; set; }

        [JsonPropertyName("bid")]
        public decimal Bid { get; set; }

        [JsonPropertyName("ask")]
        public decimal Ask { get; set; }

        [JsonPropertyName("open")]
        public decimal Open { get; set; }

        [JsonPropertyName("volume")]
        public decimal Volume { get; set; }

        [JsonPropertyName("average")]
        public decimal Average { get; set; }

        [JsonPropertyName("daily")]
        public decimal Daily { get; set; }

        [JsonPropertyName("dailyPercent")]
        public decimal DailyPercent { get; set; }

        [JsonPropertyName("denominatorSymbol")]
        public string DenominatorSymbol { get; set; } = string.Empty;

        [JsonPropertyName("numeratorSymbol")]
        public string NumeratorSymbol { get; set; } = string.Empty;

        [JsonPropertyName("order")]
        public int Order { get; set; }
    }
}
