using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RazeWinComTr.Areas.Admin.DbModel;

[Table("MARKET")]
public class Market
{
    [Column("ID")]
    public int Id { get; set; }

    [Column("COIN")]
    [Required]
    [StringLength(50)]
    public string Coin { get; set; } = null!;

    [Column("NAME")]
    [Required]
    [StringLength(255)]
    public string Name { get; set; } = null!;

    [Column("SHORT_NAME")]
    [Required]
    [StringLength(255)]
    public string ShortName { get; set; } = null!;

    [Column("BUY_PRICE")]
    public decimal BuyPrice { get; set; }

    [Column("SELL_PRICE")]
    public decimal SellPrice { get; set; }

    [Column("CHANGE_24H")]
    public decimal? Change24h { get; set; }

    [Column("VOLUME_24H")]
    public decimal? Volume24h { get; set; }

    [Column("GENERAL_INCREASE")]
    public decimal GeneralIncrease { get; set; } = 0;

    [Column("DECIMAL_PLACES")]
    public int DecimalPlaces { get; set; } = 8;

    [Column("MIN_BUY")]
    public decimal MinimumBuy { get; set; } = 0;

    [Column("MAX_BUY")]
    public decimal MaximumBuy { get; set; } = decimal.MaxValue;

    [Column("MIN_SELL")]
    public decimal MinimumSell { get; set; } = 0;

    [Column("MAX_SELL")]
    public decimal MaximumSell { get; set; } = 0;

    [Column("ICON_URL")]
    [StringLength(255)]
    public string? IconUrl { get; set; }

    [Column("IS_API")]
    public int IsApi { get; set; } = 0;

    [Column("API_SERVICE_NAME")]
    [StringLength(50)]
    public string? ApiServiceName { get; set; }

    [Column("IS_ACTIVE")]
    public int IsActive { get; set; }

    [Column("ORDER")]
    public int Order { get; set; } = 0;

    [Column("PAIR_CODE")]
    [Required]
    [StringLength(50)]
    public string PairCode { get; set; } = null!;

    [Column("CR_DATE", TypeName = "datetime")]
    public DateTime CrDate { get; set; }

    [Column("MOD_DATE", TypeName = "datetime")]
    public DateTime? ModDate { get; set; }

    [Column("MIN_TRANSACTION_AMOUNT")]
    public decimal? MinimumTransactionAmount { get; set; } = 0;

    [Column("MAX_TRANSACTION_AMOUNT")]
    public decimal? MaximumTransactionAmount { get; set; } = 0;

    [Column("LAST_PRICE_UPDATE", TypeName = "datetime")]
    public DateTime? LastPriceUpdate{ get; set; } = null;
}