using System.Globalization;

namespace RazeWinComTr.Areas.Admin.Helpers
{
    public static class NumberFormatHelper
    {
        /// <summary>
        /// Formats a decimal value with up to 8 decimal places, removing trailing zeros.
        /// </summary>
        /// <param name="value">The decimal value to format</param>
        /// <returns>Formatted string representation of the decimal value</returns>
        public static string FormatDecimal(decimal value)
        {
            // Format with up to 8 decimal places, removing trailing zeros
            string formatted = value.ToString("0.########", CultureInfo.InvariantCulture);
            return formatted;
        }

        /// <summary>
        /// Formats a decimal value with the specified number of decimal places, removing trailing zeros.
        /// </summary>
        /// <param name="value">The decimal value to format</param>
        /// <param name="maxDecimalPlaces">Maximum number of decimal places to show</param>
        /// <returns>Formatted string representation of the decimal value</returns>
        public static string FormatDecimal(decimal value, int maxDecimalPlaces)
        {
            if (maxDecimalPlaces < 0)
                maxDecimalPlaces = 0;

            if (maxDecimalPlaces > 28) // Maximum decimal places for decimal type
                maxDecimalPlaces = 28;

            // Create format string with specified number of decimal places
            string formatString = "0." + new string('#', maxDecimalPlaces);

            // Format with specified decimal places, removing trailing zeros
            string formatted = value.ToString(formatString, CultureInfo.InvariantCulture);
            return formatted;
        }

        /// <summary>
        /// Formats a decimal value with thousands separators and the specified number of decimal places,
        /// using the current culture's formatting rules.
        /// </summary>
        /// <param name="value">The decimal value to format</param>
        /// <param name="maxDecimalPlaces">Maximum number of decimal places to show</param>
        /// <returns>Formatted string representation of the decimal value with thousands separators</returns>
        public static string FormatDecimalWithThousandSeparator(decimal value, int maxDecimalPlaces)
        {
            if (maxDecimalPlaces < 0)
                maxDecimalPlaces = 0;

            if (maxDecimalPlaces > 28) // Maximum decimal places for decimal type
                maxDecimalPlaces = 28;

            // Get the current culture
            var culture = CultureInfo.CurrentCulture;

            // Create format string with specified number of decimal places and thousands separator
            string formatString = "#,0." + new string('#', maxDecimalPlaces);

            // Format with specified decimal places and thousands separator
            string formatted = value.ToString(formatString, culture);
            return formatted;
        }

        /// <summary>
        /// Formats a decimal value with thousands separators and up to 8 decimal places,
        /// using the current culture's formatting rules.
        /// </summary>
        /// <param name="value">The decimal value to format</param>
        /// <returns>Formatted string representation of the decimal value with thousands separators</returns>
        public static string FormatDecimalWithThousandSeparator(decimal value)
        {
            // Get the current culture
            var culture = CultureInfo.CurrentCulture;

            // Format with up to 8 decimal places and thousands separator
            string formatted = value.ToString("#,0.########", culture);
            return formatted;
        }

        /// <summary>
        /// Formats a decimal value with thousands separators and the specified number of decimal places,
        /// using the invariant culture (period as decimal separator, comma as thousands separator).
        /// </summary>
        /// <param name="value">The decimal value to format</param>
        /// <param name="maxDecimalPlaces">Maximum number of decimal places to show</param>
        /// <returns>Formatted string representation of the decimal value with thousands separators</returns>
        public static string FormatDecimalWithThousandSeparatorInvariant(decimal value, int maxDecimalPlaces)
        {
            if (maxDecimalPlaces < 0)
                maxDecimalPlaces = 0;

            if (maxDecimalPlaces > 28) // Maximum decimal places for decimal type
                maxDecimalPlaces = 28;

            // Create format string with specified number of decimal places and thousands separator
            string formatString = "#,0." + new string('#', maxDecimalPlaces);

            // Format with specified decimal places and thousands separator using invariant culture
            string formatted = value.ToString(formatString, CultureInfo.InvariantCulture);
            return formatted;
        }
    }
}
