using Microsoft.AspNetCore.Mvc.RazorPages;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels.Package;

namespace RazeWinComTr.Areas.Admin.Pages.PackageRewardPercentage;

public class IndexModel : PageModel
{
    private readonly PackageRewardPercentageService _percentageService;

    public IndexModel(PackageRewardPercentageService percentageService)
    {
        _percentageService = percentageService;
    }

    public List<PackageRewardPercentageViewModel> Percentages { get; set; } = new();

    public async Task OnGetAsync()
    {
        Percentages = await _percentageService.GetListAsync();
    }
}
