# Adım 5.3.2: Plan Selection Interface (2-3 saat)

## 📋 Adım Özeti
Yeni vadeli hesap sayfasında plan seçimi interface'inin oluşturulması. Interactive plan kartları, kar<PERSON>ılaştırma tablosu ve plan seçim logic'i.

## 🎯 Hedefler
- ✅ Plan seçimi kartları oluşturma
- ✅ Interactive plan selection
- ✅ Plan karşılaştırma tablosu
- ✅ Responsive plan layout
- ✅ Plan recommendation badges

## 📊 Yapılacak İşler

### ✅ YAPILACAKLAR

#### 5.3.2.1 Create.cshtml Plan Selection Section

**Dosya**: `src/Areas/MyAccount/Pages/RzwSavings/Create.cshtml`
```html
@page
@model RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.CreateModel
@{
    ViewData["Title"] = Model.ViewModel.PageTitle;
    Layout = "~/Areas/MyAccount/Pages/Shared/_Layout.cshtml";
}

<div class="create-savings-container">
    <!-- Page Header -->
    <div class="page-header">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-3">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="/MyAccount">
                        <i class="fas fa-home"></i> @Localizer["Dashboard"]
                    </a>
                </li>
                <li class="breadcrumb-item">
                    <a href="/MyAccount/RzwSavings">
                        <i class="fas fa-piggy-bank"></i> @Localizer["RZW Savings"]
                    </a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">
                    <i class="fas fa-plus"></i> @Localizer["Create New"]
                </li>
            </ol>
        </nav>

        <!-- Page Title -->
        <div class="page-title-section">
            <h1 class="page-title">
                <i class="fas fa-plus-circle text-primary"></i>
                @Model.ViewModel.PageTitle
            </h1>
            <p class="page-subtitle">@Model.ViewModel.PageSubtitle</p>
        </div>
    </div>

    <!-- User Balance Summary -->
    <div class="balance-summary-card">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="balance-info">
                    <h6 class="balance-title">
                        <i class="fas fa-wallet text-primary"></i>
                        @Localizer["Your RZW Balance"]
                    </h6>
                    <div class="balance-details">
                        <div class="balance-item">
                            <span class="balance-label">@Localizer["Available"]:</span>
                            <span class="balance-value available" data-balance="available">
                                @Model.ViewModel.UserBalance.AvailableBalance.ToString("N8") RZW
                            </span>
                        </div>
                        @if (Model.ViewModel.UserBalance.LockedBalance > 0)
                        {
                            <div class="balance-item">
                                <span class="balance-label">@Localizer["Locked"]:</span>
                                <span class="balance-value locked">
                                    @Model.ViewModel.UserBalance.LockedBalance.ToString("N8") RZW
                                </span>
                            </div>
                        }
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="balance-actions">
                    @if (!Model.ViewModel.HasSufficientBalance)
                    {
                        <div class="insufficient-balance-warning">
                            <i class="fas fa-exclamation-triangle text-warning"></i>
                            <span>@Localizer["Insufficient balance to create savings account"]</span>
                            <a href="/Market" class="btn btn-sm btn-primary mt-2">
                                @Localizer["Buy RZW"]
                            </a>
                        </div>
                    }
                    else
                    {
                        <div class="balance-status">
                            <i class="fas fa-check-circle text-success"></i>
                            <span>@Localizer["Ready to create savings account"]</span>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    @if (Model.ViewModel.HasSufficientBalance && Model.ViewModel.HasAvailablePlans)
    {
        <form method="post" id="createSavingsForm" class="create-savings-form">
            <!-- Step 1: Plan Selection -->
            <div class="form-step" id="step-plan-selection">
                <div class="step-header">
                    <div class="step-number">
                        <span class="step-badge">1</span>
                    </div>
                    <div class="step-content">
                        <h5 class="step-title">@Localizer["Choose Your Savings Plan"]</h5>
                        <p class="step-description">@Localizer["Select the term that best fits your investment goals"]</p>
                    </div>
                </div>

                <!-- Plan Selection Cards -->
                <div class="plans-grid">
                    @foreach (var plan in Model.ViewModel.AvailablePlans)
                    {
                        <div class="plan-card @plan.PlanCardClass" data-plan-id="@plan.Id">
                            <!-- Plan Header -->
                            <div class="plan-header">
                                <div class="plan-badges">
                                    @if (plan.IsRecommended)
                                    {
                                        <span class="badge badge-recommended">
                                            <i class="fas fa-star"></i> @Localizer["Recommended"]
                                        </span>
                                    }
                                    @if (plan.IsPopular)
                                    {
                                        <span class="badge badge-popular">
                                            <i class="fas fa-fire"></i> @Localizer["Popular"]
                                        </span>
                                    }
                                    <span class="badge @plan.PlanBadgeClass">@plan.TermDisplayText</span>
                                </div>
                                <div class="plan-title">
                                    <h6>@plan.Name</h6>
                                </div>
                            </div>

                            <!-- Plan Content -->
                            <div class="plan-content">
                                <!-- Interest Rate -->
                                <div class="plan-rate">
                                    <div class="rate-primary">
                                        <span class="rate-value">@plan.FormattedInterestRate</span>
                                        <span class="rate-period">@plan.TermType</span>
                                    </div>
                                    <div class="rate-annual">
                                        <small class="text-muted">@plan.FormattedAnnualizedReturn</small>
                                    </div>
                                </div>

                                <!-- Plan Features -->
                                <div class="plan-features">
                                    <div class="feature-item">
                                        <i class="fas fa-coins text-primary"></i>
                                        <span>@Localizer["Min"]: @plan.FormattedMinAmount RZW</span>
                                    </div>
                                    <div class="feature-item">
                                        <i class="fas fa-infinity text-primary"></i>
                                        <span>@Localizer["Max"]: @plan.FormattedMaxAmount</span>
                                    </div>
                                    <div class="feature-item">
                                        <i class="fas fa-calendar-alt text-primary"></i>
                                        <span>@Localizer["Duration"]: @plan.TermDisplayText</span>
                                    </div>
                                    <div class="feature-item">
                                        <i class="fas fa-percentage text-success"></i>
                                        <span>@Localizer["Guaranteed Returns"]</span>
                                    </div>
                                </div>

                                <!-- Plan Description -->
                                @if (!string.IsNullOrEmpty(plan.Description))
                                {
                                    <div class="plan-description">
                                        <small class="text-muted">@plan.Description</small>
                                    </div>
                                }
                            </div>

                            <!-- Plan Footer -->
                            <div class="plan-footer">
                                <button type="button" 
                                        class="btn btn-outline-primary plan-select-btn" 
                                        data-plan-id="@plan.Id"
                                        data-plan-name="@plan.Name"
                                        data-min-amount="@plan.MinRzwAmount"
                                        data-max-amount="@(plan.MaxRzwAmount?.ToString() ?? "")"
                                        onclick="selectPlan(@plan.Id)">
                                    <i class="fas fa-check"></i> @Localizer["Select This Plan"]
                                </button>
                            </div>

                            <!-- Selection Indicator -->
                            <div class="plan-selected-indicator">
                                <i class="fas fa-check-circle"></i>
                                <span>@Localizer["Selected"]</span>
                            </div>
                        </div>
                    }
                </div>

                <!-- Plan Comparison Table (Collapsible) -->
                <div class="plan-comparison-section">
                    <div class="comparison-toggle">
                        <button type="button" 
                                class="btn btn-link" 
                                data-bs-toggle="collapse" 
                                data-bs-target="#planComparison">
                            <i class="fas fa-table"></i> @Localizer["Compare All Plans"]
                            <i class="fas fa-chevron-down ms-2"></i>
                        </button>
                    </div>
                    
                    <div class="collapse" id="planComparison">
                        <div class="comparison-table-container">
                            <table class="table comparison-table">
                                <thead>
                                    <tr>
                                        <th>@Localizer["Plan"]</th>
                                        <th>@Localizer["Term"]</th>
                                        <th>@Localizer["Interest Rate"]</th>
                                        <th>@Localizer["APY"]</th>
                                        <th>@Localizer["Min Amount"]</th>
                                        <th>@Localizer["Max Amount"]</th>
                                        <th>@Localizer["Action"]</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var plan in Model.ViewModel.AvailablePlans)
                                    {
                                        <tr class="comparison-row" data-plan-id="@plan.Id">
                                            <td>
                                                <div class="plan-name-cell">
                                                    <strong>@plan.Name</strong>
                                                    @if (plan.IsRecommended)
                                                    {
                                                        <span class="badge badge-sm badge-recommended ms-1">
                                                            <i class="fas fa-star"></i>
                                                        </span>
                                                    }
                                                </div>
                                            </td>
                                            <td>@plan.TermDisplayText</td>
                                            <td>
                                                <span class="rate-highlight">@plan.FormattedInterestRate</span>
                                            </td>
                                            <td>
                                                <span class="apy-highlight">@plan.FormattedAnnualizedReturn</span>
                                            </td>
                                            <td>@plan.FormattedMinAmount RZW</td>
                                            <td>@plan.FormattedMaxAmount</td>
                                            <td>
                                                <button type="button" 
                                                        class="btn btn-sm btn-primary comparison-select-btn" 
                                                        data-plan-id="@plan.Id"
                                                        onclick="selectPlan(@plan.Id)">
                                                    @Localizer["Select"]
                                                </button>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Hidden Input for Selected Plan -->
                <input type="hidden" asp-for="Form.SelectedPlanId" id="selectedPlanId" />
                <div class="validation-message">
                    <span asp-validation-for="Form.SelectedPlanId" class="text-danger"></span>
                </div>
            </div>

            <!-- Step 2 ve 3 buraya gelecek (sonraki adımlarda) -->
            
        </form>
    }
    else if (!Model.ViewModel.HasAvailablePlans)
    {
        <!-- No Plans Available -->
        <div class="no-plans-available">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                @Localizer["No savings plans are currently available. Please try again later."]
            </div>
        </div>
    }
</div>

@section Scripts {
    <script src="~/js/rzw-savings-create.js"></script>
}

@section Styles {
    <link href="~/css/rzw-savings-create.css" rel="stylesheet" />
}
```

#### ******* Plan Selection CSS

**Dosya**: `src/wwwroot/css/rzw-savings-create.css`
```css
/* Create Savings Page Styles */
.create-savings-container {
    padding: 20px 0;
}

/* Page Header */
.page-header {
    margin-bottom: 30px;
}

.page-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.page-subtitle {
    color: #6c757d;
    font-size: 1.1rem;
    margin-bottom: 0;
}

/* Balance Summary Card */
.balance-summary-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 30px;
    border: 1px solid #dee2e6;
}

.balance-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.balance-details {
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
}

.balance-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.balance-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.balance-value {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    font-size: 1rem;
}

.balance-value.available {
    color: #28a745;
}

.balance-value.locked {
    color: #ffc107;
}

.insufficient-balance-warning {
    text-align: center;
    color: #856404;
}

.balance-status {
    text-align: center;
    color: #155724;
}

/* Form Steps */
.form-step {
    margin-bottom: 40px;
}

.step-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.step-number .step-badge {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.2rem;
}

.step-title {
    color: #2c3e50;
    font-weight: 600;
    margin: 0;
}

.step-description {
    color: #6c757d;
    margin: 5px 0 0 0;
}

/* Plans Grid */
.plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

/* Plan Cards */
.plan-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.plan-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    border-color: #667eea;
}

.plan-card.selected {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
    transform: translateY(-3px);
}

.plan-card.plan-card-daily {
    border-left: 4px solid #007bff;
}

.plan-card.plan-card-monthly {
    border-left: 4px solid #28a745;
}

.plan-card.plan-card-yearly {
    border-left: 4px solid #17a2b8;
}

/* Plan Header */
.plan-header {
    background: #f8f9fa;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.plan-badges {
    display: flex;
    gap: 8px;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.plan-badges .badge {
    font-size: 0.75rem;
    padding: 4px 8px;
}

.badge-recommended {
    background: linear-gradient(135deg, #ffc107, #ff8c00);
    color: white;
}

.badge-popular {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

.badge-primary {
    background: #007bff;
}

.badge-success {
    background: #28a745;
}

.badge-info {
    background: #17a2b8;
}

.plan-title h6 {
    color: #2c3e50;
    font-weight: 600;
    margin: 0;
    font-size: 1.1rem;
}

/* Plan Content */
.plan-content {
    padding: 25px 20px;
}

.plan-rate {
    text-align: center;
    margin-bottom: 25px;
}

.rate-primary {
    margin-bottom: 5px;
}

.rate-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    font-family: 'Courier New', monospace;
}

.rate-period {
    font-size: 1rem;
    color: #6c757d;
    margin-left: 5px;
}

.rate-annual {
    color: #28a745;
    font-weight: 500;
}

.plan-features {
    margin-bottom: 20px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    font-size: 0.9rem;
    color: #495057;
}

.feature-item i {
    width: 16px;
    text-align: center;
}

.plan-description {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

/* Plan Footer */
.plan-footer {
    padding: 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.plan-select-btn {
    width: 100%;
    font-weight: 500;
    border-radius: 8px;
    padding: 10px;
    transition: all 0.3s ease;
}

.plan-card.selected .plan-select-btn {
    background: #28a745;
    border-color: #28a745;
    color: white;
}

/* Plan Selected Indicator */
.plan-selected-indicator {
    position: absolute;
    top: 15px;
    right: 15px;
    background: #28a745;
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    display: none;
    align-items: center;
    gap: 5px;
}

.plan-card.selected .plan-selected-indicator {
    display: flex;
}

/* Plan Comparison */
.plan-comparison-section {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.comparison-toggle {
    text-align: center;
    margin-bottom: 20px;
}

.comparison-toggle .btn-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
}

.comparison-toggle .btn-link:hover {
    color: #5a6fd8;
}

.comparison-table-container {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.comparison-table {
    margin: 0;
    border: none;
}

.comparison-table thead th {
    background: #f8f9fa;
    border: none;
    font-weight: 600;
    color: #495057;
    padding: 15px;
    font-size: 0.9rem;
}

.comparison-table tbody td {
    padding: 15px;
    border-top: 1px solid #e9ecef;
    vertical-align: middle;
}

.comparison-row:hover {
    background-color: #f8f9fa;
}

.plan-name-cell {
    display: flex;
    align-items: center;
}

.rate-highlight {
    font-weight: 600;
    color: #2c3e50;
    font-family: 'Courier New', monospace;
}

.apy-highlight {
    font-weight: 600;
    color: #28a745;
}

.comparison-select-btn {
    padding: 5px 15px;
    font-size: 0.85rem;
}

/* No Plans Available */
.no-plans-available {
    text-align: center;
    padding: 40px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .create-savings-container {
        padding: 15px 0;
    }
    
    .plans-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .balance-details {
        flex-direction: column;
        gap: 10px;
    }
    
    .step-header {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .plan-content {
        padding: 20px 15px;
    }
    
    .rate-value {
        font-size: 2rem;
    }
    
    .comparison-table-container {
        overflow-x: auto;
    }
    
    .comparison-table {
        min-width: 600px;
    }
}

@media (max-width: 576px) {
    .plan-card {
        margin-bottom: 15px;
    }
    
    .plan-header,
    .plan-content,
    .plan-footer {
        padding: 15px;
    }
    
    .rate-value {
        font-size: 1.8rem;
    }
    
    .feature-item {
        font-size: 0.85rem;
    }
}

/* Animation Classes */
.plan-card-animate {
    animation: planCardSlideIn 0.5s ease-out;
}

@keyframes planCardSlideIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.plan-selected-animation {
    animation: planSelected 0.3s ease-out;
}

@keyframes planSelected {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}
```

## 📋 Kontrol Listesi

### ✅ YAPILACAKLAR
- [ ] Plan selection HTML oluşturma
- [ ] Plan card design oluşturma
- [ ] Plan comparison table oluşturma
- [ ] CSS styling ekleme
- [ ] Interactive selection logic (JavaScript - sonraki adımda)
- [ ] Responsive design testleri

### 🔄 YAPILMAKTA OLANLAR
- (Henüz başlanmadı)

### ✅ YAPILMIŞLAR
- (Henüz tamamlanmadı)

## 🧪 Test Kriterleri

### UI Tests
- [ ] Plan kartları doğru görüntüleniyor
- [ ] Plan seçimi çalışıyor
- [ ] Comparison table çalışıyor
- [ ] Responsive design tüm cihazlarda çalışıyor

### UX Tests
- [ ] Plan recommendation badges görünüyor
- [ ] Hover effects çalışıyor
- [ ] Selection feedback uygun
- [ ] Mobile experience iyi

## 📝 Notlar

### Plan Card Features
- **Recommended badge**: Monthly plan için
- **Popular badge**: Yearly plan için
- **Color coding**: Her plan türü için farklı renk
- **Interactive selection**: Click to select
- **Hover effects**: Visual feedback

### Plan Comparison
- **Collapsible table**: Space-saving design
- **All plan details**: Side-by-side comparison
- **Quick selection**: Direct select from table
- **Mobile scrollable**: Horizontal scroll on mobile

### Responsive Behavior
- **Desktop**: 3 cards per row
- **Tablet**: 2 cards per row
- **Mobile**: 1 card per row
- **Table**: Horizontal scroll on mobile

### Sonraki Adım
Bu adım tamamlandıktan sonra **Adım 5.3.3: Amount Input ve Calculation** başlayacak.

---
**Tahmini Süre**: 2-3 saat
**Öncelik**: Yüksek
**Bağımlılıklar**: Adım 5.3.1 tamamlanmış olmalı
