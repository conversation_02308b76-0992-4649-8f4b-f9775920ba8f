﻿@page
@model RazeWinComTr.Areas.MyAccount.Pages.DepositPaparaModel
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.DbModel
@using RazeWinComTr.Areas.Admin.Services
@inject SettingService _settingService
@inject IStringLocalizer<SharedResource> Localizer
@{
    var paparaName = await _settingService.GetSettingAsync("papara_name", "");
    var paparaNumber = await _settingService.GetSettingAsync("papara_number", "");
    var paparaNote = await _settingService.GetSettingAsync("papara_note", "Lütfen açıklama kısmına kullanıcı ID'nizi yazınız");
    var paparaUrl = await _settingService.GetSettingAsync("papara_url", "https://www.papara.com/");
}

<style>
    .deposit-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px 15px;
    }

    .deposit-card {
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        background-color: #fff;
        margin-bottom: 30px;
    }

    .deposit-card .card-header {
        background: linear-gradient(135deg, #3a7bd5, #00d2ff);
        color: white;
        padding: 16px 20px;
        font-size: 1.2rem;
        border-bottom: none;
    }

    .deposit-card .card-header a {
        color: white !important;
        font-weight: bold;
        text-decoration: none;
        margin-right: 10px;
        transition: all 0.3s ease;
    }

    .deposit-card .card-header a:hover {
        transform: translateX(-3px);
    }

    .deposit-card .card-body {
        padding: 25px;
    }

    .deposit-card .alert-warning {
        background-color: #fff8e1;
        border-color: #ffe0b2;
        color: #ff6f00;
        border-radius: 8px;
    }

    .deposit-info {
        background-color: #f9f9f9;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .deposit-info h5 {
        margin-bottom: 10px;
        color: #555;
    }

    .deposit-info h5 b {
        color: #333;
        font-weight: 600;
    }

    .deposit-limits {
        background-color: #f0f7ff;
        border-radius: 8px;
        padding: 15px;
        margin: 20px 0;
    }

    .deposit-limits h4 {
        color: #2962ff;
        font-size: 1.1rem;
        margin-bottom: 10px;
    }

    .form-control {
        border-radius: 6px;
        height: calc(2.5rem + 2px);
        border: 1px solid #ddd;
        transition: all 0.3s;
    }

    .form-control:focus {
        border-color: #3a7bd5;
        box-shadow: 0 0 0 0.2rem rgba(58, 123, 213, 0.15);
    }

    .input-group-text {
        background-color: #f8f9fa;
        border-color: #ddd;
        color: #495057;
    }

    .btn {
        border-radius: 6px;
        padding: 10px 20px;
        font-weight: 500;
        transition: all 0.3s;
    }

    .btn-success {
        background: linear-gradient(135deg, #43a047, #2e7d32);
        border: none;
    }

    .btn-success:hover {
        background: linear-gradient(135deg, #2e7d32, #1b5e20);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .btn-danger {
        background: linear-gradient(135deg, #e53935, #c62828);
        border: none;
    }

    .btn-danger:hover {
        background: linear-gradient(135deg, #c62828, #b71c1c);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .btn-info {
        background: linear-gradient(135deg, #039be5, #0277bd);
        border: none;
        color: white;
    }

    .btn-info:hover {
        background: linear-gradient(135deg, #0277bd, #01579b);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        color: white;
    }

    .card-footer {
        background-color: #f9f9f9;
        border-top: 1px solid #eee;
        padding: 20px;
    }

    .security-info {
        color: #2e7d32;
        font-size: 1rem;
        font-weight: 600;
        background-color: #e8f5e9;
        border-radius: 8px;
        padding: 12px 15px;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        border: 1px solid #c8e6c9;
        margin-top: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .security-info i {
        color: #4caf50;
        font-size: 1.2rem;
        margin-right: 10px;
    }

    .security-badge {
        background-color: #4caf50;
        color: white;
        font-size: 0.8rem;
        padding: 2px 6px;
        border-radius: 4px;
        margin-left: 8px;
        font-weight: 700;
        letter-spacing: 0.5px;
        display: inline-block;
        vertical-align: middle;
    }

    .user-id-note {
        background-color: #e8f5e9;
        border-radius: 8px;
        padding: 12px;
        margin-top: 15px;
        border-left: 4px solid #43a047;
    }

    .user-id-note p {
        margin-bottom: 0;
        color: #2e7d32;
    }

    .user-id-note strong {
        color: #1b5e20;
    }

    /* Copy field styles */
    .copy-fields-table {
        display: table;
        width: 100%;
        border-collapse: separate;
        border-spacing: 0 12px;
    }

    .copy-field {
        display: table-row;
        align-items: center;
    }

    .field-label {
        font-weight: 600;
        color: #555;
        width: 220px;
        display: table-cell;
        text-align: right;
        padding-right: 5px;
        vertical-align: middle;
        white-space: nowrap;
    }

    .field-value {
        font-weight: 600;
        color: #333;
        padding: 0 8px;
        word-break: break-word;
        display: table-cell;
        width: 100%;
        vertical-align: middle;
    }

    .copy-btn {
        background: none;
        border: none;
        color: #3a7bd5;
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.2s ease;
        display: table-cell;
        vertical-align: middle;
        text-align: center;
        position: relative;
        width: 40px;
    }

    .copy-btn:hover {
        background-color: rgba(58, 123, 213, 0.1);
        color: #0277bd;
    }

    .copy-btn:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(58, 123, 213, 0.25);
    }

    .copy-btn i {
        font-size: 14px;
    }

    .copy-tooltip {
        position: absolute;
        background-color: #4caf50;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        z-index: 100;
        top: -30px;
        left: 50%;
        transform: translateX(-50%);
        animation: fadeInOut 1.5s ease;
        pointer-events: none;
    }

    .copy-tooltip::after {
        content: '';
        position: absolute;
        top: 100%;
        left: 50%;
        margin-left: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: #4caf50 transparent transparent transparent;
    }

    @@keyframes fadeInOut {
        0% { opacity: 0; }
        15% { opacity: 1; }
        85% { opacity: 1; }
        100% { opacity: 0; }
    }

    /* Icon colors */
    .fas {
        color: #555;
    }

    /* Responsive adjustments */
    @@media (max-width: 767.98px) {
        .deposit-card .card-body {
            padding: 20px 15px;
        }

        .btn {
            padding: 8px 16px;
        }
    }
</style>

<div class="deposit-container">
    <div class="deposit-card">
        <div class="card-header">
            <a href="/MyAccount/Deposit" class="back-link">
                <i class="fas fa-chevron-left"></i>
            </a>
            <span>@Localizer["Papara"]</span><span class="font-weight-normal ml-1">@Localizer["Make Deposit With"]</span>
        </div>

        <form id="form" method="post">
            <div class="card-body">
                <div class="alert alert-warning" role="alert">
                    <strong class="d-block mb-2">@Localizer["Important"]</strong>
                    <p class="mb-0">@Localizer["Login To Papara Account To Make Deposit"]</p>
                </div>

                <div class="deposit-info">
                    <div class="copy-fields-table">
                        <div class="copy-field">
                            <span class="field-label">@Localizer["Recipient"] :</span>
                            <span class="field-value" id="recipient-text">@paparaName</span>
                            <button type="button" class="copy-btn" onclick="copyToClipboard('recipient-text')" title="@Localizer["Copy"]">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>

                        <div class="copy-field">
                            <span class="field-label">@Localizer["Papara"] :</span>
                            <span class="field-value" id="papara-text">@paparaNumber</span>
                            <button type="button" class="copy-btn" onclick="copyToClipboard('papara-text')" title="@Localizer["Copy"]">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>

                        <div class="copy-field">
                            <span class="field-label">@Localizer["Text For Description"] :</span>
                            <span class="field-value" id="description-text">Kullanıcı Id: @Model.CurrentUserId, Adı Soyadı: @Model.UserFullName</span>
                            <button type="button" class="copy-btn" onclick="copyToClipboard('description-text')" title="@Localizer["Copy"]">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>

                    <div class="mt-3">
                        <a href="@paparaUrl" target="_blank" class="btn btn-info btn-sm">
                            <i class="fas fa-external-link-alt mr-1"></i> @Localizer["Go To Papara"]
                        </a>
                    </div>
                </div>

                <div class="user-id-note">
                    <p><strong>@Localizer["Important Note"]:</strong> @Localizer["Write Exact Text In Papara Description"]</p>
                </div>

                @if (TempData["AlertMessage"] != null)
                {
                    var alertType = TempData["AlertType"]?.ToString() ?? "info";
                    <div class="alert alert-@alertType" role="alert">
                        @TempData["AlertMessage"]
                    </div>
                }

                <div class="deposit-limits">
                    <span class="text-danger font-weight-bold">@paparaNote</span>
                    <p class="text-secondary mt-2 mb-0">@Localizer["Please Do Not Click The Completed Deposit Button Before Making The Deposit"]</p>
                </div>

                <div class="row justify-content-center">
                    <div class="col-12 col-md-10">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                        <div class="form-group">
                            <label asp-for="DepositInfo.FullName">@Localizer["Full Name"]</label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                </div>
                                <input asp-for="DepositInfo.FullName" class="form-control" placeholder="@Localizer["Enter Full Name"]">
                            </div>
                            <span asp-validation-for="DepositInfo.FullName" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="DepositInfo.PaparaNumber">@Localizer["Papara No"]</label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-credit-card"></i></span>
                                </div>
                                <input asp-for="DepositInfo.PaparaNumber" class="form-control" placeholder="xxxxxxxx" oninput="updateDescription()">
                            </div>
                            <span asp-validation-for="DepositInfo.PaparaNumber" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="DepositInfo.Phone">@Localizer["Phone Number"]</label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                </div>
                                <input asp-for="DepositInfo.Phone" class="form-control" placeholder="555 555 55 55">
                            </div>
                            <span asp-validation-for="DepositInfo.Phone" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label>@Localizer["Amount"]<span class="text-danger">*</span></label>
                            <div class="input-group money-input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-money-bill-wave"></i></span>
                                </div>
                                <input asp-for="DepositInfo.AmountInteger" class="form-control integer-part" placeholder="0" type="number" min="0" step="1" inputmode="numeric" oninput="updateDescription()">
                                <div class="input-group-append input-group-prepend decimal-separator">
                                    <span class="input-group-text">.</span>
                                </div>
                                <input asp-for="DepositInfo.AmountFraction" class="form-control fraction-part" placeholder="00" type="text" pattern="[0-9]*" maxlength="2" inputmode="numeric" oninput="validateFraction(); updateDescription()">
                                <div class="input-group-append">
                                    <span class="input-group-text">₺</span>
                                </div>
                            </div>
                            <span asp-validation-for="DepositInfo.AmountInteger" class="text-danger"></span>
                            <span asp-validation-for="DepositInfo.AmountFraction" class="text-danger"></span>
                        </div>
                        <style>
                            /* Para girişi için özel stiller */
                            .money-input-group {
                                display: flex;
                            }
                            .money-input-group .integer-part {
                                width: 120px;
                                text-align: right;
                                border-right: none;
                                border-top-left-radius: 0;
                                border-bottom-left-radius: 0;
                                color: #000;
                            }
                            .money-input-group .decimal-separator {
                                margin: 0;
                                padding: 0;
                            }
                            .money-input-group .decimal-separator .input-group-text {
                                border-radius: 0;
                                border-left: none;
                                border-right: none;
                                padding: 0;
                                width: 10px;
                                min-width: 10px;
                                text-align: center;
                                background-color: white;
                                font-weight: bold;
                                color: #000;
                            }
                            .money-input-group .fraction-part {
                                width: 60px;
                                border-left: none;
                                border-radius: 0;
                                color: #000;
                            }
                            .money-input-group.focused {
                                box-shadow: 0 0 0 0.2rem rgba(58, 123, 213, 0.15);
                            }
                        </style>
                    </div>
                </div>
            </div>

            <div class="card-footer">
                <div class="d-flex flex-column flex-sm-row-reverse">
                    <button type="submit" class="btn btn-success mb-2 mb-sm-0 ml-sm-2">
                        <i class="fas fa-check mr-2"></i>@Localizer["I Made The Deposit"]
                    </button>
                    <a href="/MyAccount/Deposit" class="btn btn-danger">
                        <i class="fas fa-times mr-2"></i>@Localizer["Cancel"]
                    </a>
                </div>
            </div>
        </form>
    </div>

    <div class="security-info">
        <i class="fas fa-shield-alt"></i>
        <span>@Localizer["Secure 256-bit TLS-encryption"] <span class="security-badge">SSL</span></span>
    </div>
</div>

@section Scripts {
    <script>
        // Show SweetAlert if success message exists
        @if (!string.IsNullOrEmpty(Model.AlertMessage) && Model.AlertType == "success")
        {
            <text>
            $(document).ready(function() {
                Swal.fire({
                    title: window.t["Success"] || '@Localizer["Success"]',
                    text: '@Html.Raw(Model.AlertMessage)',
                    icon: 'success',
                    confirmButtonText: window.t["OK"] || '@Localizer["OK"]',
                    confirmButtonColor: '#28a745',
                    allowOutsideClick: false,
                    customClass: {
                        confirmButton: 'btn btn-success btn-lg',
                        popup: 'animated fadeInDown'
                    },
                    padding: '2em',
                    showConfirmButton: true
                }).then(function() {
                    window.location.href = '/MyAccount/Deposit';
                });
            });
            </text>
        }

        function validateFraction() {
            // Handle decimal part input
            var fractionInput = document.getElementById('DepositInfo_AmountFraction');
            if (fractionInput) {
                // Remove any non-digit characters
                var value = fractionInput.value.replace(/[^0-9]/g, '');
                fractionInput.value = value;

                // Limit to 2 digits for decimal part
                if (value.length > 2) {
                    fractionInput.value = value.substring(0, 2);
                }
            }
        }

        // Add custom validation method for combined amount
        $.validator.addMethod("combinedAmountGreaterThanZero", function(value, element, param) {
            var integerPart = parseInt($("#DepositInfo_AmountInteger").val()) || 0;
            var fractionPart = $("#DepositInfo_AmountFraction").val() || '0';

            // If both parts are 0, validation fails
            if (integerPart === 0 && (fractionPart === '0' || fractionPart === '' || parseInt(fractionPart) === 0)) {
                return false;
            }
            return true;
        }, '@Localizer["Total Amount Must Be Greater Than Zero"]');

        function updateDescription() {
            var userId = '@Model.CurrentUserId';
            var userName = '@Model.UserFullName';
            var integerPart = document.getElementById('DepositInfo_AmountInteger').value || '0';
            var fractionPart = document.getElementById('DepositInfo_AmountFraction').value || '00';
            var paparaNumber = document.getElementById('DepositInfo_PaparaNumber').value || '';

            // Format the amount
            var amount = integerPart;
            if (fractionPart) {
                amount += ',' + fractionPart;
            }

            var description = 'Kullanıcı Id: ' + userId;
            description += ', Adı Soyadı: ' + userName;

            if (paparaNumber) {
                description += ', Papara No: ' + paparaNumber;
            }

            if (parseInt(integerPart) > 0 || (fractionPart && parseInt(fractionPart) > 0)) {
                description += ', Yatırılan Tutar: ' + amount + ' TL';
            }

            document.getElementById('description-text').innerText = description;
        }

        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.innerText;

            // Create a temporary textarea element to copy from
            const textarea = document.createElement('textarea');
            textarea.value = text;
            textarea.setAttribute('readonly', '');
            textarea.style.position = 'absolute';
            textarea.style.left = '-9999px';
            document.body.appendChild(textarea);

            // Select and copy the text
            textarea.select();
            document.execCommand('copy');

            // Remove the temporary element
            document.body.removeChild(textarea);

            // Show a tooltip or some indication that text was copied
            const button = element.nextElementSibling;
            const originalTitle = button.getAttribute('title');
            const originalColor = button.style.color;

            // Create and show a tooltip
            const tooltip = document.createElement('div');
            tooltip.className = 'copy-tooltip';
            tooltip.textContent = '@Localizer["Copied"]';
            button.appendChild(tooltip);

            // Change button appearance
            button.style.color = '#4caf50';
            button.innerHTML = '<i class="fas fa-check"></i>';

            // Reset after a short delay
            setTimeout(() => {
                button.style.color = originalColor;
                button.innerHTML = '<i class="fas fa-copy"></i>';
                if (tooltip && tooltip.parentNode) {
                    tooltip.parentNode.removeChild(tooltip);
                }
            }, 1500);
        }
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/js/all.min.js" integrity="sha512-Tn2m0TIpgVyTzzvmxLNuqbSJH3JP8jm+Cy3hvHrW7ndTDcJ1w5mBiksqDBb8GpE2ksktFvDB/ykZ0mDpsZj20w==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        $(document).ready(function() {
            // Add validation rules
            $("form").validate({
                rules: {
                    "DepositInfo.AmountInteger": {
                        required: true,
                        min: 0,
                        combinedAmountGreaterThanZero: true
                    },
                    "DepositInfo.AmountFraction": {
                        pattern: /^[0-9]*$/
                    },
                    "DepositInfo.FullName": {
                        required: true
                    },
                    "DepositInfo.PaparaNumber": {
                        required: true
                    },
                    "DepositInfo.Phone": {
                        required: true
                    }
                },
                messages: {
                    "DepositInfo.AmountInteger": {
                        required: '@Localizer["Please Enter Valid Amount"]',
                        min: '@Localizer["Amount Cannot Be Negative"]',
                        combinedAmountGreaterThanZero: '@Localizer["Total Amount Must Be Greater Than Zero"]'
                    },
                    "DepositInfo.AmountFraction": {
                        pattern: '@Localizer["Only Digits For Decimal Part"]'
                    },
                    "DepositInfo.FullName": {
                        required: '@Localizer["Full Name Required"]'
                    },
                    "DepositInfo.PaparaNumber": {
                        required: '@Localizer["Papara Number Required"]'
                    },
                    "DepositInfo.Phone": {
                        required: '@Localizer["Phone Number Required"]'
                    }
                },
                errorElement: 'span',
                errorPlacement: function (error, element) {
                    error.addClass('text-danger');
                    element.closest('.form-group').append(error);
                },
                highlight: function (element, errorClass, validClass) {
                    $(element).addClass('is-invalid');
                },
                unhighlight: function (element, errorClass, validClass) {
                    $(element).removeClass('is-invalid');
                }
            });
        });
    </script>

    <script>
        // Initialize description when page loads
        $(document).ready(function() {
            updateDescription();

            // Focus handling for better UX
            $("#DepositInfo_AmountInteger, #DepositInfo_AmountFraction").on('focus', function() {
                $(this).closest('.money-input-group').addClass('focused');
            }).on('blur', function() {
                $(this).closest('.money-input-group').removeClass('focused');
            });

            // Select all text in fraction field when it receives focus
            $("#DepositInfo_AmountFraction").on('focus', function() {
                // Use setTimeout to ensure this happens after the browser's default focus behavior
                setTimeout(() => {
                    this.select();
                }, 0);
            });

            // Handle comma or period key press in integer field to move cursor to fraction field
            $("#DepositInfo_AmountInteger").on('keydown', function(e) {
                // Check if the key pressed is comma (188), period (190), or numpad decimal (110)
                if (e.which === 188 || e.which === 190 || e.which === 110 || e.key === ',' || e.key === '.') {
                    e.preventDefault(); // Prevent the character from being entered
                    $("#DepositInfo_AmountFraction").focus(); // Move focus to fraction field
                }
            });

            // Also handle input event for mobile devices where keydown might not catch all cases
            $("#DepositInfo_AmountInteger").on('input', function(e) {
                var value = $(this).val();
                // If the value contains a comma or period
                if (value.includes(',') || value.includes('.')) {
                    // Remove the comma or period
                    $(this).val(value.replace(/[,\.]/g, ''));
                    // Move focus to fraction field
                    $("#DepositInfo_AmountFraction").focus();
                }
            });

            // Format integer part with thousand separators on blur
            $("#DepositInfo_AmountInteger").on('blur', function() {
                var value = $(this).val();
                if (value && !isNaN(value)) {
                    // Store the cursor position
                    var cursorPos = this.selectionStart;

                    // Format the value
                    var formattedValue = parseInt(value).toLocaleString('en-US');

                    // Update the display value (but not the actual value)
                    $(this).attr('data-formatted-value', formattedValue);
                }
            });
        });
    </script>
}
