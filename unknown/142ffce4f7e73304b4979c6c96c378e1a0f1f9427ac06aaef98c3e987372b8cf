@page
@model RazeWinComTr.Areas.MyAccount.Pages.BalanceHistoryModel
@using RazeWinComTr.Areas.Admin.DbModel
@using RazeWinComTr.Areas.Admin.Helpers
@inject IStringLocalizer<SharedResource> Localizer
@using static RazeWinComTr.Areas.Admin.Helpers.NumberFormatHelper

@{
    ViewData["Title"] = Localizer["Balance History"];
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">@Localizer["Balance History"]</h3>
                    <div class="card-tools">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">@Localizer["Current Balance"]:</span>
                            </div>
                            <span class="form-control bg-light">@FormatDecimalWithThousandSeparator(Model.CurrentBalance) @Localizer["Currency_Symbol"]</span>
                        </div>
                    </div>
                </div>
                <div class="card-body table-responsive p-0">
                    <table class="table table-hover text-nowrap">
                        <thead>
                            <tr>
                                <th>@Localizer["Date"]</th>
                                <th>@Localizer["Type"]</th>
                                <th>@Localizer["Amount"]</th>
                                <th>@Localizer["Previous Balance"]</th>
                                <th>@Localizer["New Balance"]</th>
                                <th>@Localizer["Description"]</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var transaction in Model.Transactions)
                            {
                                <tr>
                                    <td>@transaction.CreatedDate.ToLocalTime().ToString("dd.MM.yyyy HH:mm")</td>
                                    <td>
                                        @{
                                            string transactionTypeKey = transaction.TransactionType switch
                                            {
                                                TransactionType.Deposit => "Deposit",
                                                TransactionType.Withdrawal => "Withdrawal",
                                                TransactionType.PackagePurchase => "Package Purchase",
                                                TransactionType.TradeProfit => "Trade Profit",
                                                TransactionType.TradeLoss => "Trade Loss",
                                                TransactionType.ReferralReward => "Referral Reward",
                                                TransactionType.AdminAdjustment => "Admin Adjustment",
                                                TransactionType.Buy => "Buy",
                                                TransactionType.Sell => "Sell",
                                                _ => transaction.TransactionType != null ? transaction.TransactionType.ToString() : "Other"
                                            };
                                        }
                                        @Localizer[transactionTypeKey]
                                    </td>
                                    <td class="@(transaction.TransactionType == TransactionType.Deposit || transaction.TransactionType == TransactionType.ReferralReward || transaction.TransactionType == TransactionType.Sell ? "text-success" : "text-danger")">
                                        @{
                                            string prefix = transaction.TransactionType == TransactionType.Deposit || transaction.TransactionType == TransactionType.ReferralReward || transaction.TransactionType == TransactionType.Sell ? "+" : "-";
                                        }
                                        @prefix@FormatDecimalWithThousandSeparator(transaction.Amount) @Localizer["Currency_Symbol"]
                                    </td>
                                    <td>@FormatDecimalWithThousandSeparator(transaction.PreviousBalance) @Localizer["Currency_Symbol"]</td>
                                    <td>@FormatDecimalWithThousandSeparator(transaction.NewBalance) @Localizer["Currency_Symbol"]</td>
                                    <td>
                                        @transaction.Description
                                        @if (transaction.ReferralReward != null)
                                        {
                                            <div class="text-muted small mt-1">
                                                @transaction.ReferralReward.GetReferralRewardDescription()
                                            </div>
                                        }
                                    </td>
                                </tr>
                            }
                            @if (!Model.Transactions.Any())
                            {
                                <tr>
                                    <td colspan="6" class="text-center">@Localizer["No transactions found"]</td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
