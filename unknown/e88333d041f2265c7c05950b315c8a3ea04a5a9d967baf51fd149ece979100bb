@page
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.DbModel
@using RazeWinComTr.Areas.Admin.Enums
@using RazeWinComTr.Areas.Admin.ViewModels.Deposit
@model RazeWinComTr.Areas.Admin.Pages.Deposit.IndexModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["Payments"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["Payments"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item active">@L["Payments"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        @if (TempData["ErrorMessage"] != null)
        {
            <div class="alert alert-danger alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h5><i class="icon fas fa-ban"></i> @L["Error"]!</h5>
                @(TempData["ErrorMessage"]?.ToString() ?? string.Empty)
            </div>
        }
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <a id="btnCreateNew" asp-page="Create" class="btn btn-success" role="button"
                           aria-label="@($"{L["Create a New"]} {L["Payment"]}")" title="@($"{L["Create a New"]} {L["Payment"]}")">
                            <i class="fas fa-plus fa-2x"></i>
                        </a>

                        <div class="btn-group ml-2">
                            <a href="/Admin/Deposit" class="btn @(string.IsNullOrEmpty(Model.DepositTypeFilter) ? "btn-primary" : "btn-outline-primary")">
                                @L["All Payments"]
                            </a>
                            <a href="/Admin/Deposit?filter=fiat" class="btn @(Model.ShowingFiatOnly ? "btn-primary" : "btn-outline-primary")">
                                @L["Fiat Deposits"]
                            </a>
                            <a href="/Admin/Deposit?filter=crypto" class="btn @(Model.ShowingCryptoOnly ? "btn-primary" : "btn-outline-primary")">
                                @L["Cryptocurrency Deposits"]
                            </a>
                        </div>
                    </div>

                    <h3 class="card-title">@L["Count"]: @(Model.Deposits.Count)</h3>
                </div>
            </div>
            <div class="card-body">
                <table class="table table-bordered table-striped datatable">
                    <thead>
                        <tr>
                            <th>@L["User"]</th>
                            <th>@L["Payment Type"]</th>
                            <th>@L["Amount"]</th>
                            <th>@L["Status"]</th>
                            <th>@L["Reward Status"]</th>
                            <th>@L["Created Date"]</th>
                            <th>@L["Last Online Date"]</th>
                            <th style="width: 200px">@L["Actions"]</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model.Deposits)
                        {
                            <tr>
                                <td>@item.UserEmail</td>
                                <td>
                                    @if (!string.IsNullOrEmpty(item.DepositType))
                                    {
                                        @if (item.IsCryptoDeposit)
                                        {
                                            <span class="badge badge-primary">@item.DepositType</span>
                                        }
                                        else
                                        {
                                            <span class="badge badge-info">@item.DepositType</span>
                                        }
                                    }
                                    else
                                    {
                                        <span>-</span>
                                    }
                                </td>
                                <td>
                                    @if (item.IsCryptoDeposit)
                                    {
                                        @item.Amount.ToString("N8")
                                    }
                                    else
                                    {
                                        @item.Amount.ToString("N2") @L["Currency_Symbol"]
                                    }
                                </td>
                                <td>
                                    @if (item.Status == DepositStatus.Pending)
                                    {
                                        <span class="badge badge-warning">@L["Pending"]</span>
                                    }
                                    else if (item.Status == DepositStatus.Approved)
                                    {
                                        <span class="badge badge-success">@L["Approved"]</span>
                                    }
                                    else if (item.Status == DepositStatus.Rejected)
                                    {
                                        <span class="badge badge-danger">@L["Rejected"]</span>
                                    }
                                </td>
                                <td>
                                    @if (item.Status != DepositStatus.Approved)
                                    {
                                        <span class="badge badge-secondary">@L["N/A"]</span>
                                    }
                                    else
                                    {
                                        @switch (item.RewardStatus)
                                        {
                                            case DepositRewardStatus.Pending:
                                                <span class="badge badge-warning">@L["Pending"]</span>
                                                break;
                                            case DepositRewardStatus.Processed:
                                                <span class="badge badge-info">@L["Processed"]</span>
                                                break;
                                            case DepositRewardStatus.Distributed:
                                                <span class="badge badge-success">@L["Distributed"]</span>
                                                break;
                                            case DepositRewardStatus.NoRewards:
                                                <span class="badge badge-secondary">@L["No Rewards"]</span>
                                                break;
                                            case DepositRewardStatus.Failed:
                                                <span class="badge badge-danger">@L["Failed"]</span>
                                                break;
                                        }
                                    }
                                </td>
                                <td>@item.CreatedDate.ToLocalTime().ToString("g")</td>
                                <td>@(item.LastOnlineDate?.ToString("g") ?? "-")</td>
                                <td>
                                    @if (item.Status == DepositStatus.Pending)
                                    {
                                        <a href="/Admin/Deposit/Edit?id=@item.Id" class="btn btn-info btn-sm">
                                            <i class="fas fa-pencil-alt"></i> @L["Edit"]
                                        </a>
                                    }
                                    @if (item.Status == DepositStatus.Approved && item.RewardStatus == DepositRewardStatus.Pending)
                                    {
                                        <a href="/Admin/Deposit/DistributeRewards?id=@item.Id" class="btn btn-primary btn-sm">
                                            <i class="fas fa-gift"></i> @L["Distribute Rewards"]
                                        </a>
                                    }
                                    @if (item.RewardStatus == DepositRewardStatus.Distributed ||
                                                                    item.RewardStatus == DepositRewardStatus.NoRewards)
                                    {
                                        <a href="/Admin/Deposit/RewardSummary?id=@item.Id" class="btn btn-success btn-sm">
                                            <i class="fas fa-chart-pie"></i> @L["Summary"]
                                        </a>
                                    }
                                    @if (item.RewardStatus == DepositRewardStatus.Failed)
                                    {
                                        <a href="/Admin/Deposit/DistributeRewards?id=@item.Id" class="btn btn-warning btn-sm">
                                            <i class="fas fa-sync"></i> @L["Retry"]
                                        </a>
                                    }
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>
