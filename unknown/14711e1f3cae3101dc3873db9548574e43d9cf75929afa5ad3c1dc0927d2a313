# Faz 5.2: RZW Savings Ana Sayfası (1 gün)

## 📋 Alt Faz Özeti
MyAccount/RzwSavings ana sayfası oluşturma. Kullanıcının aktif vadeli hesaplarını, özet bilgilerini ve hızlı işlemlerini gösterecek dashboard tarzı sayfa.

## 🎯 Hedefler
- ✅ RzwSavings/Index.cshtml oluşturma
- ✅ RzwSavingsIndexModel oluşturma
- ✅ Dashboard widget'ları
- ✅ Aktif vadeli hesaplar listesi
- ✅ Hızlı işlem butonları

## 📊 Bu Alt Faz 4 Küçük Adıma Bölünmüştür

### **Adım 5.2.1**: PageModel ve ViewModel Oluşturma → `RZW_SAVINGS_PHASE_5_2_1.md`
- RzwSavingsIndexModel oluşturma
- ViewModel'ler ve DTO'lar
- Service entegrasyonu

### **Adım 5.2.2**: Ana Sayfa Layout ve Header → `RZW_SAVINGS_PHASE_5_2_2.md`
- Index.cshtml temel yapısı
- Page header ve navigation
- Breadcrumb ve sayfa başlığı

### **Adım 5.2.3**: Dashboard Widget'ları → `RZW_SAVINGS_PHASE_5_2_3.md`
- Özet istatistik kartları
- RZW bakiye özeti
- Toplam kazanç gösterimi

### **Adım 5.2.4**: Aktif Vadeli Hesaplar Listesi → `RZW_SAVINGS_PHASE_5_2_4.md`
- Vadeli hesaplar tablosu
- Durum göstergeleri
- Hızlı işlem butonları

## 📋 Genel Kontrol Listesi

### ✅ YAPILACAKLAR
- [ ] **Adım 5.2.1**: PageModel ve ViewModel oluşturma
- [ ] **Adım 5.2.2**: Ana sayfa layout ve header
- [ ] **Adım 5.2.3**: Dashboard widget'ları
- [ ] **Adım 5.2.4**: Aktif vadeli hesaplar listesi

### 🔄 YAPILMAKTA OLANLAR
- (Henüz başlanmadı)

### ✅ YAPILMIŞLAR
- (Henüz tamamlanmadı)

## 🎨 Sayfa Tasarım Konsepti

### Layout Yapısı
```
┌─────────────────────────────────────────┐
│ Page Header + Breadcrumb                │
├─────────────────────────────────────────┤
│ Dashboard Widgets (3 kart)              │
│ [Toplam RZW] [Aktif Hesap] [Kazanç]     │
├─────────────────────────────────────────┤
│ Quick Actions                           │
│ [Yeni Hesap Aç] [Faiz Geçmişi]         │
├─────────────────────────────────────────┤
│ Aktif Vadeli Hesaplar Tablosu           │
│ Plan | Miktar | Vade | Durum | İşlem    │
└─────────────────────────────────────────┘
```

### Renk Şeması
- **Ana Tema**: RZW gradient (#667eea → #764ba2)
- **Widget Kartları**: Beyaz arka plan, renkli border
- **Durum İkonları**: Yeşil (aktif), Sarı (yakın vade), Mavi (tamamlanmış)

### Responsive Breakpoints
- **Desktop**: 3 widget yan yana
- **Tablet**: 2 widget yan yana, 1 alt satır
- **Mobile**: Tüm widget'lar alt alta

## 🔧 Teknik Gereksinimler

### ViewModels
```csharp
public class RzwSavingsIndexModel
{
    public RzwSavingsDashboard Dashboard { get; set; }
    public List<RzwSavingsAccountSummary> ActiveAccounts { get; set; }
    public List<RzwSavingsPlan> AvailablePlans { get; set; }
}

public class RzwSavingsDashboard
{
    public decimal TotalRzwInSavings { get; set; }
    public int ActiveAccountsCount { get; set; }
    public decimal TotalEarnedInterest { get; set; }
    public decimal EstimatedMonthlyEarnings { get; set; }
}

public class RzwSavingsAccountSummary
{
    public int Id { get; set; }
    public string PlanName { get; set; }
    public decimal Amount { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime MaturityDate { get; set; }
    public string Status { get; set; }
    public decimal InterestRate { get; set; }
    public decimal TotalEarned { get; set; }
    public int DaysRemaining { get; set; }
    public bool IsNearMaturity { get; set; }
}
```

### JavaScript Fonksiyonları
- Dashboard widget'ları için real-time updates
- Vadeli hesap detay modal'ları
- Hızlı işlem butonları
- Responsive table handling

### CSS Components
- Dashboard widget kartları
- Progress bar'lar (vade gösterimi)
- Status badge'ler
- Responsive table

## 📱 Mobile Optimizasyonu

### Mobile-First Yaklaşım
- Widget'lar stack layout
- Horizontal scroll table
- Touch-friendly buttons
- Swipe gestures

### Performance
- Lazy loading
- Image optimization
- Minimal JavaScript
- CSS animations

## 🧪 Test Kriterleri

### Functional Tests
- [ ] Dashboard widget'ları doğru veri gösteriyor
- [ ] Aktif hesaplar listesi çalışıyor
- [ ] Hızlı işlem butonları çalışıyor
- [ ] Real-time updates çalışıyor

### UI/UX Tests
- [ ] Responsive design tüm cihazlarda çalışıyor
- [ ] Loading states uygun
- [ ] Error handling doğru
- [ ] Navigation akışı sorunsuz

## 📝 Notlar

### Önemli Özellikler
- Dashboard tarzı özet görünüm
- Real-time balance updates
- Quick action buttons
- Mobile-optimized design

### Veri Kaynakları
- RzwSavingsService (aktif hesaplar)
- RzwSavingsInterestService (kazanç bilgileri)
- RzwBalanceManagementService (bakiye bilgileri)
- RzwSavingsPlanService (mevcut planlar)

### Sonraki Adım
Bu alt faz tamamlandıktan sonra **Faz 5.3: Yeni Vadeli Hesap Sayfası** başlayacak.

---
**Tahmini Süre**: 1 gün (4 küçük adım)
**Öncelik**: Yüksek
**Bağımlılıklar**: Faz 5.1 tamamlanmış olmalı
