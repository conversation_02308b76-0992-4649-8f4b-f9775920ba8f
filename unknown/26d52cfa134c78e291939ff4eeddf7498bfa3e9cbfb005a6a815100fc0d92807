﻿@page
@using Microsoft.Extensions.Localization
@using System.Globalization
@model DashboardModel
@inject IStringLocalizer<SharedResource> L
@{
    ViewData["Title"] = L["Dashboard"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["Dashboard"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item active">@L["Dashboard"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <!-- User Profile Card -->
        <div class="row">
            <div class="col-md-12">
                <div class="card card-primary card-outline">
                    <div class="card-body box-profile">
                        <div class="row">
                            <div class="col-md-2 text-center">
                                <img class="profile-user-img img-fluid img-circle" src="/site/images/avatar_man.png" alt="User profile picture">
                            </div>
                            <div class="col-md-10">
                                <h3 class="profile-username">11123 1123</h3>
                                <p class="text-muted"><EMAIL></p>
                                <ul class="list-group list-group-unbordered mb-3">
                                    <li class="list-group-item">
                                        <a href="tel:5551234567" class="text-muted">
                                            <i class="fas fa-phone mr-2"></i>5551234567
                                        </a>
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-clock mr-2"></i>10.04.2025 23:37
                                    </li>
                                </ul>
                                <div class="mb-3">
                                    <span class="badge badge-success p-2">@L["Balance"]: @Model.Balance.ToString("0.########", CultureInfo.InvariantCulture) @L["Currency_Symbol"]</span>
                             </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Background Service Status -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">@L["Background Services Status"]</h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="custom-control custom-switch">
                                    <input type="checkbox" class="custom-control-input" id="autoRefreshToggle" checked>
                                    <label class="custom-control-label" for="autoRefreshToggle">@L["Auto Refresh"]</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="refreshIntervalSelect">@L["Refresh Interval"]</label>
                                    <select class="form-control" id="refreshIntervalSelect">
                                        <option value="2000">2 @L["seconds"]</option>
                                        <option value="5000" selected>5 @L["seconds"]</option>
                                        <option value="10000">10 @L["seconds"]</option>
                                        <option value="30000">30 @L["seconds"]</option>
                                        <option value="60000">60 @L["seconds"]</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button id="refreshNowBtn" class="btn btn-primary">@L["Refresh Now"]</button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table id="backgroundServiceTable" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>@L["Service Name"]</th>
                                        <th>@L["Status"]</th>
                                        <th>@L["Last Success"]</th>
                                        <th>@L["Time Since Last Success"]</th>
                                        <th>@L["Success Count"]</th>
                                        <th>@L["Failure Count"]</th>
                                        <th>@L["Last Error"]</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (Model.BackgroundServiceStatuses.Count > 0)
                                    {
                                        @foreach (var status in Model.BackgroundServiceStatuses)
                                        {
                                            <tr>
                                                <td>@status.Value.ServiceName</td>
                                                <td><span class="badge @status.Value.StatusBadgeClass">@status.Value.Status</span></td>
                                                <td>@(status.Value.LastSuccessfulRunTime?.ToLocalTime().ToString("dd.MM.yyyy HH:mm:ss") ?? "-")</td>
                                                <td>
                                                    @if (status.Value.TimeSinceLastSuccess.HasValue)
                                                    {
                                                        var timeSpan = status.Value.TimeSinceLastSuccess.Value;
                                                        if (timeSpan.TotalDays >= 1)
                                                        {
                                                            @($"{timeSpan.Days}d {timeSpan.Hours}h {timeSpan.Minutes}m")
                                                        }
                                                        else if (timeSpan.TotalHours >= 1)
                                                        {
                                                            @($"{timeSpan.Hours}h {timeSpan.Minutes}m {timeSpan.Seconds}s")
                                                        }
                                                        else if (timeSpan.TotalMinutes >= 1)
                                                        {
                                                            @($"{timeSpan.Minutes}m {timeSpan.Seconds}s")
                                                        }
                                                        else
                                                        {
                                                            @($"{timeSpan.Seconds}s")
                                                        }
                                                    }
                                                    else
                                                    {
                                                        <span>-</span>
                                                    }
                                                </td>
                                                <td>@status.Value.SuccessCount</td>
                                                <td>@status.Value.FailureCount</td>
                                                <td>@(status.Value.LastErrorMessage ?? "-")</td>
                                            </tr>
                                        }
                                    }
                                    else
                                    {
                                        <tr>
                                            <td colspan="7" class="text-center">@L["No background services found"]</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Info boxes -->
        <div class="row">
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box">
                    <span class="info-box-icon bg-info elevation-1"><i class="fas fa-tv"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">@L["Products"]</span>
                        <span class="info-box-number">@Model.ProductCount</span>
                        <a href="/Admin/Product" class="small-box-footer">@L["More Info"] <i class="fas fa-arrow-circle-right"></i></a>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box">
                    <span class="info-box-icon bg-success elevation-1"><i class="fas fa-building"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">@L["Projects"]</span>
                        <span class="info-box-number">@Model.ProjectCount</span>
                        <a href="/Admin/Project" class="small-box-footer">@L["More Info"] <i class="fas fa-arrow-circle-right"></i></a>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box">
                    <span class="info-box-icon bg-warning elevation-1"><i class="fas fa-mail-bulk"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">@L["References"]</span>
                        <span class="info-box-number">@Model.ReferenceCount</span>
                        <a href="/Admin/Reference" class="small-box-footer">@L["More Info"] <i class="fas fa-arrow-circle-right"></i></a>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box">
                    <span class="info-box-icon bg-danger elevation-1"><i class="fas fa-users"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">@L["Users"]</span>
                        <span class="info-box-number">@Model.UserCount</span>
                        <a href="/Admin/User" class="small-box-footer">@L["More Info"] <i class="fas fa-arrow-circle-right"></i></a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Info boxes for Pending Payments and Withdrawals -->
        <div class="row">
            <div class="col-12 col-sm-6 col-md-6">
                <div class="info-box">
                    <span class="info-box-icon bg-warning elevation-1"><i class="fas fa-money-bill-wave"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">@L["Pending Payments"]</span>
                        <span class="info-box-number">@Model.PendingDepositsCount</span>
                        <a href="/Admin/Deposit" class="small-box-footer">@L["More Info"] <i class="fas fa-arrow-circle-right"></i></a>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-6">
                <div class="info-box">
                    <span class="info-box-icon bg-danger elevation-1"><i class="fas fa-hand-holding-usd"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">@L["Pending Withdrawals"]</span>
                        <span class="info-box-number">@Model.PendingWithdrawalsCount</span>
                        <a href="/Admin/Withdrawal" class="small-box-footer">@L["More Info"] <i class="fas fa-arrow-circle-right"></i></a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <!-- Quick Stats -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">@L["Quick Stats"]</h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="progress-group mt-4">
                            @L["Active"] @L["Users"]
                            <span class="float-right"><b>@Model.ActiveUserCount</b>/@Model.UserCount</span>
                            <div class="progress progress-sm">
                                <div class="progress-bar bg-danger" style="width: @(Model.UserCount > 0 ? (Model.ActiveUserCount * 100 / Model.UserCount) : 0)%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        $(function () {
            // Variables for auto-refresh
            let autoRefreshEnabled = true;
            let refreshInterval = 5000; // 5 seconds default
            let refreshTimer;

            // Function to fetch background service status
            function fetchBackgroundServiceStatus() {
                $.ajax({
                    url: '/api/BackgroundServiceStatus',
                    type: 'GET',
                    dataType: 'json',
                    headers: {
                        'Authorization': 'Bearer ' + (localStorage.getItem('token') || '')
                    },
                    success: function (data) {
                        updateBackgroundServiceTable(data);
                        if (autoRefreshEnabled) {
                            refreshTimer = setTimeout(fetchBackgroundServiceStatus, refreshInterval);
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error('Error fetching background service status:', error);
                        if (autoRefreshEnabled) {
                            refreshTimer = setTimeout(fetchBackgroundServiceStatus, refreshInterval);
                        }
                    }
                });
            }

            // Function to update the background service table
            function updateBackgroundServiceTable(data) {
                const tbody = $('#backgroundServiceTable tbody');
                tbody.empty();

                if (Object.keys(data).length === 0) {
                    tbody.append(`<tr><td colspan="7" class="text-center">${window.t['No background services found']}</td></tr>`);
                    return;
                }

                for (const [serviceName, status] of Object.entries(data)) {
                    const lastSuccessTime = status.LastSuccessfulRunTime ? new Date(status.LastSuccessfulRunTime).toLocaleString() : '-';

                    let timeSinceLastSuccess = '-';
                    if (status.TimeSinceLastSuccess) {
                        // JSON serileştirme sırasında TimeSpan nesnesi farklı bir formatta geliyor
                        // Örnek: { "days": 0, "hours": 0, "minutes": 5, "seconds": 30, ... }
                        const days = status.TimeSinceLastSuccess.Days || 0;
                        const hours = status.TimeSinceLastSuccess.Hours || 0;
                        const minutes = status.TimeSinceLastSuccess.Minutes || 0;
                        const seconds = status.TimeSinceLastSuccess.Seconds || 0;

                        if (days > 0) {
                            timeSinceLastSuccess = `${days}d ${hours}h ${minutes}m`;
                        } else if (hours > 0) {
                            timeSinceLastSuccess = `${hours}h ${minutes}m ${seconds}s`;
                        } else if (minutes > 0) {
                            timeSinceLastSuccess = `${minutes}m ${seconds}s`;
                        } else {
                            timeSinceLastSuccess = `${seconds}s`;
                        }
                    }

                    const statusClass = status.Status === 'Running' ? 'badge-success' :
                                      status.Status === 'Failed' ? 'badge-danger' : 'badge-warning';

                    const statusText = status.Status === 'Running' ? window.t['Running'] :
                                     status.Status === 'Failed' ? window.t['Failed'] : window.t['Not Started'];

                    const row = `
                        <tr>
                            <td>${serviceName}</td>
                            <td><span class="badge ${statusClass}">${statusText}</span></td>
                            <td>${lastSuccessTime}</td>
                            <td>${timeSinceLastSuccess}</td>
                            <td>${status.SuccessCount}</td>
                            <td>${status.FailureCount}</td>
                            <td>${status.LastErrorMessage || '-'}</td>
                        </tr>
                    `;
                    tbody.append(row);
                }
            }

            // Initialize auto-refresh controls
            $('#autoRefreshToggle').change(function() {
                autoRefreshEnabled = $(this).prop('checked');
                if (autoRefreshEnabled) {
                    fetchBackgroundServiceStatus();
                } else {
                    clearTimeout(refreshTimer);
                }
            });

            $('#refreshIntervalSelect').change(function() {
                refreshInterval = parseInt($(this).val());
                if (autoRefreshEnabled) {
                    clearTimeout(refreshTimer);
                    fetchBackgroundServiceStatus();
                }
            });

            $('#refreshNowBtn').click(function() {
                clearTimeout(refreshTimer);
                fetchBackgroundServiceStatus();
            });

            // Initial fetch
            fetchBackgroundServiceStatus();
        });
    </script>
}
