<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<location path="." inheritInChildApplications="false">
		<system.webServer>
			<handlers>
				<add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
			</handlers>
			<aspNetCore processPath="dotnet" arguments=".\RazeWinComTr.dll" stdoutLogEnabled="true" stdoutLogFile="c:\\Inetpub\\vhosts\\razewin.com.tr\\app-logs\\stdoutlogs\\stdout" hostingModel="inprocess" />
		</system.webServer>
	</location>
	<system.webServer>
		<tracing>
			<traceFailedRequests>
				<add path="*">
					<traceAreas>
						<add provider="ASP" verbosity="Verbose"/>
						<add provider="ASPNET" areas="AppServices,Infrastructure,Module,Page" verbosity="Verbose"/>
						<add provider="ISAPI Extension" verbosity="Verbose"/>
						<add provider="WWW Server" areas="ANCM,Authentication,Cache,CGI,Compression,Cors,FastCGI,Filter,iisnode,Module,RequestNotifications,RequestRouting,Rewrite,Security,StaticFile,WebSocket" verbosity="Verbose"/>
					</traceAreas>
					<failureDefinitions statusCodes="500"/>
				</add>
			</traceFailedRequests>
		</tracing>
	</system.webServer>
</configuration>
<!--ProjectGuid: df9713f5-7136-4c67-b4ae-d29de0750f67-->