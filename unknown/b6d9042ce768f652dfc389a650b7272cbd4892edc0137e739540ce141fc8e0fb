using RazeWinComTr.Areas.Admin.DbModel;

namespace RazeWinComTr.Areas.Admin.ViewModels.RzwSavings
{
    public class RzwSavingsPlanViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string TermType { get; set; } = string.Empty;
        public int TermDuration { get; set; }
        public decimal InterestRate { get; set; }
        public decimal MinRzwAmount { get; set; }
        public decimal? MaxRzwAmount { get; set; }
        public bool IsActive { get; set; }
        public int DisplayOrder { get; set; }
        public string? Description { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }

        // Statistics
        public int TotalAccounts { get; set; }
        public decimal TotalLockedRzw { get; set; }
        public decimal TotalInterestPaid { get; set; }

        // Display properties
        public string TermDisplayText => TermType switch
        {
            "Daily" => $"{TermDuration} Day(s)",
            "Monthly" => $"{TermDuration} Month(s)",
            "Yearly" => $"{TermDuration} Year(s)",
            _ => $"{TermDuration} {TermType}"
        };

        public string InterestRateDisplayText => $"{(InterestRate * 100).ToString("N8").TrimEnd('0').TrimEnd('.')}%";

        public string StatusDisplayText => IsActive ? "Active" : "Inactive";

        public string MaxAmountDisplayText => MaxRzwAmount?.ToString("N8") ?? "Unlimited";

        public static RzwSavingsPlanViewModel FromEntity(RzwSavingsPlan entity)
        {
            return new RzwSavingsPlanViewModel
            {
                Id = entity.Id,
                Name = entity.Name,
                TermType = entity.TermType,
                TermDuration = entity.TermDuration,
                InterestRate = entity.InterestRate,
                MinRzwAmount = entity.MinRzwAmount,
                MaxRzwAmount = entity.MaxRzwAmount,
                IsActive = entity.IsActive,
                DisplayOrder = entity.DisplayOrder,
                Description = entity.Description,
                CreatedDate = entity.CreatedDate,
                ModifiedDate = entity.ModifiedDate
            };
        }

        public RzwSavingsPlan ToEntity()
        {
            return new RzwSavingsPlan
            {
                Id = Id,
                Name = Name,
                TermType = TermType,
                TermDuration = TermDuration,
                InterestRate = InterestRate,
                MinRzwAmount = MinRzwAmount,
                MaxRzwAmount = MaxRzwAmount,
                IsActive = IsActive,
                DisplayOrder = DisplayOrder,
                Description = Description,
                CreatedDate = CreatedDate,
                ModifiedDate = ModifiedDate
            };
        }
    }
}
