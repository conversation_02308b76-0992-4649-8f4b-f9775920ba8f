using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Resources;

namespace RazeWinComTr.Attributes;

/// <summary>
/// Provides localized required field validation, similar to RequiredAttribute
/// but with culture and localization support.
/// </summary>
[AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter, AllowMultiple = false)]
public class LocalizedRequiredAttribute : RequiredAttribute
{
    private static readonly ResourceManager ResourceManager = new ResourceManager("RazeWinComTr.Areas.Admin.Resources.SharedResource", typeof(LocalizedRequiredAttribute).Assembly);

    public LocalizedRequiredAttribute()
    {
        // Don't set ErrorMessageResourceType and ErrorMessageResourceName
        // We'll override FormatErrorMessage instead
    }

    public override string FormatErrorMessage(string name)
    {
        string? template = ResourceManager.GetString("The {0} field is required.", CultureInfo.CurrentUICulture);

        if (template == null)
        {
            // Fall back to base implementation if resource is not found
            return base.FormatErrorMessage(name);
        }

        return string.Format(CultureInfo.CurrentCulture, template, name);
    }
}
