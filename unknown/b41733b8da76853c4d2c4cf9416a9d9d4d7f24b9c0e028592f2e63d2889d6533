# Adım 5.2.2: Ana <PERSON><PERSON> Layout ve Header (1-2 saat)

## 📋 <PERSON>ım Özeti
RZW Savings ana sayfasının temel layout yapısı, page header, navigation ve breadcrumb bileşenlerinin oluşturulması.

## 🎯 Hedefler
- ✅ Index.cshtml temel yapısı oluşturma
- ✅ Page header ve title
- ✅ Breadcrumb navigation
- ✅ Quick action buttons
- ✅ Responsive layout

## 📊 Yapılacak İşler

### ✅ YAPILACAKLAR

#### 5.2.2.1 Index.cshtml Temel Yapısı

**Dosya**: `src/Areas/MyAccount/Pages/RzwSavings/Index.cshtml`
```html
@page
@model RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.IndexModel
@{
    ViewData["Title"] = Model.ViewModel.PageTitle;
    Layout = "~/Areas/MyAccount/Pages/Shared/_Layout.cshtml";
}

<!-- Page Header -->
<div class="rzw-savings-header">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="/MyAccount">
                    <i class="fas fa-home"></i> @Localizer["Dashboard"]
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="/MyAccount/Wallet">
                    <i class="fas fa-wallet"></i> @Localizer["Wallet"]
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                <i class="fas fa-piggy-bank"></i> @Localizer["RZW Savings"]
            </li>
        </ol>
    </nav>

    <!-- Page Title Section -->
    <div class="page-title-section">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="page-title-content">
                    <h1 class="page-title">
                        <div class="title-icon">
                            <i class="fas fa-piggy-bank"></i>
                        </div>
                        <div class="title-text">
                            <span class="main-title">@Model.ViewModel.PageTitle</span>
                            <small class="title-subtitle">@Model.ViewModel.WelcomeMessage</small>
                        </div>
                    </h1>
                </div>
            </div>
            <div class="col-md-4">
                <div class="page-actions">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary" onclick="refreshDashboard()" title="@Localizer["Refresh Data"]">
                            <i class="fas fa-sync-alt"></i>
                            <span class="d-none d-sm-inline">@Localizer["Refresh"]</span>
                        </button>
                        <a href="/MyAccount/RzwSavings/InterestHistory" class="btn btn-outline-info" title="@Localizer["Interest History"]">
                            <i class="fas fa-history"></i>
                            <span class="d-none d-sm-inline">@Localizer["History"]</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats Bar -->
    <div class="quick-stats-bar">
        <div class="row">
            <div class="col-6 col-md-3">
                <div class="quick-stat">
                    <div class="stat-icon">
                        <i class="fas fa-coins text-primary"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" data-stat="available-balance">
                            @Model.ViewModel.UserRzwBalance.AvailableBalance.ToString("N2")
                        </div>
                        <div class="stat-label">@Localizer["Available RZW"]</div>
                    </div>
                </div>
            </div>
            <div class="col-6 col-md-3">
                <div class="quick-stat">
                    <div class="stat-icon">
                        <i class="fas fa-lock text-warning"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" data-stat="locked-balance">
                            @Model.ViewModel.Dashboard.TotalRzwInSavings.ToString("N2")
                        </div>
                        <div class="stat-label">@Localizer["In Savings"]</div>
                    </div>
                </div>
            </div>
            <div class="col-6 col-md-3">
                <div class="quick-stat">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line text-success"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" data-stat="total-earned">
                            @Model.ViewModel.Dashboard.TotalEarnedInterest.ToString("N2")
                        </div>
                        <div class="stat-label">@Localizer["Total Earned"]</div>
                    </div>
                </div>
            </div>
            <div class="col-6 col-md-3">
                <div class="quick-stat">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-alt text-info"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" data-stat="active-accounts">
                            @Model.ViewModel.Dashboard.ActiveAccountsCount
                        </div>
                        <div class="stat-label">@Localizer["Active Accounts"]</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions-section">
        <div class="row">
            <div class="col-12">
                <div class="quick-actions-container">
                    <h6 class="quick-actions-title">
                        <i class="fas fa-bolt"></i> @Localizer["Quick Actions"]
                    </h6>
                    <div class="quick-actions-grid">
                        @if (Model.ViewModel.HasAvailableBalance)
                        {
                            <a href="/MyAccount/RzwSavings/Create" class="quick-action-card primary">
                                <div class="action-icon">
                                    <i class="fas fa-plus-circle"></i>
                                </div>
                                <div class="action-content">
                                    <div class="action-title">@Localizer["New Savings Account"]</div>
                                    <div class="action-subtitle">@Localizer["Start earning interest"]</div>
                                </div>
                                <div class="action-arrow">
                                    <i class="fas fa-chevron-right"></i>
                                </div>
                            </a>
                        }
                        else
                        {
                            <div class="quick-action-card disabled">
                                <div class="action-icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="action-content">
                                    <div class="action-title">@Localizer["No Available RZW"]</div>
                                    <div class="action-subtitle">@Localizer["Deposit RZW to start saving"]</div>
                                </div>
                            </div>
                        }

                        @if (Model.ViewModel.Dashboard.HasEarnings)
                        {
                            <a href="/MyAccount/RzwSavings/InterestHistory" class="quick-action-card success">
                                <div class="action-icon">
                                    <i class="fas fa-chart-bar"></i>
                                </div>
                                <div class="action-content">
                                    <div class="action-title">@Localizer["Interest History"]</div>
                                    <div class="action-subtitle">@Localizer["View your earnings"]</div>
                                </div>
                                <div class="action-arrow">
                                    <i class="fas fa-chevron-right"></i>
                                </div>
                            </a>
                        }

                        <a href="/MyAccount/Wallet" class="quick-action-card info">
                            <div class="action-icon">
                                <i class="fas fa-wallet"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">@Localizer["Wallet"]</div>
                                <div class="action-subtitle">@Localizer["Manage your RZW"]</div>
                            </div>
                            <div class="action-arrow">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </a>

                        <a href="/Market" class="quick-action-card secondary">
                            <div class="action-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">@Localizer["Buy RZW"]</div>
                                <div class="action-subtitle">@Localizer["Purchase more tokens"]</div>
                            </div>
                            <div class="action-arrow">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Area -->
<div class="rzw-savings-content">
    <!-- Dashboard Widgets Section -->
    <div class="dashboard-widgets-section">
        <!-- Widget'lar buraya gelecek (Adım 5.2.3) -->
    </div>

    <!-- Active Accounts Section -->
    <div class="active-accounts-section">
        <!-- Aktif hesaplar tablosu buraya gelecek (Adım 5.2.4) -->
    </div>

    <!-- Empty State (if no accounts) -->
    @if (!Model.ViewModel.HasActiveAccounts)
    {
        <div class="empty-state-section">
            <!-- Empty state content buraya gelecek -->
        </div>
    }
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="loading-overlay d-none">
    <div class="loading-spinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">@Localizer["Loading..."]</span>
        </div>
        <div class="loading-text">@Localizer["Updating data..."]</div>
    </div>
</div>

@section Scripts {
    <script src="~/js/rzw-savings-index.js"></script>
}

@section Styles {
    <link href="~/css/rzw-savings-index.css" rel="stylesheet" />
}
```

#### ******* CSS Styling

**Dosya**: `src/wwwroot/css/rzw-savings-index.css`
```css
/* RZW Savings Index Page Styles */

/* Page Header */
.rzw-savings-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
}

.rzw-savings-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    pointer-events: none;
}

/* Breadcrumb */
.breadcrumb {
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    padding: 8px 15px;
    margin-bottom: 20px;
}

.breadcrumb-item a {
    color: rgba(255,255,255,0.9);
    text-decoration: none;
    transition: color 0.2s;
}

.breadcrumb-item a:hover {
    color: white;
}

.breadcrumb-item.active {
    color: rgba(255,255,255,0.8);
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: rgba(255,255,255,0.6);
}

/* Page Title */
.page-title-section {
    margin-bottom: 25px;
}

.page-title {
    display: flex;
    align-items: center;
    gap: 15px;
    margin: 0;
    font-weight: 600;
}

.title-icon {
    background: rgba(255,255,255,0.2);
    padding: 15px;
    border-radius: 12px;
    font-size: 1.5rem;
}

.title-text .main-title {
    display: block;
    font-size: 2rem;
    line-height: 1.2;
}

.title-text .title-subtitle {
    display: block;
    font-size: 1rem;
    opacity: 0.9;
    font-weight: 400;
    margin-top: 5px;
}

.page-actions {
    text-align: right;
}

.page-actions .btn {
    border-radius: 8px;
    font-weight: 500;
    border-color: rgba(255,255,255,0.3);
    color: white;
}

.page-actions .btn:hover {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.5);
}

/* Quick Stats Bar */
.quick-stats-bar {
    background: rgba(255,255,255,0.15);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
    backdrop-filter: blur(10px);
}

.quick-stat {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px;
    border-radius: 8px;
    transition: background 0.2s;
}

.quick-stat:hover {
    background: rgba(255,255,255,0.1);
}

.stat-icon {
    font-size: 1.5rem;
    width: 40px;
    text-align: center;
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
    line-height: 1.2;
}

.stat-label {
    font-size: 0.85rem;
    opacity: 0.9;
    margin-top: 2px;
}

/* Quick Actions */
.quick-actions-section {
    background: rgba(255,255,255,0.1);
    border-radius: 12px;
    padding: 20px;
    backdrop-filter: blur(10px);
}

.quick-actions-title {
    color: white;
    font-weight: 600;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.quick-action-card {
    background: rgba(255,255,255,0.15);
    border: 1px solid rgba(255,255,255,0.2);
    border-radius: 10px;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 12px;
    text-decoration: none;
    color: white;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.quick-action-card:hover {
    background: rgba(255,255,255,0.25);
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

.quick-action-card.disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.quick-action-card.disabled:hover {
    transform: none;
    background: rgba(255,255,255,0.15);
}

.action-icon {
    font-size: 1.5rem;
    width: 40px;
    text-align: center;
}

.action-content {
    flex: 1;
}

.action-title {
    font-weight: 600;
    font-size: 0.95rem;
    line-height: 1.2;
}

.action-subtitle {
    font-size: 0.8rem;
    opacity: 0.9;
    margin-top: 2px;
}

.action-arrow {
    font-size: 0.9rem;
    opacity: 0.7;
}

/* Main Content */
.rzw-savings-content {
    position: relative;
}

.dashboard-widgets-section,
.active-accounts-section {
    margin-bottom: 30px;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    text-align: center;
    color: white;
}

.loading-text {
    margin-top: 15px;
    font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .rzw-savings-header {
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .page-title {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .title-text .main-title {
        font-size: 1.5rem;
    }
    
    .page-actions {
        text-align: center;
        margin-top: 15px;
    }
    
    .quick-stats-bar {
        padding: 15px;
    }
    
    .quick-stat {
        padding: 8px;
    }
    
    .stat-value {
        font-size: 1rem;
    }
    
    .quick-actions-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .quick-action-card {
        padding: 12px;
    }
}

@media (max-width: 576px) {
    .rzw-savings-header {
        padding: 15px;
        border-radius: 10px;
    }
    
    .breadcrumb {
        padding: 6px 10px;
        font-size: 0.85rem;
    }
    
    .title-text .main-title {
        font-size: 1.3rem;
    }
    
    .title-text .title-subtitle {
        font-size: 0.9rem;
    }
    
    .quick-stats-bar {
        padding: 12px;
    }
    
    .stat-value {
        font-size: 0.95rem;
    }
    
    .stat-label {
        font-size: 0.8rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}
```

## 📋 Kontrol Listesi

### ✅ YAPILACAKLAR
- [ ] Index.cshtml temel yapısı oluşturma
- [ ] Page header ve breadcrumb ekleme
- [ ] Quick stats bar ekleme
- [ ] Quick actions section ekleme
- [ ] CSS styling ekleme
- [ ] Responsive design testleri

### 🔄 YAPILMAKTA OLANLAR
- (Henüz başlanmadı)

### ✅ YAPILMIŞLAR
- (Henüz tamamlanmadı)

## 🧪 Test Kriterleri

### Layout Tests
- [ ] Page header doğru görüntüleniyor
- [ ] Breadcrumb navigation çalışıyor
- [ ] Quick stats doğru veri gösteriyor
- [ ] Quick actions butonları çalışıyor

### Responsive Tests
- [ ] Mobile cihazlarda düzgün görünüyor
- [ ] Tablet cihazlarda düzgün görünüyor
- [ ] Desktop'ta optimal görünüm

## 📝 Notlar

### Önemli Özellikler
- Gradient header design
- Quick stats overview
- Contextual quick actions
- Responsive breadcrumb
- Loading overlay

### Accessibility
- ARIA labels
- Semantic HTML
- Keyboard navigation
- Screen reader support

### Sonraki Adım
Bu adım tamamlandıktan sonra **Adım 5.2.3: Dashboard Widget'ları** başlayacak.

---
**Tahmini Süre**: 1-2 saat
**Öncelik**: Yüksek
**Bağımlılıklar**: Adım 5.2.1 tamamlanmış olmalı
