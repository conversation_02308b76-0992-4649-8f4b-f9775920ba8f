using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels;

namespace RazeWinComTr.Areas.Admin.Pages.Withdrawal;

public class DeleteModel : PageModel
{
    private readonly WithdrawalService _withdrawalService;
    private readonly IWalletService _walletService;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public DeleteModel(
        WithdrawalService withdrawalService,
        IWalletService walletService,
        IStringLocalizer<SharedResource> localizer)
    {
        _withdrawalService = withdrawalService;
        _walletService = walletService;
        _localizer = localizer;
    }

    public SweetAlert2Message? AlertMessage { get; set; }

    [BindProperty] public int Id { get; set; }

    public DbModel.Withdrawal? Entity { get; set; }
    public string? ErrorMessage { get; set; }

    public async Task<IActionResult> OnGetAsync(int id)
    {
        Id = id;
        Entity = await _withdrawalService.GetByIdAsync(id);

        if (Entity == null) return NotFound();

        ViewData["WarningTitle"] = _localizer["Warning"];
        ViewData["WarningRecord"] = _localizer["This withdrawal record will be permanently deleted"];
        ViewData["WarningBalances"] = _localizer["If this withdrawal was approved, deleting it will not refund the user's balance"];
        ViewData["WarningUnrecoverable"] = _localizer["This action is irreversible"];

        return Page();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        try
        {
            var entity = await _withdrawalService.GetByIdAsync(Id);
            if (entity != null && entity.Status == WithdrawalStatus.Approved) // If approved, refund the user
            {
                var tryWallet = await _walletService.GetByUserIdAndCoinIdAsync(entity.UserId, 1); // Assuming TRY has ID 1
                if (tryWallet != null)
                {
                    tryWallet.Balance += entity.WithdrawalAmount;
                    tryWallet.ModifiedDate = DateTime.UtcNow;
                    await _walletService.UpdateAsync(tryWallet);
                }
            }

            await _withdrawalService.DeleteAsync(Id);

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Success"],
                Text = _localizer["Deletion was successful"],
                Icon = "success",
                RedirectUrl = "/Admin/Withdrawal"
            };

            return Page();
        }
        catch (Exception ex)
        {
            ErrorMessage = _localizer["An error occurred: {0}", ex.Message].Value;
            Entity = await _withdrawalService.GetByIdAsync(Id);
            return Page();
        }
    }
}
