using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels;

namespace RazeWinComTr.Areas.Admin.Pages.Package;

public class EditModel : PageModel
{
    private readonly PackageService _packageService;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public EditModel(
        PackageService packageService,
        IStringLocalizer<SharedResource> localizer)
    {
        _packageService = packageService;
        _localizer = localizer;
    }

    public SweetAlert2Message? AlertMessage { get; set; }

    [BindProperty] public PackageEditViewModel Entity { get; set; } = new();

    public string? ErrorMessage { get; set; }

    public async Task<IActionResult> OnGetAsync(int id)
    {
        var entity = await _packageService.GetByIdAsync(id);

        if (entity == null) return NotFound();
        Entity = new PackageEditViewModel
        {
            Id = entity.Id,
            Name = entity.Name,
            Price = entity.Price,
            Description = entity.Description,
            Benefits = entity.Benefits,
            InviteLimit = entity.InviteLimit,
            EarningsCap = entity.EarningsCap,
            IsActive = entity.IsActive,
            Order = entity.Order
        };

        return Page();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        try
        {
            if (!ModelState.IsValid) return Page();
            var userId = User.GetClaimUserId();
            if (!userId.HasValue) return Unauthorized();
            var entity = await _packageService.GetByIdAsync(Entity.Id);
            if (entity == null) return NotFound();

            entity.Name = Entity.Name;
            entity.Price = Entity.Price;
            entity.Description = Entity.Description;
            entity.Benefits = Entity.Benefits;
            entity.InviteLimit = Entity.InviteLimit;
            entity.EarningsCap = Entity.EarningsCap;
            entity.IsActive = Entity.IsActive;
            entity.Order = Entity.Order;
            entity.ModifiedDate = DateTime.UtcNow;
            await _packageService.UpdateAsync(entity);

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Success"],
                Text = _localizer["Successfully updated"],
                Icon = "success",
                RedirectUrl = "/Admin/Package"
            };

            return Page();
        }
        catch (Exception ex)
        {
            ErrorMessage = ex.Message;
            return Page();
        }
    }
}

public class PackageEditViewModel
{
    public int Id { get; set; }
    public string Name { get; set; } = null!;
    public decimal Price { get; set; }
    public string? Description { get; set; }
    public string? Benefits { get; set; }
    public int? InviteLimit { get; set; }
    public decimal? EarningsCap { get; set; }
    public bool IsActive { get; set; }
    public int Order { get; set; }
}
