@page
@model RazeWinComTr.Areas.Admin.Pages.Deposit.DistributeRewardsModel
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.DbModel
@using RazeWinComTr.Areas.Admin.Enums
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["Distribute Rewards"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["Distribute Rewards"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/Admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="/Admin/Deposit">@L["Payments"]</a></li>
                    <li class="breadcrumb-item active">@L["Distribute Rewards"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        @if (Model.AlertMessage != null)
        {
            <script id="scriptMessage" type="text/javascript">
                window.onload = function () {
                    Swal.fire({
                        title: '@Html.Raw(Model.AlertMessage.Title)',
                        text: '@Html.Raw(Model.AlertMessage.Text)',
                        icon: '@Html.Raw(Model.AlertMessage.Icon)'
                    }).then((result) => {
                        var redirectUrl = '@(Model.AlertMessage.RedirectUrl ?? string.Empty)';
                        if (result.isConfirmed && redirectUrl !== '') {
                            location.href = redirectUrl;
                        }
                    });
                    $('#scriptMessage').remove();
                }
            </script>
        }

        <div class="row">
            <div class="col-md-6">
                <div class="card card-primary">
                    <div class="card-header">
                        <h3 class="card-title">@L["Payment Details"]</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label>@L["Payment ID"]</label>
                            <input type="text" class="form-control" value="@Model.Id" readonly />
                        </div>
                        <div class="form-group">
                            <label>@L["User"]</label>
                            <input type="text" class="form-control" value="@Model.UserFullName (@Model.UserEmail)" readonly />
                        </div>
                        <div class="form-group">
                            <label>@L["Amount"]</label>
                            <input type="text" class="form-control" value="@Model.Deposit?.Amount.ToString("N2") @L["Currency_Symbol"]" readonly />
                        </div>
                        <div class="form-group">
                            <label>@L["Payment Date"]</label>
                            <input type="text" class="form-control" value="@Model.Deposit?.CreatedDate.ToLocalTime().ToString("dd.MM.yyyy HH:mm")" readonly />
                        </div>
                        <div class="form-group">
                            <label>@L["Status"]</label>
                            <input type="text" class="form-control" value="@(Model.Deposit?.Status == DepositStatus.Approved ? L["Approved"] : Model.Deposit?.Status == DepositStatus.Pending ? L["Pending"] : L["Rejected"])" readonly />
                        </div>
                        <div class="form-group">
                            <label>@L["Reward Status"]</label>
                            <input type="text" class="form-control" value="@(Model.Deposit?.RewardStatus == DepositRewardStatus.Pending ? L["Pending"] :
                                                                                                                                                Model.Deposit?.RewardStatus == DepositRewardStatus.Processed ? L["Processed"] :
                                                                                                                                                Model.Deposit?.RewardStatus == DepositRewardStatus.Distributed ? L["Distributed"] :
                                                                                                                                                Model.Deposit?.RewardStatus == DepositRewardStatus.NoRewards ? L["No Rewards"] :
                                                                                                                                                L["Failed"])" readonly />
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card card-info">
                    <div class="card-header">
                        <h3 class="card-title">@L["Reward Distribution Information"]</h3>
                    </div>
                    <div class="card-body">
                        <p>@L["When you distribute rewards for this payment:"]</p>
                        <ul>
                            <li>@L["The system will find all users in the referral chain of the user who made this payment."]</li>
                            <li>@L["For each referrer with an active package, the system will calculate a reward based on the package's reward percentage."]</li>
                            <li>@L["The reward will be added to each referrer's RZW wallet."]</li>
                        </ul>
                        <div class="alert alert-warning">
                            <h5><i class="icon fas fa-exclamation-triangle"></i> @L["A record of each reward will be stored in the system."]</h5>
                            <p>@L["Important!"]</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @if (Model.RewardPreview != null && Model.RewardPreview.RewardedUsersCount > 0)
        {
            <div class="row">
                <div class="col-12">
                    <div class="card card-success">
                        <div class="card-header">
                            <h3 class="card-title">@L["Reward Preview"]</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-box bg-gradient-info">
                                        <span class="info-box-icon"><i class="fas fa-users"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">@L["Users to be Rewarded"]</span>
                                            <span class="info-box-number">@Model.RewardPreview.RewardedUsersCount</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="info-box bg-gradient-success">
                                        <span class="info-box-icon"><i class="fas fa-coins"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">@L["Total RZW to Distribute"]</span>
                                            <span class="info-box-number">@Model.RewardPreview.TotalRzwDistributed.ToString("0.########")</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="info-box bg-gradient-warning">
                                        <span class="info-box-icon"><i class="fas fa-lira-sign"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">@L["Total TL to Distribute"]</span>
                                            <span class="info-box-number">@Model.RewardPreview.TotalTlDistributed.ToString("0.########") @L["Currency_Symbol"]</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>@L["User"]</th>
                                        <th>@L["Package"]</th>
                                        <th>@L["Level"]</th>
                                        <th>@L["Percentage"] (RZW)</th>
                                        <th>@L["Percentage"] (TL)</th>
                                        <th>@L["RZW Price at Distribution"]</th>
                                        <th>@L["Amount (RZW)"]</th>
                                        <th>@L["Value (TRY)"]</th>
                                        <th>@L["Amount (TL)"]</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var reward in Model.RewardPreview.Rewards)
                                    {
                                        <tr>
                                            <td>@reward.UserFullName (@reward.UserEmail)</td>
                                            <td>@reward.PackageName</td>
                                            <td>@reward.Level</td>
                                            <td>@reward.RzwPercentage.ToString("N2")%</td>
                                            <td>@reward.TlPercentage.ToString("N2")%</td>
                                            <td>@reward.RzwBuyPrice.ToString("N2")</td>
                                            <td>@reward.RzwAmount.ToString("N8")</td>
                                            <td>@NumberFormatHelper.FormatDecimalWithThousandSeparator(@reward.TryValue) @L["Currency_Symbol"]</td>
                                            <td>@NumberFormatHelper.FormatDecimalWithThousandSeparator(@reward.TlAmount) @L["Currency_Symbol"]</td>
                                        </tr>
                                    }
                                </tbody>
                                <tfoot>
                                    <tr class="bg-light">
                                        <th colspan="6">@L["Total"]</th>
                                        <th>@Model.RewardPreview.TotalRzwDistributed.ToString("N8")</th>
                                        <th>@NumberFormatHelper.FormatDecimalWithThousandSeparator(Model.RewardPreview.TotalTryValue) @L["Currency_Symbol"]</th>
                                        <th>@NumberFormatHelper.FormatDecimalWithThousandSeparator(Model.RewardPreview.TotalTlDistributed) @L["Currency_Symbol"]</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        }
        else if (Model.RewardPreview != null && Model.RewardPreview.RewardedUsersCount == 0)
        {
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-info">
                        <h5><i class="icon fas fa-info"></i> @L["No Rewards to Distribute"]</h5>
                        <p>@L["There are no users eligible for rewards in the referral chain for this payment."]</p>
                    </div>
                </div>
            </div>
        }

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <a href="/Admin/Deposit" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> @L["Reward distribution cannot be undone. Make sure this payment is legitimate before proceeding."]
                        </a>
                        @if (Model.Deposit?.Status == DepositStatus.Approved &&
                                                (Model.Deposit?.RewardStatus == DepositRewardStatus.Pending ||
                                                Model.Deposit?.RewardStatus == DepositRewardStatus.Failed))
                        {
                            <form method="post" class="d-inline">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-gift"></i> @L["Distribute Rewards"]
                                </button>
                            </form>
                        }
                        @if (Model.Deposit?.RewardStatus == DepositRewardStatus.Distributed ||
                                                Model.Deposit?.RewardStatus == DepositRewardStatus.NoRewards)
                        {
                            <a href="/Admin/Deposit/RewardSummary?id=@Model.Id" class="btn btn-success">
                                <i class="fas fa-chart-pie"></i> @L["View Reward Summary"]
                            </a>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
