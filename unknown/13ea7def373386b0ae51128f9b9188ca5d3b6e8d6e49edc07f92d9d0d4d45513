using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;

namespace RazeWinComTr.Areas.Admin.ViewModels.RzwSavings
{
    public class RzwSavingsAccountViewModel
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string UserEmail { get; set; } = string.Empty;
        public int PlanId { get; set; }
        public string PlanName { get; set; } = string.Empty;
        public decimal RzwAmount { get; set; }
        public decimal InterestRate { get; set; }
        public string TermType { get; set; } = string.Empty;
        public int TermDuration { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime MaturityDate { get; set; }
        public string Status { get; set; } = string.Empty;
        public decimal TotalEarnedRzw { get; set; }
        public DateTime? LastInterestDate { get; set; }
        public bool AutoRenew { get; set; }
        public decimal EarlyWithdrawalPenalty { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }

        // Computed properties
        public bool IsActive => Status == RzwSavingsStatus.Active;
        public bool IsMatured => DateTime.UtcNow >= MaturityDate && Status == RzwSavingsStatus.Active;
        public int DaysHeld => (DateTime.UtcNow - StartDate).Days;
        public int DaysToMaturity => Math.Max(0, (MaturityDate - DateTime.UtcNow).Days);
        public decimal CurrentBalance => RzwAmount + TotalEarnedRzw;

        // Display properties
        public string StatusDisplayText => Status switch
        {
            RzwSavingsStatus.Active => "Active",
            RzwSavingsStatus.Matured => "Matured",
            RzwSavingsStatus.Withdrawn => "Withdrawn",
            RzwSavingsStatus.Cancelled => "Cancelled",
            _ => Status
        };

        public string TermDisplayText => TermType switch
        {
            "Daily" => $"{TermDuration} Day(s)",
            "Monthly" => $"{TermDuration} Month(s)",
            "Yearly" => $"{TermDuration} Year(s)",
            _ => $"{TermDuration} {TermType}"
        };

        public string InterestRateDisplayText => $"{(InterestRate * 100).ToString("N8").TrimEnd('0').TrimEnd('.')}%";

        public string ProgressPercentage
        {
            get
            {
                if (Status != RzwSavingsStatus.Active) return "100";
                var progressPercentage = RzwSavingsCalculationHelper.CalculatePreciseProgressPercentage(StartDate, MaturityDate);
                return progressPercentage.ToString("N1", System.Globalization.CultureInfo.InvariantCulture);
            }
        }

        public string StatusBadgeClass => Status switch
        {
            RzwSavingsStatus.Active => "badge-success",
            RzwSavingsStatus.Matured => "badge-info",
            RzwSavingsStatus.Withdrawn => "badge-warning",
            RzwSavingsStatus.Cancelled => "badge-danger",
            _ => "badge-secondary"
        };

        public static RzwSavingsAccountViewModel FromEntity(RzwSavingsAccount entity, string userEmail = "", string planName = "")
        {
            return new RzwSavingsAccountViewModel
            {
                Id = entity.Id,
                UserId = entity.UserId,
                UserEmail = userEmail,
                PlanId = entity.PlanId,
                PlanName = planName,
                RzwAmount = entity.RzwAmount,
                InterestRate = entity.InterestRate,
                TermType = entity.TermType,
                TermDuration = entity.TermDuration,
                StartDate = entity.StartDate,
                MaturityDate = entity.MaturityDate,
                Status = entity.Status,
                TotalEarnedRzw = entity.TotalEarnedRzw,
                LastInterestDate = entity.LastInterestDate,
                AutoRenew = entity.AutoRenew,
                EarlyWithdrawalPenalty = entity.EarlyWithdrawalPenalty,
                CreatedDate = entity.CreatedDate,
                ModifiedDate = entity.ModifiedDate
            };
        }
    }
}
