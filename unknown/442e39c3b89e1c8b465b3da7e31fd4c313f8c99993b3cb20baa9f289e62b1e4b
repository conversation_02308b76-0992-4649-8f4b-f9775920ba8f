@page
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.DbModel
@using RazeWinComTr.Areas.Admin.Helpers
@using RazeWinComTr.Areas.Admin.Services
@inject SettingService _settingService
@inject IStringLocalizer<SharedResource> Localizer
@model RazeWinComTr.Areas.MyAccount.Pages.DepositCryptoModel
@{
    var btcAddress = "**********************************";
    var usdtTrc20Address = "TMTxRJ43YaCgoodxiT1EFjzY9Yz7t3k3HV";
    var bnbBep20Address = "******************************************";
}

<style>
    .deposit-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px 15px;
    }

    .deposit-card {
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        background-color: #fff;
        margin-bottom: 30px;
    }

    .deposit-card .card-header {
        background: linear-gradient(135deg, #3a7bd5, #00d2ff);
        color: white;
        padding: 16px 20px;
        font-size: 1.2rem;
        border-bottom: none;
    }

    .deposit-card .card-header a {
        color: white !important;
        font-weight: bold;
        text-decoration: none;
        margin-right: 10px;
        transition: all 0.3s ease;
    }

    .deposit-card .card-header a:hover {
        transform: translateX(-3px);
    }

    .deposit-card .card-body {
        padding: 25px;
    }

    .deposit-card .card-footer {
        background-color: #f8f9fa;
        border-top: 1px solid #eee;
        padding: 15px 25px;
    }

    .crypto-address-container {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .crypto-address {
        font-family: monospace;
        font-size: 14px;
        word-break: break-all;
    }

    .copy-field {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }

    .field-label {
        font-weight: bold;
        min-width: 120px;
    }

    .field-value {
        flex-grow: 1;
        padding: 8px 12px;
        background-color: #f0f0f0;
        border-radius: 4px;
        margin-right: 10px;
        font-family: monospace;
        word-break: break-all;
    }

    .copy-btn {
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 6px 10px;
        cursor: pointer;
        transition: all 0.2s;
    }

    .copy-btn:hover {
        background-color: #0069d9;
    }

    .qr-code {
        text-align: center;
        margin: 15px 0;
    }

    .qr-code img {
        max-width: 150px;
        height: auto;
    }

    .nav-tabs .nav-link {
        color: #495057;
        background-color: #f8f9fa;
        border-color: #dee2e6 #dee2e6 #fff;
        margin-right: 5px;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
    }

    .nav-tabs .nav-link.active {
        color: #007bff;
        background-color: #fff;
        border-color: #dee2e6 #dee2e6 #fff;
        font-weight: bold;
    }

    .tab-content {
        border: 1px solid #dee2e6;
        border-top: none;
        padding: 20px;
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
    }

    .warning-text {
        color: #dc3545;
        font-weight: bold;
    }

    .form-section {
        margin-top: 30px;
        border-top: 1px solid #eee;
        padding-top: 20px;
    }

    .amount-input-group {
        display: flex;
        align-items: center;
    }

    .amount-input-group .form-control {
        border-radius: 4px;
        margin-right: 10px;
    }

    .amount-input-group .input-group-text {
        background-color: #f8f9fa;
        border-color: #ced4da;
    }
</style>

<div class="deposit-container">
    <div class="deposit-card">
        <div class="card-header">
            <a href="/MyAccount/Deposit" class="back-link">
                <i class="fas fa-chevron-left"></i>
            </a>
            <span>@Localizer["Cryptocurrency Deposit"]</span>
        </div>

        <div class="card-body">
            <div class="alert alert-warning" role="alert">
                <strong class="d-block mb-2">@Localizer["Important"]</strong>
                <p class="mb-0">@Localizer["Please make sure to send your cryptocurrency to the correct network address. Sending to the wrong network may result in permanent loss of funds."]</p>
            </div>

            @if (!string.IsNullOrEmpty(Model.SuccessMessage))
            {
                <div class="alert alert-success" role="alert">
                    @Model.SuccessMessage
                </div>
            }

            @if (!string.IsNullOrEmpty(Model.ErrorMessage))
            {
                <div class="alert alert-danger" role="alert">
                    @Model.ErrorMessage
                </div>
            }

            <ul class="nav nav-tabs" id="cryptoTabs" role="tablist">
                <li role="presentation" class="@(Model.DepositInfo.CoinType == "BTC" ? "active" : "")">
                    <a href="#btc" id="btc-tab" aria-controls="btc" role="tab" data-toggle="tab">
                        <img src="/site/images/deposit/bitcoin.png" alt="BTC" style="width: 20px; height: 20px; margin-right: 5px;"> Bitcoin (BTC)
                    </a>
                </li>
                <li role="presentation" class="@(Model.DepositInfo.CoinType == "USDT-TRC20" ? "active" : "")">
                    <a href="#usdt" id="usdt-tab" aria-controls="usdt" role="tab" data-toggle="tab">
                        <img src="/site/images/deposit/tether.png" alt="USDT" style="width: 20px; height: 20px; margin-right: 5px;"> USDT (TRC20)
                    </a>
                </li>
                <li role="presentation" class="@(Model.DepositInfo.CoinType == "BNB-BEP20" ? "active" : "")">
                    <a href="#bnb" id="bnb-tab" aria-controls="bnb" role="tab" data-toggle="tab">
                        <img src="/site/images/deposit/bnb.png" alt="BNB" style="width: 20px; height: 20px; margin-right: 5px;"> BNB (BEP20)
                    </a>
                </li>
            </ul>

            <div class="tab-content" id="cryptoTabsContent">
                <!-- Bitcoin Tab -->
                <div class="tab-pane @(Model.DepositInfo.CoinType == "BTC" ? "active" : "")" id="btc" role="tabpanel">
                    <h5 class="mb-3">@Localizer["Bitcoin (BTC) Deposit Address"]</h5>
                    <div class="crypto-address-container">
                        <div class="copy-field">
                            <span class="field-label">@Localizer["BTC Address"]:</span>
                            <span class="field-value" id="btc-address">@btcAddress</span>
                            <button type="button" class="copy-btn" onclick="copyToClipboard('btc-address')" title="@Localizer["Copy"]">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        <div class="qr-code">
                            <img src="https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=@btcAddress&bgcolor=ffffff" alt="BTC QR Code">
                            <p class="mt-2 mb-0">@Localizer["Scan to copy address"]</p>
                        </div>
                    </div>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <strong>@Localizer["Warning"]:</strong> @Localizer["Only send Bitcoin (BTC) to this address. Sending any other cryptocurrency may result in permanent loss."]
                    </div>
                </div>

                <!-- USDT Tab -->
                <div class="tab-pane @(Model.DepositInfo.CoinType == "USDT-TRC20" ? "active" : "")" id="usdt" role="tabpanel">
                    <h5 class="mb-3">@Localizer["USDT (TRC20) Deposit Address"]</h5>
                    <div class="crypto-address-container">
                        <div class="copy-field">
                            <span class="field-label">@Localizer["USDT Address"]:</span>
                            <span class="field-value" id="usdt-address">@usdtTrc20Address</span>
                            <button type="button" class="copy-btn" onclick="copyToClipboard('usdt-address')" title="@Localizer["Copy"]">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        <div class="qr-code">
                            <img src="https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=@usdtTrc20Address&bgcolor=ffffff" alt="USDT QR Code">
                            <p class="mt-2 mb-0">@Localizer["Scan to copy address"]</p>
                        </div>
                    </div>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <strong>@Localizer["Warning"]:</strong> @Localizer["Only send USDT on the TRC20 network to this address. Sending USDT on other networks or other cryptocurrencies may result in permanent loss."]
                    </div>
                </div>

                <!-- BNB Tab -->
                <div class="tab-pane @(Model.DepositInfo.CoinType == "BNB-BEP20" ? "active" : "")" id="bnb" role="tabpanel">
                    <h5 class="mb-3">@Localizer["BNB (BEP20) Deposit Address"]</h5>
                    <div class="crypto-address-container">
                        <div class="copy-field">
                            <span class="field-label">@Localizer["BNB Address"]:</span>
                            <span class="field-value" id="bnb-address">@bnbBep20Address</span>
                            <button type="button" class="copy-btn" onclick="copyToClipboard('bnb-address')" title="@Localizer["Copy"]">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        <div class="qr-code">
                            <img src="https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=@bnbBep20Address&bgcolor=ffffff" alt="BNB QR Code">
                            <p class="mt-2 mb-0">@Localizer["Scan to copy address"]</p>
                        </div>
                    </div>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <strong>@Localizer["Warning"]:</strong> @Localizer["Only send BNB on the BEP20 (Smart Chain) network to this address. Sending BNB on other networks or other cryptocurrencies may result in permanent loss."]
                    </div>
                </div>
            </div>

            <div class="form-section">
                <h4>@Localizer["Report Your Deposit"]</h4>
                <p>@Localizer["After sending cryptocurrency to one of the addresses above, please fill out this form to report your deposit."]</p>

                <form id="cryptoDepositForm" method="post">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                    <div class="form-group">
                        <label asp-for="DepositInfo.CoinType">@Localizer["Cryptocurrency Type"]</label>
                        <select asp-for="DepositInfo.CoinType" class="form-control">
                            <option value="BTC">Bitcoin (BTC)</option>
                            <option value="USDT-TRC20">USDT (TRC20)</option>
                            <option value="BNB-BEP20">BNB (BEP20)</option>
                        </select>
                        <span asp-validation-for="DepositInfo.CoinType" class="text-danger"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="DepositInfo.TransactionHash">@Localizer["Transaction Hash/ID"]</label>
                        <input asp-for="DepositInfo.TransactionHash" class="form-control" placeholder="@Localizer["Enter the transaction hash/ID from your wallet"]" />
                        <span asp-validation-for="DepositInfo.TransactionHash" class="text-danger"></span>
                        <small class="form-text text-muted">@Localizer["This is the unique identifier for your transaction on the blockchain."]</small>
                    </div>

                    <div class="form-group">
                        <label asp-for="DepositInfo.AmountInteger">@Localizer["Amount"]</label>
                        <div class="amount-input-group">
                            <input asp-for="DepositInfo.AmountInteger" class="form-control" placeholder="0" />
                            <span>.</span>
                            <input asp-for="DepositInfo.AmountFraction" class="form-control" style="width: 100px;" placeholder="00000000" />
                        </div>
                        <span asp-validation-for="DepositInfo.AmountInteger" class="text-danger"></span>
                        <span asp-validation-for="DepositInfo.AmountFraction" class="text-danger"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="DepositInfo.SenderAddress">@Localizer["Sender Address"] (@Localizer["Optional"])</label>
                        <input asp-for="DepositInfo.SenderAddress" class="form-control" placeholder="@Localizer["Enter the address you sent from"]" />
                        <span asp-validation-for="DepositInfo.SenderAddress" class="text-danger"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="DepositInfo.Notes">@Localizer["Additional Notes (Optional)"]</label>
                        <textarea asp-for="DepositInfo.Notes" class="form-control" rows="3" placeholder="@Localizer["Any additional information about your deposit"]"></textarea>
                        <span asp-validation-for="DepositInfo.Notes" class="text-danger"></span>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane mr-1"></i> @Localizer["Submit Deposit Report"]
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;

            navigator.clipboard.writeText(text).then(function() {
                // Show success message
                Swal.fire({
                    title: '@Localizer["Copied!"]',
                    text: '@Localizer["Address copied to clipboard"]',
                    icon: 'success',
                    timer: 1500,
                    showConfirmButton: false
                });
            }, function() {
                // Fallback for older browsers
                const textarea = document.createElement('textarea');
                textarea.value = text;
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);

                Swal.fire({
                    title: '@Localizer["Copied!"]',
                    text: '@Localizer["Address copied to clipboard"]',
                    icon: 'success',
                    timer: 1500,
                    showConfirmButton: false
                });
            });
        }

        // Handle tab switching based on selected cryptocurrency
        $(document).ready(function() {
            $('#DepositInfo_CoinType').change(function() {
                const selectedCoin = $(this).val();

                if (selectedCoin === 'BTC') {
                    $('#btc-tab').tab('show');
                } else if (selectedCoin === 'USDT-TRC20') {
                    $('#usdt-tab').tab('show');
                } else if (selectedCoin === 'BNB-BEP20') {
                    $('#bnb-tab').tab('show');
                }
            });

            // Handle tab changes to update the dropdown
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                const tabId = $(e.target).attr('id');

                if (tabId === 'btc-tab') {
                    $('#DepositInfo_CoinType').val('BTC');
                } else if (tabId === 'usdt-tab') {
                    $('#DepositInfo_CoinType').val('USDT-TRC20');
                } else if (tabId === 'bnb-tab') {
                    $('#DepositInfo_CoinType').val('BNB-BEP20');
                }
            });

            // Handle decimal input
            $('#DepositInfo_AmountInteger').on('keydown', function(e) {
                if (e.key === '.' || e.key === ',') {
                    e.preventDefault();
                    $('#DepositInfo_AmountFraction').focus();
                }
            });
        });
    </script>
}
