﻿﻿using System.Text.Json.Serialization;

namespace RazeWinComTr.Areas.Admin.ViewModels.Common
{
    /// <summary>
    /// Represents a trading pair from an exchange API
    /// </summary>
    public class PairViewModel
    {
        /// <summary>
        /// The pair code used for API requests (e.g., "BTCTRY")
        /// </summary>
        [JsonPropertyName("pairCode")]
        public string PairCode { get; set; } = string.Empty;

        /// <summary>
        /// The display name for the pair (e.g., "Bitcoin - TRY")
        /// </summary>
        [JsonPropertyName("displayName")]
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// The base currency symbol (e.g., "BTC")
        /// </summary>
        [JsonPropertyName("baseCurrency")]
        public string BaseCurrency { get; set; } = string.Empty;

        /// <summary>
        /// The quote currency symbol (e.g., "TRY")
        /// </summary>
        [JsonPropertyName("quoteCurrency")]
        public string QuoteCurrency { get; set; } = string.Empty;
    }
}
