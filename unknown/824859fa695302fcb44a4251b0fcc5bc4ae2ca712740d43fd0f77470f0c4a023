﻿﻿using System.ComponentModel.DataAnnotations;

namespace RazeWinComTr.ViewModels.Market
{
    /// <summary>
    /// ViewModel for the coin list API endpoint, containing only the properties needed by the client
    /// </summary>
    public class CoinListViewModel
    {
        /// <summary>
        /// The unique identifier for the market/coin
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// The coin code (e.g., BTC)
        /// </summary>
        [StringLength(50)]
        public string Coin { get; set; } = string.Empty;

        /// <summary>
        /// The full name of the coin (e.g., Bitcoin)
        /// </summary>
        [StringLength(255)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// The buy price of the coin
        /// </summary>
        public decimal BuyPrice { get; set; }

        /// <summary>
        /// The buy price of the coin formatted as a string without rounding
        /// </summary>
        public string BuyPriceFormatted { get; set; } = string.Empty;

        /// <summary>
        /// The sell price of the coin
        /// </summary>
        public decimal SellPrice { get; set; }

        /// <summary>
        /// The sell price of the coin formatted as a string without rounding
        /// </summary>
        public string SellPriceFormatted { get; set; } = string.Empty;

        /// <summary>
        /// The 24-hour price change percentage
        /// </summary>
        public decimal? Change24h { get; set; }

        /// <summary>
        /// The number of decimal places to display for this coin
        /// </summary>
        public int DecimalPlaces { get; set; }

        /// <summary>
        /// The URL to the coin's image
        /// </summary>
        [StringLength(255)]
        public string? IconUrl { get; set; }

        /// <summary>
        /// The pair code used for external services (e.g., BTCTRY)
        /// </summary>
        [StringLength(50)]
        public string PairCode { get; set; } = string.Empty;

        /// <summary>
        /// The timestamp of the last price update
        /// </summary>
        public DateTime? LastPriceUpdate { get; set; }
    }
}
