@page
@using Microsoft.Extensions.Localization
@model EditModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["Edit Wallet"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

@if (Model.AlertMessage != null)
{
    <script id="scriptMessage" type="text/javascript">
        window.onload = function () {
            Swal.fire({
                title: '@Html.Raw(Model.AlertMessage.Title)',
                text: '@Html.Raw(Model.AlertMessage.Text)',
                icon: '@Html.Raw(Model.AlertMessage.Icon)'
            }).then((result) => {
                var redirectUrl = '@(Model.AlertMessage.RedirectUrl ?? string.Empty)';
                if (result.isConfirmed && redirectUrl !== '') {
                    location.href = redirectUrl;
                }
            });
            $('#scriptMessage').remove();
        }
    </script>
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["Edit Wallet"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="/admin/wallet">@L["Wallets"]</a></li>
                    <li class="breadcrumb-item active">@L["Edit"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>
<div asp-validation-summary="ModelOnly" class="text-danger"></div>
<section class="content">
    <div class="container-fluid">
        <div class="card">
            <form method="post">
                <input type="hidden" asp-for="Entity.Id"/>
                <input type="hidden" asp-for="Entity.UserId"/>
                <input type="hidden" asp-for="Entity.CoinId"/>
                <div class="card-body">
                    <div class="form-group">
                        <label>@L["User"]</label>
                        <input type="text" class="form-control" value="@Model.UserEmail" readonly />
                    </div>
                    <div class="form-group">
                        <label>@L["Coin"]</label>
                        <input type="text" class="form-control" value="@Model.CoinName" readonly />
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.Balance">@L["Balance"]</label>
                        <input asp-for="Entity.Balance" class="form-control" type="number" step="0.00000001" required/>
                    </div>
                </div>
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-1"></i> @L["Save"]
                    </button>
                    <a href="/Admin/Wallet" class="btn btn-default">
                        <i class="fas fa-times mr-1"></i> @L["Cancel"]
                    </a>
                </div>
            </form>
        </div>
    </div>
</section>
