using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels;

namespace RazeWinComTr.Areas.Admin.Pages.Deposit;

public class EditModel : PageModel
{
    private readonly DepositService _depositService;
    private readonly AppDbContext _context;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public EditModel(
        DepositService depositService,
        AppDbContext context,
        IStringLocalizer<SharedResource> localizer)
    {
        _depositService = depositService;
        _context = context;
        _localizer = localizer;
    }

    public SweetAlert2Message? AlertMessage { get; set; }

    [BindProperty] public DepositEditViewModel Entity { get; set; } = new();

    public string UserEmail { get; set; } = string.Empty;
    public decimal UserBalance { get; set; }
    public string DepositType { get; set; } = string.Empty;
    public string ExtraData { get; set; } = string.Empty;
    public DateTime CreatedDate { get; set; }

    // Cryptocurrency specific properties
    public bool IsCryptoDeposit { get; set; }
    public string CryptoType { get; set; } = string.Empty;
    public string TransactionHash { get; set; } = string.Empty;
    public string SenderAddress { get; set; } = string.Empty;
    public int? CoinId { get; set; }
    public string CoinCode { get; set; } = string.Empty;



    public async Task<IActionResult> OnGetAsync(int id)
    {
        var entity = await _depositService.GetByIdAsync(id);

        if (entity == null) return NotFound();

        // Check if payment is already approved or rejected
        if (entity.Status == DepositStatus.Approved || entity.Status == DepositStatus.Rejected)
        {
            // Use localized message
            TempData["ErrorMessage"] = _localizer["Approved or rejected payments cannot be edited."].Value;
            return RedirectToPage("./Index");
        }

        Entity = new DepositEditViewModel
        {
            Id = entity.Id,
            UserId = entity.UserId,
            Amount = entity.Amount,
            Status = (int)entity.Status,
            PreviousStatus = (int)entity.Status
        };

        var user = await _context.Users.FirstOrDefaultAsync(u => u.UserId == entity.UserId);
        UserEmail = user?.Email ?? _localizer["Unknown"].Value;
        UserBalance = user?.Balance ?? 0;
        DepositType = entity.DepositType ?? _localizer["Not specified"].Value;
        ExtraData = entity.ExtraData ?? _localizer["No additional data"].Value;
        CreatedDate = entity.CreatedDate;

        // Check if this is a cryptocurrency deposit
        IsCryptoDeposit = entity.DepositType?.StartsWith("Crypto -") ?? false;

        // Parse cryptocurrency information if available
        if (IsCryptoDeposit && !string.IsNullOrEmpty(entity.ExtraData))
        {
            try
            {
                var cryptoData = System.Text.Json.JsonSerializer.Deserialize<CryptoDepositExtraData>(entity.ExtraData);
                if (cryptoData != null)
                {
                    CryptoType = cryptoData.CoinType;
                    TransactionHash = cryptoData.TransactionHash;
                    SenderAddress = cryptoData.SenderAddress;
                    CoinId = cryptoData.CoinId;
                    CoinCode = cryptoData.CoinCode;

                    // Get the coin name
                    var coin = await _context.Markets.FirstOrDefaultAsync(m => m.Id == CoinId);
                    if (coin != null)
                    {
                        CoinCode = coin.Coin;
                    }
                }
            }
            catch
            {
                // If we can't parse the data, just continue without the crypto-specific information
            }
        }

        return Page();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        try
        {
            if (!ModelState.IsValid) return Page();
            var userId = User.GetClaimUserId();
            if (!userId.HasValue) return Unauthorized();

            var entity = await _depositService.GetByIdAsync(Entity.Id);
            if (entity == null) return NotFound();

            // Double-check if payment is already approved or rejected (security measure)
            if (entity.Status == DepositStatus.Approved || entity.Status == DepositStatus.Rejected)
            {
                // Use localized message
                TempData["ErrorMessage"] = _localizer["Approved or rejected payments cannot be edited."].Value;
                return RedirectToPage("./Index");
            }

            // Update amount
            entity.Amount = Entity.Amount;

            // Check if status has changed
            if (Entity.Status != Entity.PreviousStatus)
            {
                // Update status and handle balance changes
                bool success = await _depositService.UpdateDepositStatusAsync(Entity.Id, (DepositStatus)Entity.Status);
                if (!success)
                {
                    ModelState.AddModelError("", _localizer["Failed to update payment status"]);
                    return Page();
                }
            }
            else
            {
                // Just update the amount if status hasn't changed
                await _depositService.UpdateAsync(entity);
            }

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Success"],
                Text = _localizer["Successfully updated"],
                Icon = "success",
                RedirectUrl = "/Admin/Deposit"
            };

            return Page();
        }
        catch (Exception ex)
        {
            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Error"],
                Text = _localizer["An error occurred: {0}", ex.Message],
                Icon = "error"
            };
            return Page();
        }
    }
}

public class DepositEditViewModel
{
    public int Id { get; set; }
    public int UserId { get; set; }
    public decimal Amount { get; set; }
    public int Status { get; set; }
    public int PreviousStatus { get; set; }
}

/// <summary>
/// Data class for cryptocurrency deposit extra data
/// </summary>
public class CryptoDepositExtraData
{
    public string CoinType { get; set; } = string.Empty;
    public string TransactionHash { get; set; } = string.Empty;
    public string SenderAddress { get; set; } = string.Empty;
    public int CoinId { get; set; }
    public string CoinCode { get; set; } = string.Empty;
}
