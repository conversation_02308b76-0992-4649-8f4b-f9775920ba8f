using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.ViewModels.Bank;
using System.Text.RegularExpressions;

namespace RazeWinComTr.Areas.Admin.Services;

public class BankService
{
    private readonly AppDbContext _context;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public BankService(IStringLocalizer<SharedResource> localizer, AppDbContext context)
    {
        _localizer = localizer;
        _context = context;
    }

    public async Task<Bank?> GetByIdAsync(int id)
    {
        return await _context.Banks
            .FirstOrDefaultAsync(r => r.Id == id);
    }

    public async Task<List<BankViewModel>> GetListAsync()
    {
        return await _context.Banks
            .Select(r => new BankViewModel
            {
                Id = r.Id,
                BankName = r.BankName,
                AccountHolder = r.AccountHolder,
                Iban = r.Iban,
                IsActive = r.IsActive,
                Order = r.Order,
                CreatedDate = r.CreatedDate,
                ModifiedDate = r.ModifiedDate
            })
            .OrderBy(p => p.Order)
            .ToListAsync();
    }

    public async Task<List<BankViewModel>> GetActiveListAsync()
    {
        return await _context.Banks
            .Where(r => r.IsActive)
            .Select(r => new BankViewModel
            {
                Id = r.Id,
                BankName = r.BankName,
                AccountHolder = r.AccountHolder,
                Iban = r.Iban,
                IsActive = r.IsActive,
                Order = r.Order,
                CreatedDate = r.CreatedDate,
                ModifiedDate = r.ModifiedDate
            })
            .OrderBy(p => p.Order)
            .ToListAsync();
    }

    public async Task DeleteAsync(int id)
    {
        var entity = await _context.Banks.FindAsync(id);
        if (entity != null)
        {
            _context.Banks.Remove(entity);
            await _context.SaveChangesAsync();
        }
    }

    /// <summary>
    /// Validates an IBAN number
    /// </summary>
    /// <param name="iban">The IBAN to validate</param>
    /// <returns>Tuple with validation result and error message if validation fails</returns>
    public (bool IsValid, string? ErrorMessage) ValidateIban(string iban)
    {
        // Check if IBAN is null or empty
        if (string.IsNullOrWhiteSpace(iban))
        {
            return (false, _localizer["IBAN is required"].Value);
        }

        // Remove any spaces or special characters
        iban = iban.Replace(" ", "").Replace("-", "");

        // Check if IBAN matches the pattern: 2 letters followed by 2 digits and then 1-30 alphanumeric characters
        if (!Regex.IsMatch(iban, @"^[A-Z]{2}[0-9]{2}[A-Z0-9]{1,30}$"))
        {
            return (false, _localizer["Please enter a valid IBAN format (e.g., **************************)"].Value);
        }

        // Additional validation could be added here, such as country-specific rules or checksum validation

        return (true, null);
    }

    public async Task<Bank> CreateAsync(Bank bank)
    {
        // Validate IBAN
        var (isValid, errorMessage) = ValidateIban(bank.Iban);
        if (!isValid)
        {
            throw new InvalidOperationException(errorMessage);
        }

        // Standardize IBAN format (remove spaces, uppercase)
        bank.Iban = bank.Iban.Replace(" ", "").ToUpper();

        _context.Banks.Add(bank);
        await _context.SaveChangesAsync();
        return bank;
    }

    public async Task UpdateAsync(Bank bank)
    {
        // Validate IBAN
        var (isValid, errorMessage) = ValidateIban(bank.Iban);
        if (!isValid)
        {
            throw new InvalidOperationException(errorMessage);
        }

        // Standardize IBAN format (remove spaces, uppercase)
        bank.Iban = bank.Iban.Replace(" ", "").ToUpper();

        _context.Entry(bank).State = EntityState.Modified;
        await _context.SaveChangesAsync();
    }
}
