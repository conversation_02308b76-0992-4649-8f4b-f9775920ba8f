using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RazeWinComTr.Areas.Admin.Constants;

namespace RazeWinComTr.Areas.MyAccount.Pages;

[AllowAnonymous] // Allow anonymous access to the logout page
public class LogoutModel : PageModel
{
    public async Task<IActionResult> OnGetAsync()
    {
        // Clear all session data
        HttpContext.Session.Clear();

        // Sign out the user by removing the authentication cookie
        await HttpContext.SignOutAsync(AuthConstants.UserAuthenticationScheme);

        // Redirect to the home page
        return RedirectToPage("/Index", new { area = "" });
    }
}
