# RazeWin Reward Distribution System Tests

This project contains unit tests for the RazeWin reward distribution system. The tests are designed to verify that the reward distribution system works correctly in various scenarios.

## Test Categories

1. **Basic Tests** (`ReferralRewardTests.cs`)
   - Tests for basic reward calculation functionality
   - Tests for handling of edge cases like zero amounts, zero percentages, etc.

2. **Distribution Tests** (`RewardDistributionTests.cs`)
   - Tests for reward distribution in various scenarios
   - Tests for handling of referral chains, multiple package types, etc.
   - Tests for handling of different RZW prices

3. **Edge Case Tests** (`RewardDistributionEdgeCaseTests.cs`)
   - Tests for extreme edge cases like zero amounts, circular referrals, etc.
   - Tests for handling of decimal precision with very large and very small amounts
   - Tests for handling of empty referral chains

## Running the Tests

To run the tests, use the following command:

```
dotnet test
```

## Test Coverage

The tests cover the following aspects of the reward distribution system:

- Calculation of reward amounts based on percentages
- Distribution of rewards to referrers in a referral chain
- Handling of different package types and reward percentages
- Handling of missing reward percentages for certain levels
- Handling of circular referral chains
- Handling of very large and very small amounts
- Handling of empty referral chains
- Handling of different RZW prices

## Implementation Notes

The tests are designed to be independent of the actual implementation of the reward distribution system. They test the core logic and algorithms used in the system, rather than the specific implementation details.

This approach allows the tests to be used as a reference for implementing the reward distribution system in the actual codebase, and also allows the tests to be used to verify that the implementation is correct.

## Next Steps

1. Integrate these tests with the actual implementation of the reward distribution system
2. Add more tests for specific edge cases and scenarios
3. Add integration tests that test the complete reward distribution flow
4. Add performance tests to ensure the system can handle large numbers of referrers and rewards
