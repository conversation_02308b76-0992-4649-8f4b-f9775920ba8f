using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Formats.Jpeg;
using SixLabors.ImageSharp.Processing;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;

namespace RazeWinComTr.Areas.Admin.Pages.Download;

public class IndexByFileIdModel : PageModel
{
    private readonly AppDbContext _context;

    private readonly FileService _fileService;

    public IndexByFileIdModel(AppDbContext context, FileService fileService)
    {
        _context = context;
        _fileService = fileService;
    }

    public async Task<IActionResult> OnGetAsync(string fileType, string id, string? size) //todo AUTH
    {
        if (string.IsNullOrEmpty(fileType)) return NotFound();
        if (string.IsNullOrEmpty(id)) return NotFound();
        var parsedIdStr = int.TryParse(id, out var genericId);
        if (!parsedIdStr) return BadRequest("Parse Error");
        var fileRelativePath = "";
        //TODO
        //if (fileType == nameof(ProductFile))
        //{
        //    var entity = await _context.ProductFiles
        //        .Include(p => p.Product)
        //        .Where(p => p.ProductFileId == genericId)
        //        .FirstOrDefaultAsync();
        //    if (entity == null) return NotFound();

        //    fileRelativePath = entity.FileUrl;
        //}
        //else if (fileType == nameof(ProjectFile))
        //{
        //    var entity = await _context.ProjectFiles
        //        .Include(p => p.Project)
        //        .Where(p => p.ProjectFileId == genericId)
        //        .FirstOrDefaultAsync();
        //    if (entity == null) return NotFound();

        //    fileRelativePath = entity.FileUrl;
        //}
        //else if (fileType == nameof(Reference))
        //{
        //    var entity = await _context.References
        //        .Where(p => p.ReferenceId == genericId)
        //        .Select(p => new { p.OrganizationLogoUrl })
        //        .FirstOrDefaultAsync();
        //    if (entity == null) return NotFound();

        //    fileRelativePath = entity.OrganizationLogoUrl;
        //}
        //else if (fileType == nameof(DbModel.Content))
        //{
        //    var entity = await _context.Contents
        //        .Where(p => p.ContentId == genericId)
        //        .Select(p => new { p.ImageUrl })
        //        .FirstOrDefaultAsync();
        //    if (entity == null) return NotFound();

        //    fileRelativePath = entity.ImageUrl;
        //}
        if (fileType == nameof(DbModel.Market))
        {
            var entity = await _context.Markets
                .Where(p => p.Id == genericId)
                .Select(p => new { p.IconUrl })
                .FirstOrDefaultAsync();
            if (entity == null) return NotFound();

            fileRelativePath = entity.IconUrl;
        }

        if (fileRelativePath == null) return NotFound();
        if (string.IsNullOrEmpty(_fileService.FileStoragePath)) return NotFound(); // Return 404

        // Resolve the full file path by combining the storage path with the relative filename
        var filePath = Path.Combine(_fileService.FileStoragePath, fileRelativePath);

        // Normalize the file path to prevent directory traversal
        var fullFilePath = Path.Combine(_fileService.FileStoragePath, filePath);
        var fileName = Path.GetFileName(fullFilePath); // Ensure only the file name is returned

        // Check if the file exists
        if (!System.IO.File.Exists(fullFilePath)) return NotFound(); // Return 404 if the file doesn't exist

        // Return the file content with the appropriate MIME type
        var fileBytes = await System.IO.File.ReadAllBytesAsync(fullFilePath);
        if (size != null)
        {
            var sizeArr = size.Split('x');
            if (sizeArr.Length != 2) return BadRequest("Invalid size parameter");
            var width = int.Parse(sizeArr[0]);
            var height = int.Parse(sizeArr[1]);
            using (var image = Image.Load(fileBytes)) // Load from byte array
            {
                // Resize the image to a thumbnail (200x200 for example)
                var thumbnail = image.Clone(ctx => ctx.Resize(width, height));

                // Compress the thumbnail to reduce file size
                using (var thumbnailStream = new MemoryStream())
                {
                    // Save the thumbnail image to the memory stream
                    await thumbnail.SaveAsync(thumbnailStream,
                        new JpegEncoder { Quality = 50 }); // 50% quality for smaller file size

                    // Ensure the stream's position is set to the beginning before using it
                    thumbnailStream.Position = 0;
                    // Return the thumbnail with cache headers
                    Response.Headers.CacheControl = "public, max-age=86400"; // Cache for 30 days
                    Response.Headers.Expires = DateTime.UtcNow.AddDays(1).ToString("R");
                    var contentType = ContentTypesHelper.GetImageContentType(fileName);
                    // Return the thumbnail as a JPEG image
                    return File(thumbnailStream.ToArray(), contentType, fileName);
                }
            }
        }


        return File(fileBytes, "application/octet-stream", fileName);
    }
}