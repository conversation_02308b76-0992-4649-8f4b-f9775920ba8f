<#
.SYNOPSIS
    Language resource file management tool for multilingual applications.

.DESCRIPTION
    This script provides functions to manage language resource files (.resx) in a multilingual application.
    It can add new language keys, verify consistency across language files, and report on the status of language files.
    The script supports any number of languages and ensures all language files contain the same keys.

.PARAMETER ResourceDir
    The directory containing the resource files. Default is "../Areas/Admin/Resources".

.PARAMETER Command
    The command to execute. Available commands:
    - add: Add a new language key to all resource files
    - verify: Verify that all resource files contain the same keys
    - sync: Synchronize all resource files to ensure they contain the same keys
    - report: Generate a report on the status of language files

.PARAMETER Key
    The language key to add (used with 'add' command).

.PARAMETER DefaultValue
    The default value for the key (used with 'add' command). This will be used for all languages if no specific value is provided.

.PARAMETER Values
    A hashtable of language-specific values (used with 'add' command). Format: @{"en"="English Value"; "tr"="Turkish Value"; "fr"="French Value"}

.PARAMETER Comment
    An optional comment for the key (used with 'add' command).

.EXAMPLE
    # Add a new key with specific values for each language
    .\LanguageManager.ps1 -Command add -Key "Welcome" -Values @{"en"="Welcome"; "tr"="Hoş Geldiniz"; "fr"="Bienvenue"} -Comment "Welcome message"

.EXAMPLE
    # Add a new key with a default value for all languages
    .\LanguageManager.ps1 -Command add -Key "Submit" -DefaultValue "Submit" -Comment "Submit button text"

.EXAMPLE
    # Verify that all resource files contain the same keys
    .\LanguageManager.ps1 -Command verify

.EXAMPLE
    # Synchronize all resource files to ensure they contain the same keys
    .\LanguageManager.ps1 -Command sync

.EXAMPLE
    # Generate a report on the status of language files
    .\LanguageManager.ps1 -Command report
#>

param (
    [Parameter(Mandatory = $false)]
    [string]$ResourceDir = "../Areas/Admin/Resources",

    [Parameter(Mandatory = $false)]
    [ValidateSet("add", "verify", "sync", "report")]
    [string]$Command = "add",

    [Parameter(Mandatory = $false)]
    [string]$Key,

    [Parameter(Mandatory = $false)]
    [string]$DefaultValue,

    [Parameter(Mandatory = $false)]
    [hashtable]$Values = @{},

    [Parameter(Mandatory = $false)]
    [string]$Comment
)

# Get the script directory to use as a reference point
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Write-Host "Script directory: $scriptDir"

# Resolve the resource directory path
try {
    # If ResourceDir is a relative path, resolve it relative to the script directory
    if (-not [System.IO.Path]::IsPathRooted($ResourceDir)) {
        $ResourceDir = Join-Path -Path $scriptDir -ChildPath $ResourceDir
    }

    # Ensure the path exists
    if (-not (Test-Path $ResourceDir)) {
        Write-Error "Resource directory not found: $ResourceDir"
        Write-Host "Current script directory: $scriptDir"
        exit 1
    }

    # Get the absolute path
    $ResourceDir = (Resolve-Path $ResourceDir).Path
    Write-Host "Resource directory resolved to: $ResourceDir"
} catch {
    Write-Error "Error resolving resource directory: $_"
    exit 1
}

# Function to get language code from file name
function Get-LanguageCode {
    param (
        [string]$FileName
    )

    # Default language is English
    $langCode = "en"

    # Extract language code from file name (e.g., SharedResource.tr.resx -> tr)
    if ($FileName -match "\.([a-z]{2})\.resx$") {
        $langCode = $matches[1]
    }

    return $langCode
}

# Function to check if a key exists in a resource file
function Test-KeyExists {
    param (
        [string]$FilePath,
        [string]$Key
    )

    if (-not (Test-Path $FilePath)) {
        return $false
    }

    $content = Get-Content $FilePath -Raw
    return $content -match "<data\s+name=`"$Key`"\s+xml:space=`"preserve`">"
}

# Function to get all keys from a resource file
function Get-ResourceKeys {
    param (
        [string]$FilePath
    )

    if (-not (Test-Path $FilePath)) {
        return @()
    }

    $xml = New-Object System.Xml.XmlDocument
    $xml.Load($FilePath)
    $dataNodes = $xml.SelectNodes("//data")

    $keys = @()
    foreach ($node in $dataNodes) {
        $keys += $node.GetAttribute("name")
    }

    return $keys
}

# Function to add a key to a resource file
function Add-LanguageKey {
    param (
        [string]$FilePath,
        [string]$Key,
        [string]$Value,
        [string]$Comment
    )

    # Check if the key already exists
    if (Test-KeyExists -FilePath $FilePath -Key $Key) {
        Write-Warning "Key '$Key' already exists in $FilePath. Skipping."
        return
    }

    # Load the XML file
    $xml = New-Object System.Xml.XmlDocument
    $xml.Load($FilePath)

    # Create the new data element
    $dataElement = $xml.CreateElement("data")
    $dataElement.SetAttribute("name", $Key)
    $dataElement.SetAttribute("xml:space", "preserve")

    # Create the value element
    $valueElement = $xml.CreateElement("value")
    $valueElement.InnerText = $Value
    $dataElement.AppendChild($valueElement) | Out-Null

    # Add comment if provided
    if (-not [string]::IsNullOrEmpty($Comment)) {
        $commentElement = $xml.CreateElement("comment")
        $commentElement.InnerText = $Comment
        $dataElement.AppendChild($commentElement) | Out-Null
    }

    # Find the root element
    $rootElement = $xml.SelectSingleNode("//root")

    # Add the new data element
    $rootElement.AppendChild($dataElement) | Out-Null

    # Sort the data elements alphabetically by name attribute
    $dataNodes = $rootElement.SelectNodes("data")
    $sortedDataNodes = $dataNodes | Sort-Object { $_.GetAttribute("name") }

    # Remove all data nodes
    foreach ($node in $dataNodes) {
        $rootElement.RemoveChild($node) | Out-Null
    }

    # Add sorted data nodes back
    foreach ($node in $sortedDataNodes) {
        $rootElement.AppendChild($node) | Out-Null
    }

    # Save the XML file with UTF-8 encoding
    $xmlSettings = New-Object System.Xml.XmlWriterSettings
    $xmlSettings.Encoding = [System.Text.Encoding]::UTF8
    $xmlSettings.Indent = $true

    $xmlWriter = [System.Xml.XmlWriter]::Create($FilePath, $xmlSettings)
    $xml.Save($xmlWriter)
    $xmlWriter.Close()

    Write-Host "Added key '$Key' to $FilePath"
}

# Function to get the value of a key from a resource file
function Get-KeyValue {
    param (
        [string]$FilePath,
        [string]$Key
    )

    if (-not (Test-Path $FilePath)) {
        return $null
    }

    $xml = New-Object System.Xml.XmlDocument
    $xml.Load($FilePath)
    $dataNode = $xml.SelectSingleNode("//data[@name='$Key']")

    if ($null -ne $dataNode) {
        $valueNode = $dataNode.SelectSingleNode("value")
        if ($null -ne $valueNode) {
            return $valueNode.InnerText
        }
    }

    return $null
}

# Function to verify that all resource files contain the same keys
function Test-ResourceConsistency {
    param (
        [string]$ResourceDir
    )

    $resourceFiles = Get-ChildItem -Path $ResourceDir -Filter "*.resx" -Recurse

    if ($resourceFiles.Count -eq 0) {
        Write-Warning "No resource files found in $ResourceDir"
        return $false
    }

    # Get all keys from the first file (reference)
    $referenceFile = $resourceFiles[0]
    $referenceKeys = Get-ResourceKeys -FilePath $referenceFile.FullName
    $referenceLanguage = Get-LanguageCode -FileName $referenceFile.Name

    Write-Host "Reference file: $($referenceFile.Name) ($referenceLanguage) with $($referenceKeys.Count) keys"

    $allConsistent = $true
    $missingKeysReport = @{}

    # Check each file against the reference
    foreach ($file in $resourceFiles) {
        if ($file.FullName -eq $referenceFile.FullName) {
            continue
        }

        $fileKeys = Get-ResourceKeys -FilePath $file.FullName
        $language = Get-LanguageCode -FileName $file.Name

        Write-Host "Checking file: $($file.Name) ($language) with $($fileKeys.Count) keys"

        # Find missing keys
        $missingKeys = $referenceKeys | Where-Object { $_ -notin $fileKeys }

        if ($missingKeys.Count -gt 0) {
            $allConsistent = $false
            $missingKeysReport[$file.Name] = $missingKeys
            Write-Warning "File $($file.Name) is missing $($missingKeys.Count) keys"
        }

        # Find extra keys
        $extraKeys = $fileKeys | Where-Object { $_ -notin $referenceKeys }

        if ($extraKeys.Count -gt 0) {
            $allConsistent = $false
            Write-Warning "File $($file.Name) has $($extraKeys.Count) extra keys"
        }
    }

    if ($allConsistent) {
        Write-Host "All resource files are consistent" -ForegroundColor Green
    } else {
        Write-Host "Resource files are not consistent" -ForegroundColor Yellow

        # Print missing keys report
        foreach ($file in $missingKeysReport.Keys) {
            Write-Host "Missing keys in ${file}:" -ForegroundColor Yellow
            foreach ($key in $missingKeysReport[$file]) {
                Write-Host "  - $key" -ForegroundColor Yellow
            }
        }
    }

    return $allConsistent
}

# Function to synchronize all resource files
function Sync-ResourceFiles {
    param (
        [string]$ResourceDir,
        [switch]$AutoTranslate = $false
    )

    $resourceFiles = Get-ChildItem -Path $ResourceDir -Filter "*.resx" -Recurse

    if ($resourceFiles.Count -eq 0) {
        Write-Warning "No resource files found in $ResourceDir"
        return
    }

    # Get all keys from all files
    $allKeys = @{}
    $fileLanguages = @{}
    $baseNameToFiles = @{}
    $languageGroups = @{}

    foreach ($file in $resourceFiles) {
        $language = Get-LanguageCode -FileName $file.Name
        $fileLanguages[$file.FullName] = $language

        # Group files by base name (e.g., SharedResource)
        $baseName = $file.Name -replace "\.[a-z]{2}\.resx$", ".resx"
        $baseName = $baseName -replace "\.resx$", ""

        if (-not $baseNameToFiles.ContainsKey($baseName)) {
            $baseNameToFiles[$baseName] = @()
        }
        $baseNameToFiles[$baseName] += $file

        # Group languages
        if (-not $languageGroups.ContainsKey($baseName)) {
            $languageGroups[$baseName] = @{}
        }
        $languageGroups[$baseName][$language] = $file.FullName

        $fileKeys = Get-ResourceKeys -FilePath $file.FullName
        foreach ($key in $fileKeys) {
            if (-not $allKeys.ContainsKey($key)) {
                $allKeys[$key] = @{}
            }

            $value = Get-KeyValue -FilePath $file.FullName -Key $key
            $allKeys[$key][$language] = $value
        }
    }

    Write-Host "Found $($allKeys.Count) unique keys across all resource files"

    # Process each base name group separately
    foreach ($baseName in $baseNameToFiles.Keys) {
        Write-Host "Processing resource group: $baseName" -ForegroundColor Cyan
        $languages = $languageGroups[$baseName].Keys

        # Find the reference language (prefer English, then first available)
        $referenceLanguage = "en"
        if (-not $languages.Contains("en")) {
            $referenceLanguage = $languages | Select-Object -First 1
        }

        Write-Host "Using $referenceLanguage as reference language for $baseName"

        # Add missing keys to each file in this group
        foreach ($language in $languages) {
            $filePath = $languageGroups[$baseName][$language]
            $fileKeys = Get-ResourceKeys -FilePath $filePath
            $missingCount = 0

            foreach ($key in $allKeys.Keys) {
                if ($key -notin $fileKeys) {
                    # Determine the value to use
                    $value = $null
                    $comment = "Auto-synchronized key"

                    # Strategy 1: Try to use the value from the same language in another file group
                    if ($null -eq $value) {
                        foreach ($otherBaseName in $baseNameToFiles.Keys) {
                            if ($otherBaseName -ne $baseName -and
                                $languageGroups[$otherBaseName].ContainsKey($language) -and
                                $allKeys[$key].ContainsKey($language)) {
                                $value = $allKeys[$key][$language]
                                $comment = "Auto-synchronized from another file group"
                                break
                            }
                        }
                    }

                    # Strategy 2: Try to use the value from the reference language in this file group
                    if ($null -eq $value -and $allKeys[$key].ContainsKey($referenceLanguage)) {
                        $refValue = $allKeys[$key][$referenceLanguage]

                        # If the language is not the reference language, mark it as needing translation
                        if ($language -ne $referenceLanguage) {
                            $value = $refValue
                            $comment = "Needs translation from $referenceLanguage"
                        } else {
                            $value = $refValue
                        }
                    }

                    # Strategy 3: Use any available value from any language
                    if ($null -eq $value -and $allKeys[$key].Count -gt 0) {
                        $firstLang = $allKeys[$key].Keys | Select-Object -First 1
                        $value = $allKeys[$key][$firstLang]
                        $comment = "Needs translation from $firstLang"
                    }

                    # Strategy 4: Last resort - use a placeholder indicating translation is needed
                    if ($null -eq $value) {
                        $value = "[TRANSLATION NEEDED]"
                        $comment = "Translation needed for key: $key"
                    }

                    Add-LanguageKey -FilePath $filePath -Key $key -Value $value -Comment $comment
                    $missingCount++
                }
            }

            if ($missingCount -gt 0) {
                Write-Host "Added $missingCount missing keys to $language resource file in $baseName" -ForegroundColor Yellow
            } else {
                Write-Host "No missing keys in $language resource file in $baseName" -ForegroundColor Green
            }
        }
    }

    Write-Host "All resource files have been synchronized" -ForegroundColor Green
    Write-Host "Note: Keys marked with [TRANSLATION NEEDED] or 'Needs translation' comments should be reviewed and properly translated" -ForegroundColor Yellow
}

# Function to generate a report on the status of language files
function Get-ResourceReport {
    param (
        [string]$ResourceDir
    )

    $resourceFiles = Get-ChildItem -Path $ResourceDir -Filter "*.resx" -Recurse

    if ($resourceFiles.Count -eq 0) {
        Write-Warning "No resource files found in $ResourceDir"
        return
    }

    $report = @{
        TotalFiles = $resourceFiles.Count
        Languages = @{}
        TotalUniqueKeys = 0
        KeysPerLanguage = @{}
        MissingKeys = @{}
    }

    # Get all keys from all files
    $allKeys = @{}

    foreach ($file in $resourceFiles) {
        $language = Get-LanguageCode -FileName $file.Name

        if (-not $report.Languages.ContainsKey($language)) {
            $report.Languages[$language] = 0
            $report.KeysPerLanguage[$language] = 0
            $report.MissingKeys[$language] = @()
        }

        $report.Languages[$language]++

        $fileKeys = Get-ResourceKeys -FilePath $file.FullName
        $report.KeysPerLanguage[$language] += $fileKeys.Count

        foreach ($key in $fileKeys) {
            if (-not $allKeys.ContainsKey($key)) {
                $allKeys[$key] = @()
            }

            $allKeys[$key] += $language
        }
    }

    $report.TotalUniqueKeys = $allKeys.Count

    # Find missing keys for each language
    foreach ($key in $allKeys.Keys) {
        foreach ($language in $report.Languages.Keys) {
            if ($language -notin $allKeys[$key]) {
                $report.MissingKeys[$language] += $key
            }
        }
    }

    # Print report
    Write-Host "Resource Files Report" -ForegroundColor Cyan
    Write-Host "--------------------" -ForegroundColor Cyan
    Write-Host "Total resource files: $($report.TotalFiles)" -ForegroundColor Cyan
    Write-Host "Total unique keys: $($report.TotalUniqueKeys)" -ForegroundColor Cyan
    Write-Host "Languages:" -ForegroundColor Cyan

    foreach ($language in $report.Languages.Keys) {
        Write-Host "  - ${language}: $($report.Languages[$language]) files, $($report.KeysPerLanguage[$language]) keys" -ForegroundColor Cyan

        if ($report.MissingKeys[$language].Count -gt 0) {
            Write-Host "    Missing $($report.MissingKeys[$language].Count) keys" -ForegroundColor Yellow
        } else {
            Write-Host "    No missing keys" -ForegroundColor Green
        }
    }

    # Return the report object for further processing
    return $report
}

# Function to add multiple language keys at once
function Add-MultipleLanguageKeys {
    param (
        [string]$ResourceDir,
        [hashtable]$KeysAndValues,
        [string]$DefaultComment = ""
    )

    if ($KeysAndValues.Count -eq 0) {
        Write-Warning "No keys provided to add"
        return
    }

    $resourceFiles = Get-ChildItem -Path $ResourceDir -Filter "*.resx" -Recurse

    if ($resourceFiles.Count -eq 0) {
        Write-Warning "No resource files found in $ResourceDir"
        return
    }

    $addedCount = 0
    $skippedCount = 0

    foreach ($key in $KeysAndValues.Keys) {
        $keyValues = $KeysAndValues[$key]
        $defaultValue = ""
        $values = @{}
        $comment = $DefaultComment

        # Extract values from the key values
        if ($keyValues -is [hashtable]) {
            if ($keyValues.ContainsKey("DefaultValue")) {
                $defaultValue = $keyValues["DefaultValue"]
            }

            if ($keyValues.ContainsKey("Values") -and $keyValues["Values"] -is [hashtable]) {
                $values = $keyValues["Values"]
            }

            if ($keyValues.ContainsKey("Comment")) {
                $comment = $keyValues["Comment"]
            }
        } elseif ($keyValues -is [string]) {
            $defaultValue = $keyValues
        }

        # Skip if no default value and no values
        if ([string]::IsNullOrEmpty($defaultValue) -and $values.Count -eq 0) {
            Write-Warning "Skipping key '$key' because no values were provided"
            $skippedCount++
            continue
        }

        $keyAddedToAnyFile = $false

        foreach ($file in $resourceFiles) {
            $filePath = $file.FullName
            $language = Get-LanguageCode -FileName $file.Name

            # Skip if key already exists in this file
            if (Test-KeyExists -FilePath $filePath -Key $key) {
                continue
            }

            # Determine which value to use
            $value = $defaultValue

            if ($values.ContainsKey($language)) {
                $value = $values[$language]
            }

            Add-LanguageKey -FilePath $filePath -Key $key -Value $value -Comment $comment
            $keyAddedToAnyFile = $true
        }

        if ($keyAddedToAnyFile) {
            $addedCount++
        }
    }

    Write-Host "Added $addedCount language keys to resource files. Skipped $skippedCount keys." -ForegroundColor Green
}

# Find all resource files
$resourceFiles = Get-ChildItem -Path $ResourceDir -Filter "*.resx" -Recurse

# Execute the requested command
switch ($Command) {
    "add" {
        if ([string]::IsNullOrEmpty($Key)) {
            Write-Error "Key parameter is required for 'add' command"
            exit 1
        }

        if ([string]::IsNullOrEmpty($DefaultValue) -and $Values.Count -eq 0) {
            Write-Error "Either DefaultValue or Values parameter is required for 'add' command"
            exit 1
        }

        foreach ($file in $resourceFiles) {
            $filePath = $file.FullName
            $language = Get-LanguageCode -FileName $file.Name

            # Determine which value to use
            $value = $DefaultValue

            if ($Values.ContainsKey($language)) {
                $value = $Values[$language]
            }

            Add-LanguageKey -FilePath $filePath -Key $Key -Value $value -Comment $Comment
        }

        Write-Host "Language key '$Key' has been added to all resource files." -ForegroundColor Green
    }

    "add-multiple" {
        if ($null -eq $KeysAndValues -or $KeysAndValues.Count -eq 0) {
            Write-Error "KeysAndValues parameter is required for 'add-multiple' command"
            exit 1
        }

        Add-MultipleLanguageKeys -ResourceDir $ResourceDir -KeysAndValues $KeysAndValues -DefaultComment $Comment
    }

    "verify" {
        Test-ResourceConsistency -ResourceDir $ResourceDir
    }

    "sync" {
        Sync-ResourceFiles -ResourceDir $ResourceDir
    }

    "report" {
        Get-ResourceReport -ResourceDir $ResourceDir
    }
}
