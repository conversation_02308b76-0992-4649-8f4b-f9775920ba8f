﻿namespace RazeWinComTr.Areas.Admin.Services;

public class FileService
{
    public FileService(IConfiguration configuration)
    {
        FileStoragePath = configuration.GetValue<string?>("FileStoragePath");
    }

    public string? FileStoragePath { get; }

    public string? GetFilePath(string filename)
    {
        if (string.IsNullOrWhiteSpace(FileStoragePath)) return null;
        var filePath = Path.Combine(FileStoragePath, filename);
        if (File.Exists(filePath)) return filePath;
        return null; // File not found
    }

    //public static string getDownloadUrl(string relativeFilePath)
    //{
    //    return "/api/DeviceFile/download?file=" + Uri.EscapeDataString(relativeFilePath);
    //}

    //public static string getThumbnailUrl(string relativeFilePath)
    //{
    //    return "/api/DeviceFile/Thumbnail?file=" + Uri.EscapeDataString(relativeFilePath);
    //}

    public static string getGenericThumbnailUrl(string fileType, string id)
    {
        return $"/Admin/Download/ThumbnailByFileId?fileType={fileType}&id={id}";
    }

    public static string getGenericDownloadUrl(string fileType, string id, string? size = null)
    {
        return $"/Admin/Download/IndexByFileId?fileType={fileType}&id={id}&size={size}";
    }

    public static string getAnonymousFileUrl(string fileType, int id, string? size = null)
    {
        return $"/Admin/Download/IndexAnonymousByFileId?fileType={fileType}&id={id}&size={size}";
    }
}