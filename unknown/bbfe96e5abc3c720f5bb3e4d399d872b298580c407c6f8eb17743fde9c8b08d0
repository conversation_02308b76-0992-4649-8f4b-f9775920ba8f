using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.Common;
using System.Net;

namespace RazeWinComTr.BackgroundServices;

/// <summary>
/// Base class for cryptocurrency exchange background services
/// </summary>
public abstract class CryptoExchangeBackgroundService : BackgroundService
{
    protected readonly ILogger _logger;
    protected readonly IServiceScopeFactory _serviceScopeFactory;
    protected readonly string _serviceName;
    protected readonly TimeSpan _interval;
    protected readonly TimeSpan _retryAfterDefaultDelay;
    protected readonly int _priceThresholdSeconds;
    protected DateTime _nextAllowedRequestTime = DateTime.MinValue;
    protected bool _isRunning = false;

    protected CryptoExchangeBackgroundService(
        ILogger logger,
        IServiceScopeFactory serviceScopeFactory,
        IOptions<CryptoExchangeBackgroundServiceOptions> options,
        string serviceName)
    {
        _logger = logger;
        _serviceScopeFactory = serviceScopeFactory;
        _serviceName = serviceName;
        _interval = TimeSpan.FromSeconds(options.Value.UpdateInterval);
        _retryAfterDefaultDelay = TimeSpan.FromSeconds(options.Value.RetryAfterDefaultDelay);
        _priceThresholdSeconds = options.Value.PriceThresholdSeconds;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("{ServiceName} background service is starting", _serviceName);

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                // Check if we're allowed to make a request yet
                if (DateTime.UtcNow < _nextAllowedRequestTime)
                {
                    var waitTime = _nextAllowedRequestTime - DateTime.UtcNow;
                    _logger.LogInformation("{ServiceName} is rate limited. Waiting for {WaitTime} before next request",
                        _serviceName, waitTime);
                    await Task.Delay(waitTime, stoppingToken);
                    continue;
                }

                // Prevent concurrent executions
                if (!_isRunning)
                {
                    _isRunning = true;
                    await UpdateMarketPairsAsync(stoppingToken);
                    _isRunning = false;
                }
            }
            catch (HttpRequestException ex) when (ex.StatusCode == HttpStatusCode.TooManyRequests)
            {
                // Handle rate limiting (HTTP 429)
                var retryAfter = GetRetryAfterDelay(ex);
                _nextAllowedRequestTime = DateTime.UtcNow.Add(retryAfter);

                string errorMessage = $"Rate limit (429) exceeded. Waiting for {retryAfter} before next request";
                BackgroundServiceStatus.RecordFailure(_serviceName, errorMessage);

                _logger.LogWarning("{ServiceName} received rate limit (429). Waiting for {RetryAfter} before next request",
                    _serviceName, retryAfter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred in {ServiceName} background service", _serviceName);
                BackgroundServiceStatus.RecordFailure(_serviceName, ex.Message);
            }
            finally
            {
                // Wait for the next interval
                await Task.Delay(_interval, stoppingToken);
            }
        }

        _logger.LogInformation("{ServiceName} background service is stopping", _serviceName);
    }

    /// <summary>
    /// Updates market pairs from the exchange API
    /// </summary>
    protected abstract Task UpdateMarketPairsAsync(CancellationToken stoppingToken);

    /// <summary>
    /// Gets the retry-after delay from the exception or uses a default value
    /// </summary>
    private TimeSpan GetRetryAfterDelay(HttpRequestException ex)
    {
        // Try to get the Retry-After header value
        if (ex.Data.Contains("Headers") && ex.Data["Headers"] is IEnumerable<KeyValuePair<string, IEnumerable<string>>> headers)
        {
            var retryAfterHeader = headers.FirstOrDefault(h => h.Key.Equals("Retry-After", StringComparison.OrdinalIgnoreCase));
            if (retryAfterHeader.Key != null && retryAfterHeader.Value.Any())
            {
                var retryAfterValue = retryAfterHeader.Value.First();
                if (int.TryParse(retryAfterValue, out int seconds))
                {
                    return TimeSpan.FromSeconds(seconds);
                }
                else if (DateTime.TryParse(retryAfterValue, out DateTime retryAfterDate))
                {
                    return retryAfterDate - DateTime.UtcNow;
                }
            }
        }

        // Default delay if no Retry-After header is found
        return _retryAfterDefaultDelay;
    }
}
