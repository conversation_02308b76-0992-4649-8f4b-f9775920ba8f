﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace RazeWinComTr.Areas.Admin.DbModel;

[Table("CONTENT")]
[Index("CrUserId", Name = "IX_CONTENT_CR_USER_ID")]
[Index("ModUserId", Name = "IX_CONTENT_MOD_USER_ID")]
public partial class Content
{
    [Key]
    [Column("CONTENT_ID")]
    public int ContentId { get; set; }

    [Column("CONTENT_TYPE")]
    public string ContentType { get; set; } = null!;

    [Column("TITLE")]
    public string Title { get; set; } = null!;

    [Column("DESCRIPTION")]
    public string Description { get; set; } = null!;

    [Column("IMAGE_URL")]
    public string? ImageUrl { get; set; }

    [Column("IS_ACTIVE")]
    public int IsActive { get; set; }

    [Column("CR_DATE", TypeName = "datetime")]
    public DateTime CrDate { get; set; }

    [Column("MOD_DATE", TypeName = "datetime")]
    public DateTime? ModDate { get; set; }

    [Column("CR_USER_ID")]
    public int CrUserId { get; set; }

    [Column("MOD_USER_ID")]
    public int? ModUserId { get; set; }

    [ForeignKey("CrUserId")]
    [InverseProperty("ContentCrUsers")]
    public virtual User CrUser { get; set; } = null!;

    [ForeignKey("ModUserId")]
    [InverseProperty("ContentModUsers")]
    public virtual User? ModUser { get; set; }
}