using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels;

namespace RazeWinComTr.Areas.Admin.Pages.Wallet;

public class EditModel : PageModel
{
    private readonly IWalletService _walletService;
    private readonly AppDbContext _context;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public EditModel(
        IWalletService walletService,
        AppDbContext context,
        IStringLocalizer<SharedResource> localizer)
    {
        _walletService = walletService;
        _context = context;
        _localizer = localizer;
    }

    public SweetAlert2Message? AlertMessage { get; set; }

    [BindProperty] public WalletEditViewModel Entity { get; set; } = new();

    public string UserEmail { get; set; } = string.Empty;
    public string CoinName { get; set; } = string.Empty;

    public string? ErrorMessage { get; set; }

    public async Task<IActionResult> OnGetAsync(int id)
    {
        var entity = await _walletService.GetByIdAsync(id);

        if (entity == null) return NotFound();

        Entity = new WalletEditViewModel
        {
            Id = entity.Id,
            UserId = entity.UserId,
            CoinId = entity.CoinId,
            Balance = entity.Balance
        };

        UserEmail = entity.User?.Email ?? "Unknown";
        CoinName = entity.Coin != null ? $"{entity.Coin.Name} ({entity.Coin.PairCode})" : "Unknown";

        return Page();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        try
        {
            if (!ModelState.IsValid) return Page();
            var userId = User.GetClaimUserId();
            if (!userId.HasValue) return Unauthorized();

            var entity = await _walletService.GetByIdAsync(Entity.Id);
            if (entity == null) return NotFound();

            // Only update the balance
            entity.Balance = Entity.Balance;
            entity.ModifiedDate = DateTime.UtcNow;

            await _walletService.UpdateAsync(entity);

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Success"],
                Text = _localizer["Successfully updated"],
                Icon = "success",
                RedirectUrl = "/Admin/Wallet"
            };

            return Page();
        }
        catch (Exception ex)
        {
            ErrorMessage = ex.Message;
            return Page();
        }
    }
}

public class WalletEditViewModel
{
    public int Id { get; set; }
    public int UserId { get; set; }
    public int CoinId { get; set; }
    public decimal Balance { get; set; }
}
