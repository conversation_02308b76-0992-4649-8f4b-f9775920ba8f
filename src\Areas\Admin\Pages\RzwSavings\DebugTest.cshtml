@page
@model RazeWinComTr.Areas.Admin.Pages.RzwSavings.DebugTestModel
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<SharedResource> Localizer
@{
    ViewData["Title"] = "RZW Savings Debug Test";
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>🔧 RZW Savings Debug Test</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="/Admin">Admin</a></li>
                        <li class="breadcrumb-item"><a href="/Admin/RzwSavings">RZW Savings</a></li>
                        <li class="breadcrumb-item active">Debug Test</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            
            <!-- Status Display -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card card-info">
                        <div class="card-header">
                            <h3 class="card-title">📊 Current Status</h3>
                            <div class="card-tools">
                                <button type="button" class="btn btn-sm btn-primary" onclick="loadSavingsStatus()">
                                    <i class="fas fa-sync"></i> Refresh Status
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="statusDisplay">
                                <p class="text-muted">Click "Refresh Status" to load current savings status...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Manual Operations -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card card-warning">
                        <div class="card-header">
                            <h3 class="card-title">⚡ Background Service Operations</h3>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">Manually trigger the background service operations that normally run daily.</p>
                            
                            <button type="button" class="btn btn-warning btn-block mb-3" onclick="triggerBackgroundOperations()">
                                <i class="fas fa-cogs"></i> Trigger All Background Operations
                            </button>
                            
                            <div id="backgroundOperationsResult"></div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card card-danger">
                        <div class="card-header">
                            <h3 class="card-title">🎯 Individual Account Operations</h3>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">Test operations on specific accounts.</p>
                            
                            <div class="form-group">
                                <label for="accountId">Account ID:</label>
                                <input type="number" class="form-control" id="accountId" placeholder="Enter account ID">
                            </div>
                            
                            <div class="btn-group-vertical w-100">
                                <button type="button" class="btn btn-info mb-2" onclick="triggerInterestForAccount()">
                                    <i class="fas fa-percentage"></i> Trigger Interest Payment
                                </button>
                                
                                <button type="button" class="btn btn-success mb-2" onclick="triggerMaturityForAccount()">
                                    <i class="fas fa-calendar-check"></i> Trigger Maturity Processing
                                </button>
                                
                                <button type="button" class="btn btn-danger mb-2" onclick="forceMaturityDate()">
                                    <i class="fas fa-exclamation-triangle"></i> Force Maturity Date (DANGEROUS)
                                </button>
                            </div>
                            
                            <div id="individualOperationsResult"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Results Display -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card card-success">
                        <div class="card-header">
                            <h3 class="card-title">📋 Operation Results</h3>
                            <div class="card-tools">
                                <button type="button" class="btn btn-sm btn-secondary" onclick="clearResults()">
                                    <i class="fas fa-trash"></i> Clear Results
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="operationResults">
                                <p class="text-muted">Operation results will appear here...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </section>
</div>

<script>
// Base API URL
const apiBase = '/api/admin/rzw-savings';

// Load current savings status
async function loadSavingsStatus() {
    try {
        showLoading('statusDisplay');
        const response = await fetch(`${apiBase}/debug-savings-status`);
        const data = await response.json();
        
        if (data.success) {
            displaySavingsStatus(data);
        } else {
            showError('statusDisplay', data.message);
        }
    } catch (error) {
        showError('statusDisplay', `Error loading status: ${error.message}`);
    }
}

// Display savings status
function displaySavingsStatus(data) {
    const html = `
        <div class="row">
            <div class="col-md-3">
                <div class="info-box bg-info">
                    <span class="info-box-icon"><i class="fas fa-piggy-bank"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Active Accounts</span>
                        <span class="info-box-number">${data.statistics.totalActiveAccounts}</span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="info-box bg-warning">
                    <span class="info-box-icon"><i class="fas fa-percentage"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Ready for Interest</span>
                        <span class="info-box-number">${data.statistics.accountsReadyForInterest}</span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="info-box bg-success">
                    <span class="info-box-icon"><i class="fas fa-calendar-check"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Matured Accounts</span>
                        <span class="info-box-number">${data.statistics.maturedAccountsCount}</span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="info-box bg-primary">
                    <span class="info-box-icon"><i class="fas fa-history"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Recent Payments</span>
                        <span class="info-box-number">${data.statistics.recentPaymentsCount}</span>
                    </div>
                </div>
            </div>
        </div>
        
        ${data.activeAccounts.length > 0 ? `
        <h5 class="mt-3">Active Accounts:</h5>
        <div class="table-responsive">
            <table class="table table-sm table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>User</th>
                        <th>Plan</th>
                        <th>Amount</th>
                        <th>Rate</th>
                        <th>Days Held</th>
                        <th>Days to Maturity</th>
                        <th>Total Earned</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    ${data.activeAccounts.map(account => `
                        <tr class="${account.isMatured ? 'table-success' : ''}">
                            <td>${account.id}</td>
                            <td>${account.userEmail || 'N/A'}</td>
                            <td>${account.planName || 'N/A'}</td>
                            <td>${parseFloat(account.rzwAmount).toFixed(8)} RZW</td>
                            <td>${(account.interestRate * 100).toFixed(6)}%</td>
                            <td>${account.daysHeld}</td>
                            <td>${account.daysToMaturity}</td>
                            <td>${parseFloat(account.totalEarnedRzw).toFixed(8)} RZW</td>
                            <td>
                                <span class="badge ${account.isMatured ? 'badge-success' : 'badge-warning'}">
                                    ${account.isMatured ? 'Matured' : 'Active'}
                                </span>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
        ` : '<p class="text-muted">No active accounts found.</p>'}
        
        <small class="text-muted">Last updated: ${new Date(data.timestamp).toLocaleString()}</small>
    `;
    
    document.getElementById('statusDisplay').innerHTML = html;
}

// Trigger background operations
async function triggerBackgroundOperations() {
    try {
        showLoading('backgroundOperationsResult');
        const response = await fetch(`${apiBase}/debug-trigger-background-operations`, {
            method: 'POST'
        });
        const data = await response.json();
        
        displayOperationResult('Background Operations', data, 'backgroundOperationsResult');
        addToResults('Background Operations', data);
    } catch (error) {
        showError('backgroundOperationsResult', `Error: ${error.message}`);
    }
}

// Trigger interest for specific account
async function triggerInterestForAccount() {
    const accountId = document.getElementById('accountId').value;
    if (!accountId) {
        alert('Please enter an account ID');
        return;
    }
    
    try {
        showLoading('individualOperationsResult');
        const response = await fetch(`${apiBase}/debug-trigger-interest/${accountId}`, {
            method: 'POST'
        });
        const data = await response.json();
        
        displayOperationResult(`Interest Payment for Account ${accountId}`, data, 'individualOperationsResult');
        addToResults(`Interest Payment for Account ${accountId}`, data);
    } catch (error) {
        showError('individualOperationsResult', `Error: ${error.message}`);
    }
}

// Trigger maturity for specific account
async function triggerMaturityForAccount() {
    const accountId = document.getElementById('accountId').value;
    if (!accountId) {
        alert('Please enter an account ID');
        return;
    }
    
    try {
        showLoading('individualOperationsResult');
        const response = await fetch(`${apiBase}/debug-trigger-maturity/${accountId}`, {
            method: 'POST'
        });
        const data = await response.json();
        
        displayOperationResult(`Maturity Processing for Account ${accountId}`, data, 'individualOperationsResult');
        addToResults(`Maturity Processing for Account ${accountId}`, data);
    } catch (error) {
        showError('individualOperationsResult', `Error: ${error.message}`);
    }
}

// Force maturity date (dangerous)
async function forceMaturityDate() {
    const accountId = document.getElementById('accountId').value;
    if (!accountId) {
        alert('Please enter an account ID');
        return;
    }
    
    const newDate = prompt('Enter new maturity date (YYYY-MM-DD HH:MM:SS) - DANGEROUS OPERATION!');
    if (!newDate) return;
    
    if (!confirm('This is a DANGEROUS operation that will modify the database. Are you sure?')) {
        return;
    }
    
    try {
        showLoading('individualOperationsResult');
        const response = await fetch(`${apiBase}/debug-force-maturity-date/${accountId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(newDate)
        });
        const data = await response.json();
        
        displayOperationResult(`Force Maturity Date for Account ${accountId}`, data, 'individualOperationsResult');
        addToResults(`Force Maturity Date for Account ${accountId}`, data);
    } catch (error) {
        showError('individualOperationsResult', `Error: ${error.message}`);
    }
}

// Helper functions
function showLoading(elementId) {
    document.getElementById(elementId).innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>';
}

function showError(elementId, message) {
    document.getElementById(elementId).innerHTML = `<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> ${message}</div>`;
}

function displayOperationResult(title, data, elementId) {
    const alertClass = data.success ? 'alert-success' : 'alert-danger';
    const icon = data.success ? 'fa-check' : 'fa-times';
    
    const html = `
        <div class="alert ${alertClass}">
            <h6><i class="fas ${icon}"></i> ${title}</h6>
            <p><strong>Message:</strong> ${data.message}</p>
            <small>Timestamp: ${new Date(data.timestamp).toLocaleString()}</small>
            ${data.summary ? `<pre class="mt-2">${JSON.stringify(data.summary, null, 2)}</pre>` : ''}
        </div>
    `;
    
    document.getElementById(elementId).innerHTML = html;
}

function addToResults(operation, data) {
    const resultsDiv = document.getElementById('operationResults');
    const alertClass = data.success ? 'alert-success' : 'alert-danger';
    const icon = data.success ? 'fa-check' : 'fa-times';
    
    const html = `
        <div class="alert ${alertClass}">
            <h6><i class="fas ${icon}"></i> ${operation}</h6>
            <p><strong>Message:</strong> ${data.message}</p>
            <small>Timestamp: ${new Date(data.timestamp).toLocaleString()}</small>
            <details class="mt-2">
                <summary>Full Response</summary>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            </details>
        </div>
    `;
    
    resultsDiv.innerHTML = html + resultsDiv.innerHTML;
}

function clearResults() {
    document.getElementById('operationResults').innerHTML = '<p class="text-muted">Operation results will appear here...</p>';
}

// Auto-load status on page load
document.addEventListener('DOMContentLoaded', function() {
    loadSavingsStatus();
});
</script>
