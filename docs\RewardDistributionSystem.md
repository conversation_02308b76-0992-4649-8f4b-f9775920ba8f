# RazeWin Reward Distribution System

## Overview

The RazeWin platform includes a multi-level referral reward system that allows users who have purchased packages to earn rewards when their referrals (and their referrals' referrals) make deposits. This document explains how the reward distribution system works.

## Key Concepts

### Packages

Users can purchase packages (Bronze, Silver, Gold, Platinum) that enable them to participate in the referral program. Each package defines:
- The price of the package
- The reward percentages for different referral levels
- Other benefits

### Referral Levels

The system supports multiple referral levels:
- Level 1: Direct referrals (users who signed up using your referral code)
- Level 2: Indirect referrals (users who signed up using your direct referrals' codes)
- Level 3: Further indirect referrals
- And so on...

### Reward Percentages

Each package defines specific reward percentages for different referral levels. For example:
- Bronze package: 5% for level 1, 3% for level 2, 0% for level 3
- Silver package: 10% for level 1, 7% for level 2, 5% for level 3
- Gold package: 15% for level 1, 10% for level 2, 7% for level 3
- Platinum package: 20% for level 1, 15% for level 2, 10% for level 3

These percentages are stored in the `PACKAGE_REWARD_PERCENTAGE` table and can be configured through the admin interface.

## Reward Distribution Process

### Eligibility for Rewards

To be eligible for rewards:
1. A user must have purchased a package
2. The package must be active
3. The package must have reward percentages defined for the relevant referral level

### When Rewards Are Distributed

Rewards are distributed in the following scenarios:
1. When a referred user makes a deposit
2. When a referred user purchases a package

### How Rewards Are Calculated

When a user makes a deposit or purchases a package:

1. The system identifies the referral chain (up to 10 levels for safety)
2. For each referrer in the chain:
   - The system checks if the referrer has an active package
   - If yes, it retrieves the reward percentage for the referrer's package and the specific level
   - If a percentage is defined for that level, the reward is calculated as: `deposit_amount * (percentage / 100)`
   - The reward is added to the referrer's RZW wallet
   - A record is created in the `REFERRAL_REWARD` table

### Example Scenario

Let's consider an example:

- User1 has purchased a Platinum package (with 20% for level 1, 15% for level 2, 10% for level 3)
- User1 refers User1Refer1, User1Refer2, and User1Refer3 (these are level 1 referrals)
- User1Refer2 refers User1Refer2_Refer1 and User1Refer2_Refer2 (these are level 2 referrals for User1)

When User1Refer2 makes a deposit of 1000 TL:
- User1 receives 20% of 1000 TL = 200 TL (as User1Refer2 is a level 1 referral)

When User1Refer2_Refer2 makes a deposit of 1000 TL:
- User1Refer2 receives a reward if they have an active package (level 1 percentage of their package)
- User1 receives 15% of 1000 TL = 150 TL (as User1Refer2_Refer2 is a level 2 referral)

### Important Notes

1. Only users with active packages can receive rewards
2. If no specific percentage is found for a referral level, no reward will be given for that level
3. Rewards are calculated based on the original deposit or package purchase amount
4. Rewards are distributed in RZW tokens, not in TL
5. The system supports an unlimited number of referral levels, as defined in the package reward percentages

## Database Structure

The reward system uses the following database tables:

- `PACKAGE`: Defines available packages
- `USER_PACKAGE`: Tracks which users have purchased which packages
- `PACKAGE_REWARD_PERCENTAGE`: Defines reward percentages for each package and level
- `REFERRAL_REWARD`: Records all distributed rewards

## Administration

Administrators can:
1. Create and manage packages
2. Define reward percentages for each package and level
3. View and manage user packages
4. View reward distribution history
