using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels;
using RazeWinComTr.Areas.Admin.ViewModels.Deposit;

namespace RazeWinComTr.Areas.Admin.Pages.Deposit
{
    public class DistributeRewardsModel : PageModel
    {
        private readonly ReferralRewardService _referralRewardService;
        private readonly DepositService _depositService;
        private readonly IStringLocalizer<SharedResource> _localizer;
        private readonly ILogger<DistributeRewardsModel> _logger;

        public DistributeRewardsModel(
            ReferralRewardService referralRewardService,
            DepositService depositService,
            IStringLocalizer<SharedResource> localizer,
            ILogger<DistributeRewardsModel> logger)
        {
            _referralRewardService = referralRewardService;
            _depositService = depositService;
            _localizer = localizer;
            _logger = logger;
        }

        [BindProperty(SupportsGet = true)]
        public int Id { get; set; }

        public DbModel.Deposit? Deposit { get; set; }

        public string UserEmail { get; set; } = string.Empty;

        public string UserFullName { get; set; } = string.Empty;

        public SweetAlert2Message? AlertMessage { get; set; }

        public DepositRewardSummary? RewardPreview { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            var deposit = await _depositService.GetByIdAsync(Id);

            if (deposit == null)
            {
                AlertMessage = new SweetAlert2Message
                {
                    Icon = "error",
                    Title = _localizer["Error"],
                    Text = _localizer["Payment not found."]
                };
                return Page();
            }

            Deposit = deposit;
            UserEmail = deposit.User?.Email ?? "";
            UserFullName = $"{deposit.User?.Name} {deposit.User?.Surname}".Trim();

            // Check if payment is approved
            if (deposit.Status != DepositStatus.Approved)
            {
                AlertMessage = new SweetAlert2Message
                {
                    Icon = "warning",
                    Title = _localizer["Warning"],
                    Text = _localizer["Only approved payments can have rewards distributed."]
                };
            }
            // Check if rewards are already distributed
            else if (deposit.RewardStatus == DepositRewardStatus.Distributed ||
                deposit.RewardStatus == DepositRewardStatus.NoRewards)
            {
                AlertMessage = new SweetAlert2Message
                {
                    Icon = "info",
                    Title = _localizer["Information"],
                    Text = _localizer["Rewards have already been distributed for this payment."]
                };
            }
            // If payment is approved and rewards are not distributed yet, calculate potential rewards
            else if (deposit.RewardStatus == DepositRewardStatus.Pending ||
                    deposit.RewardStatus == DepositRewardStatus.Failed)
            {
                try
                {
                    // Calculate potential rewards
                    RewardPreview = await _referralRewardService.CalculatePotentialRewardsAsync(Id);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error calculating potential rewards for payment {PaymentId}", Id);
                }
            }

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            var deposit = await _depositService.GetByIdAsync(Id);

            if (deposit == null)
            {
                AlertMessage = new SweetAlert2Message
                {
                    Icon = "error",
                    Title = _localizer["Error"],
                    Text = _localizer["Payment not found."]
                };
                return Page();
            }

            Deposit = deposit;
            UserEmail = deposit.User?.Email ?? "";
            UserFullName = $"{deposit.User?.Name} {deposit.User?.Surname}".Trim();

            // Check if payment is approved
            if (deposit.Status != DepositStatus.Approved)
            {
                AlertMessage = new SweetAlert2Message
                {
                    Icon = "warning",
                    Title = _localizer["Warning"],
                    Text = _localizer["Only approved payments can have rewards distributed."]
                };
                return Page();
            }

            // Check if rewards are already distributed
            if (deposit.RewardStatus == DepositRewardStatus.Distributed ||
                deposit.RewardStatus == DepositRewardStatus.NoRewards)
            {
                AlertMessage = new SweetAlert2Message
                {
                    Icon = "info",
                    Title = _localizer["Information"],
                    Text = _localizer["Rewards have already been distributed for this payment."]
                };
                return Page();
            }

            try
            {
                // Process rewards
                var summary = await _referralRewardService.ProcessDepositRewardsAsync(Id);

                if (summary.RewardedUsersCount > 0)
                {
                    AlertMessage = new SweetAlert2Message
                    {
                        Icon = "success",
                        Title = _localizer["Success"],
                        Text = _localizer["Rewards have been distributed successfully. {0} users received a total of {1} RZW.", summary.RewardedUsersCount, summary.TotalRzwDistributed.ToString("N8")],
                        RedirectUrl = $"/Admin/Deposit/RewardSummary?id={Id}"
                    };
                }
                else
                {
                    AlertMessage = new SweetAlert2Message
                    {
                        Icon = "info",
                        Title = _localizer["Information"],
                        Text = _localizer["No rewards were distributed. There may be no eligible users in the referral chain."],
                        RedirectUrl = $"/Admin/Deposit/RewardSummary?id={Id}"
                    };
                }

                return RedirectToPage("RewardSummary", new { id = Id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error distributing rewards for payment {PaymentId}", Id);

                AlertMessage = new SweetAlert2Message
                {
                    Icon = "error",
                    Title = _localizer["Error"],
                    Text = _localizer["An error occurred while distributing rewards: {0}", ex.Message]
                };
                return Page();
            }
        }
    }
}
