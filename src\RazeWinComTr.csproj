﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>

	<ItemGroup>
		<None Include="efpt.config.json.user" />
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="FileSignatures" Version="5.1.1" />
		<PackageReference Include="MailKit" Version="4.11.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.4" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.4">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.4" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.4">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Extensions.Caching.Hybrid" Version="9.4.0" />
		<PackageReference Include="SixLabors.ImageSharp" Version="3.1.7" />
		<PackageReference Include="Serilog" Version="4.2.0" />
		<PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
		<PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
		<PackageReference Include="Serilog.Enrichers.Thread" Version="4.0.0" />
		<PackageReference Include="Serilog.Settings.Configuration" Version="9.0.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
		<PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
	</ItemGroup>
</Project>
