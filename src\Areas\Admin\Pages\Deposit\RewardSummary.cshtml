@page
@model RazeWinComTr.Areas.Admin.Pages.Deposit.RewardSummaryModel
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.Enums
@using RazeWinComTr.Areas.Admin.ViewModels.Deposit
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["Payment Reward Summary"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["Payment Reward Summary"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/Admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="/Admin/Deposit">@L["Payments"]</a></li>
                    <li class="breadcrumb-item active">@L["Reward Summary"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        @if (Model.AlertMessage != null)
        {
            <script id="scriptMessage" type="text/javascript">
                window.onload = function () {
                    Swal.fire({
                        title: '@Html.Raw(Model.AlertMessage.Title)',
                        text: '@Html.Raw(Model.AlertMessage.Text)',
                        icon: '@Html.Raw(Model.AlertMessage.Icon)'
                    }).then((result) => {
                        var redirectUrl = '@(Model.AlertMessage.RedirectUrl ?? string.Empty)';
                        if (result.isConfirmed && redirectUrl !== '') {
                            location.href = redirectUrl;
                        }
                    });
                    $('#scriptMessage').remove();
                }
            </script>
        }

        <div class="row">
            <div class="col-md-6">
                <div class="card card-primary">
                    <div class="card-header">
                        <h3 class="card-title">@L["Payment Details"]</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label>@L["Payment ID"]</label>
                            <input type="text" class="form-control" value="@(Model.Summary?.DepositId ?? 0)" readonly />
                        </div>
                        <div class="form-group">
                            <label>@L["User"]</label>
                            <input type="text" class="form-control" value="@(Model.Summary?.UserFullName ?? "") (@(Model.Summary?.UserEmail ?? ""))" readonly />
                        </div>
                        <div class="form-group">
                            <label>@L["Payment Amount"]</label>
                            <input type="text" class="form-control" value="@(Model.Summary?.DepositAmount.ToString("N2") ?? "0.00") @L["Currency_Symbol"]" readonly />
                        </div>
                        <div class="form-group">
                            <label>@L["Payment Date"]</label>
                            <input type="text" class="form-control" value="@(Model.Summary?.DepositDate.ToLocalTime().ToString("dd.MM.yyyy HH:mm") ?? "-")" readonly />
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card card-info">
                    <div class="card-header">
                        <h3 class="card-title">@L["Reward Summary"]</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label>@L["Process Date"]</label>
                            <input type="text" class="form-control" value="@(Model.Summary?.ProcessDate?.ToLocalTime().ToString("dd.MM.yyyy HH:mm") ?? "-")" readonly />
                        </div>
                        <div class="form-group">
                            <label>@L["Rewarded Users Count"]</label>
                            <input type="text" class="form-control" value="@(Model.Summary?.RewardedUsersCount ?? 0)" readonly />
                        </div>
                        <div class="form-group">
                            <label>@L["Total TL Distributed"]</label>
                            <input type="text" class="form-control" value="@(Model.Summary?.TotalTlDistributed.ToString("N2") ?? "0.00") @L["Currency_Symbol"]" readonly />
                        </div>
                        <div class="form-group">
                            <label>@L["Total RZW Distributed"]</label>
                            <input type="text" class="form-control" value="@(Model.Summary?.TotalRzwDistributed.ToString("N8") ?? "0.00000000")" readonly />
                        </div>
                        <div class="form-group">
                            <label>@L["RZW Price at Distribution"]</label>
                            <input type="text" class="form-control" value="@(Model.Summary?.RzwBuyPrice.ToString("N8") ?? "0.00000000") @L["Currency_Symbol"]" readonly />
                        </div>
                        <div class="form-group">
                            <label>@L["Total RZW Value (TRY)"]</label>
                            <input type="text" class="form-control" value="@(Model.Summary?.TotalTryValue.ToString("N2") ?? "0.00") @L["Currency_Symbol"]" readonly />
                        </div>
                        <div class="form-group">
                            <label>@L["Total Reward Value (TRY)"]</label>
                            <input type="text" class="form-control" value="@((Model.Summary?.TotalTlDistributed + Model.Summary?.TotalTryValue ?? 0).ToString("N2")) @L["Currency_Symbol"]" readonly />
                        </div>
                        <div class="form-group">
                            <label>@L["Reward Status"]</label>
                            <input type="text" class="form-control" value="@(Model.Summary?.RewardStatus == DepositRewardStatus.Pending ? L["Pending"] :
                                                                            Model.Summary?.RewardStatus == DepositRewardStatus.Processed ? L["Processed"] :
                                                                            Model.Summary?.RewardStatus == DepositRewardStatus.Distributed ? L["Distributed"] :
                                                                            Model.Summary?.RewardStatus == DepositRewardStatus.NoRewards ? L["No Rewards"] :
                                                                            L["Failed"])" readonly />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @if (Model.Summary?.RewardedUsersCount > 0)
        {
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">@L["Distributed Rewards"]</h3>
                        </div>
                        <div class="card-body">
                            <table id="rewardsTable" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>@L["User"]</th>
                                        <th>@L["Package"]</th>
                                        <th>@L["Level"]</th>
                                        <th>@L["TL Percentage"]</th>
                                        <th>@L["TL Amount"]</th>
                                        <th>@L["RZW Percentage"]</th>
                                        <th>@L["RZW Amount"]</th>
                                        <th>@L["RZW Price at Distribution"]</th>
                                        <th>@L["RZW Value (TRY)"]</th>
                                        <th>@L["Date"]</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var reward in Model.Summary?.Rewards ?? new List<RewardDetailViewModel>())
                                    {
                                        <tr>
                                            <td>@reward.UserFullName (@reward.UserEmail)</td>
                                            <td>@reward.PackageName</td>
                                            <td>@reward.Level</td>
                                            <td>@reward.TlPercentage.ToString("N2")%</td>
                                            <td>@reward.TlAmount.ToString("N2") @L["Currency_Symbol"]</td>
                                            <td>@reward.RzwPercentage.ToString("N2")%</td>
                                            <td>@reward.RzwAmount.ToString("N8")</td>
                                            <td>@reward.RzwBuyPrice.ToString("N8") @L["Currency_Symbol"]</td>
                                            <td>@reward.TryValue.ToString("N2") @L["Currency_Symbol"]</td>
                                            <td>@reward.DepositDate.ToLocalTime().ToString("dd.MM.yyyy HH:mm")</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        }
        else
        {
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-info">
                        <h5><i class="icon fas fa-info"></i> @L["No Rewards Distributed"]</h5>
                        <p>@L["No rewards were distributed for this payment. This could be because:"]</p>
                        <ul>
                            <li>@L["There were no users in the referral chain."]</li>
                            <li>@L["Users in the referral chain did not have active packages."]</li>
                            <li>@L["The package reward percentages were set to zero for the relevant levels."]</li>
                        </ul>
                    </div>
                </div>
            </div>
        }

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <a href="/Admin/Deposit" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> @L["Back to Payments"]
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        $(function () {
            $("#rewardsTable").DataTable({
                "responsive": true,
                "lengthChange": false,
                "autoWidth": false,
                "buttons": ["copy", "csv", "excel", "pdf", "print", "colvis"],
                "order": [[1, "asc"]],
                "columnDefs": [
                    { "type": "num", "targets": [2, 3, 4, 5, 6] } // Ensure numeric columns are sorted correctly
                ]
            }).buttons().container().appendTo('#rewardsTable_wrapper .col-md-6:eq(0)');
        });
    </script>
}
