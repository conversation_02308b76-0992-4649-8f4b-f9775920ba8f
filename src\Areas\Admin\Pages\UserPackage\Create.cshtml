@page
@using Microsoft.Extensions.Localization
@model RazeWinComTr.Areas.Admin.Pages.UserPackage.CreateModel
@inject IStringLocalizer<SharedResource> L
@using RazeWinComTr.Areas.Admin.Helpers

@{
    ViewData["Title"] = $"{L["New"]} {L["User Package"]}";
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

@if (Model.AlertMessage != null)
{
    <script id="scriptMessage" type="text/javascript">
        window.onload = function () {
            Swal.fire({
                title: '@Html.Raw(Model.AlertMessage.Title)',
                text: '@Html.Raw(Model.AlertMessage.Text)',
                icon: '@Html.Raw(Model.AlertMessage.Icon)'
            }).then((result) => {
                var redirectUrl = '@(Model.AlertMessage.RedirectUrl ?? string.Empty)';
                if (result.isConfirmed && redirectUrl !== '') {
                    location.href = redirectUrl;
                }
            });
            $('#scriptMessage').remove();
        }
    </script>
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@($"{L["Create a New"]} {L["User Package"]}")</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="/admin/userpackage">@L["User Packages"]</a></li>
                    <li class="breadcrumb-item active">@L["New"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>
<div asp-validation-summary="ModelOnly" class="text-danger"></div>
<section class="content">
    <div class="container-fluid">
        <div class="card">
            <form method="post">
                <div class="card-body">
                    <div class="form-group">
                        <label asp-for="ViewEntity.UserId">@L["User"]</label>
                        <select asp-for="ViewEntity.UserId" class="form-control select2" required>
                            <option value="">@L["Select User"]</option>
                            @foreach (var user in Model.Users)
                            {
                                <option value="@user.UserId">@user.Name @user.Surname (@user.Email)</option>
                            }
                        </select>
                    </div>
                    <div class="form-group">
                        <label asp-for="ViewEntity.PackageId">@L["Package"]</label>
                        <select asp-for="ViewEntity.PackageId" class="form-control" required>
                            <option value="">@L["Select Package"]</option>
                            @foreach (var package in Model.Packages)
                            {
                                <option value="@package.Id">@package.Name (@package.Price.ToString("N8"))</option>
                            }
                        </select>
                    </div>
                    <div class="form-group row">
                        <label asp-for="ViewEntity.PurchaseDate" class="col-sm-2 col-form-label">@L["Purchase Date"]</label>
                        <div class="col-sm-10">
                            <input asp-for="ViewEntity.PurchaseDate" class="form-control" type="datetime-local" value="@(Model.ViewEntity?.PurchaseDate.ToString("yyyy-MM-ddTHH:mm"))" required/>
                            <span asp-validation-for="ViewEntity.PurchaseDate" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label asp-for="ViewEntity.ExpiryDate" class="col-sm-2 col-form-label">@L["Expiry Date"]</label>
                        <div class="col-sm-10">
                            <input asp-for="ViewEntity.ExpiryDate" class="form-control" type="datetime-local" value="@(Model.ViewEntity?.ExpiryDate?.ToString("yyyy-MM-ddTHH:mm"))"/>
                            <span asp-validation-for="ViewEntity.ExpiryDate" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label asp-for="ViewEntity.Status">@L["Status"]</label>
                        <select asp-for="ViewEntity.Status" class="form-control" required>
                            <option value="1">@L["Active"]</option>
                            <option value="2">@L["Expired"]</option>
                            <option value="3">@L["Cancelled"]</option>
                        </select>
                    </div>

                </div>
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-1"></i> @L["Save"]
                    </button>
                    <a href="/Admin/UserPackage" class="btn btn-default">
                        <i class="fas fa-times mr-1"></i> @L["Cancel"]
                    </a>
                </div>
            </form>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        $(function () {
            $('.select2').select2();
        });
    </script>
}
