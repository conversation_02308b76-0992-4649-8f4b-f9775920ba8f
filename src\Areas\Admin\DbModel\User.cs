﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;
using System.Text.Json.Serialization;
using RazeWinComTr.Models;

namespace RazeWinComTr.Areas.Admin.DbModel;

[Table("USER")]
public partial class User
{
    [Key]
    [Column("USER_ID")]
    public int UserId { get; set; }

    [Column("EMAIL")]
    public string Email { get; set; } = null!;

    [Column("IDENTITY_NUMBER")]
    public string IdentityNumber { get; set; } = null!;

    [Column("NAME")]
    public string Name { get; set; } = null!;

    [Column("SURNAME")]
    public string Surname { get; set; } = null!;

    [Column("PHONE_NUMBER")]
    public string PhoneNumber { get; set; } = null!;

    [Column("BIRTH_DATE", TypeName = "datetime")]
    public DateTime BirthDate { get; set; }

    [Column("PASSWORD_HASH")]
    public string? PasswordHash { get; set; }

    [Column("IS_ACTIVE")]
    public int IsActive { get; set; }

    [Column("BALANCE", TypeName = "decimal(20,2)")]
    public decimal Balance { get; set; }

    [Column("CR_DATE", TypeName = "datetime")]
    public DateTime CrDate { get; set; }

    [Column("MOD_DATE", TypeName = "datetime")]
    public DateTime? ModDate { get; set; }

    [Column("IBAN")]
    [StringLength(50)]
    public string? Iban { get; set; }

    [Column("SPECIAL_MARKET")]
    public string? SpecialMarket { get; set; }

    [Column("LAST_LOGIN", TypeName = "datetime")]
    public DateTime? LastLogin { get; set; }

    [Column("IP_ADDRESS")]
    [StringLength(50)]
    public string? IpAddress { get; set; }

    [Column("REFERRAL_CODE")]
    [StringLength(10)]
    public string ReferralCode { get; set; } = string.Empty;

    [Column("REFERRER_USER_ID")]
    public int? ReferrerId { get; set; }

    [ForeignKey("ReferrerId")]
    public virtual User? Referrer { get; set; }

    [InverseProperty("Referrer")]
    public virtual ICollection<User> ReferredUsers { get; set; } = new List<User>();

    [InverseProperty("User")]
    public virtual ICollection<UserRoleRelation> UserRoleRelations { get; set; } = new List<UserRoleRelation>();

    [InverseProperty("User")]
    public virtual ICollection<RzwSavingsAccount> RzwSavingsAccounts { get; set; } = new List<RzwSavingsAccount>();

    // Helper method to work with special market settings
    [NotMapped]
    public SpecialMarketSettings? SpecialMarketSettings
    {
        get => string.IsNullOrEmpty(SpecialMarket) ? null : JsonSerializer.Deserialize<SpecialMarketSettings>(SpecialMarket);
        set => SpecialMarket = value == null ? null : JsonSerializer.Serialize(value);
    }
}