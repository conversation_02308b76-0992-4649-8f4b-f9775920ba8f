/* RZW Token Listing Slider Styles */

/* Base Styles */
.rzw-listing-preview {
    padding-left: 200px;
    padding-right: 200px;
    max-width: 1200px;
    margin: 0 auto;
}

    .rzw-listing-preview ul li {
        width: 100% !important;
        max-width: 800px;
        margin: 0 auto;
    }

.rzw-listing-title span {
    display: block;
    animation: glow 2s ease-in-out infinite alternate;
    white-space: nowrap;
    overflow: visible;
}

.rzw-listing-subtitle span,
.rzw-listing-date span,
.rzw-listing-announcement span {
    display: block;
    white-space: nowrap;
    overflow: visible;
}

/* Glow Animation */
@keyframes glow {
    from {
        text-shadow: 0 0 10px #00ff88, 0 0 20px #00ff88, 0 0 30px #00ff88;
    }

    to {
        text-shadow: 0 0 20px #00ff88, 0 0 30px #00ff88, 0 0 40px #00ff88;
    }
}

/* Large Desktop (1400px+) */
@media (max-width: 1399px) and (min-width: 1200px) {
    .rzw-listing-preview {
        padding-left: 150px !important;
        padding-right: 150px !important;
        max-width: 1000px;
    }

        .rzw-listing-preview ul li {
            max-width: 700px;
        }

    .rzw-listing-title span {
        font-size: 38px !important;
    }

    .rzw-listing-subtitle span {
        font-size: 30px !important;
    }

    .rzw-listing-date span {
        font-size: 26px !important;
    }

    .rzw-listing-announcement span {
        font-size: 22px !important;
    }
}

/* Medium Desktop (992px - 1199px) */
@media (max-width: 1199px) and (min-width: 992px) {
    .rzw-listing-preview {
        padding-left: 100px !important;
        padding-right: 100px !important;
        max-width: 800px;
    }

        .rzw-listing-preview ul li {
            max-width: 600px;
        }

    .rzw-listing-title span {
        font-size: 34px !important;
    }

    .rzw-listing-subtitle span {
        font-size: 26px !important;
    }

    .rzw-listing-date span {
        font-size: 22px !important;
    }

    .rzw-listing-announcement span {
        font-size: 18px !important;
    }
}

/* Tablet (768px - 991px) */
@media (max-width: 991px) and (min-width: 768px) {
    .rzw-listing-preview {
        padding-left: 50px !important;
        padding-right: 50px !important;
        max-width: 600px;
    }

        .rzw-listing-preview ul li {
            max-width: 500px;
        }

    .rzw-listing-title span {
        font-size: 30px !important;
        white-space: normal;
    }

    .rzw-listing-subtitle span {
        font-size: 22px !important;
        white-space: normal;
    }

    .rzw-listing-date span {
        font-size: 18px !important;
        white-space: normal;
    }

    .rzw-listing-announcement span {
        font-size: 16px !important;
        white-space: normal;
    }
}

/* Large Mobile (481px - 767px) */
@media (max-width: 767px) and (min-width: 481px) {
    .rzw-listing-preview {
        padding-left: 20px !important;
        padding-right: 20px !important;
        max-width: 400px;
    }

        .rzw-listing-preview ul li {
            max-width: 350px;
        }

    .rzw-listing-title span {
        font-size: 26px !important;
        white-space: normal;
    }

    .rzw-listing-subtitle span {
        font-size: 20px !important;
        white-space: normal;
    }

    .rzw-listing-date span {
        font-size: 16px !important;
        white-space: normal;
    }

    .rzw-listing-announcement span {
        font-size: 14px !important;
        white-space: normal;
    }
}

/* Mobile (320px - 480px) */
@media (max-width: 480px) {
    .rzw-listing-preview {
        padding-left: 15px !important;
        padding-right: 15px !important;
        max-width: 320px;
    }

        .rzw-listing-preview ul li {
            max-width: 290px;
        }

    .rzw-listing-title span {
        font-size: 22px !important;
        white-space: normal;
        line-height: 1.2;
    }

    .rzw-listing-subtitle span {
        font-size: 16px !important;
        white-space: normal;
        line-height: 1.2;
    }

    .rzw-listing-date span {
        font-size: 14px !important;
        white-space: normal;
        line-height: 1.2;
    }

    .rzw-listing-announcement span {
        font-size: 12px !important;
        white-space: normal;
        line-height: 1.2;
    }
}

/* Very Small Mobile (360px and below) */
@media (max-width: 360px) {
    .rzw-listing-preview {
        padding-left: 10px !important;
        padding-right: 10px !important;
        max-width: 280px;
    }

        .rzw-listing-preview ul li {
            max-width: 260px;
        }

    .rzw-listing-title span {
        font-size: 18px !important;
        white-space: normal;
        line-height: 1.1;
    }

    .rzw-listing-subtitle span {
        font-size: 14px !important;
        white-space: normal;
        line-height: 1.1;
    }

    .rzw-listing-date span {
        font-size: 12px !important;
        white-space: normal;
        line-height: 1.1;
    }

    .rzw-listing-announcement span {
        font-size: 10px !important;
        white-space: normal;
        line-height: 1.1;
    }
}



/* Birinci slider i�in responsive arka plan konumu */
.first-slider-bg {
    background-size: contain;
    background-image: url('/public/front/fxyatirim/assets/images/slider-listing-notext.jpg');
    min-height: 560px;
}

@media (max-width: 1199px) {
    .first-slider-bg {
        min-height: 350px;
    }
}

@media (max-width: 991px) {
    .first-slider-bg {
        min-height: 350px;
    }
}

@media (max-width: 600px) {
    .first-slider-bg {
        min-height: 220px;
    }
}

/* Birinci slider i�in responsive arka plan konumu */
.index-slider-bg {
    background-size: contain;
    min-height: 560px;
}

@media (max-width: 1199px) {
    .index-slider-bg {
        min-height: 350px;
    }
}

@media (max-width: 991px) {
    .index-slider-bg {
        min-height: 350px;
    }
}

@media (max-width: 600px) {
    .index-slider-bg {
        min-height: 220px;
    }
}
