/**
 * RZW Savings Create Page JavaScript
 * Handles plan selection, amount input, interest calculation and form validation
 */

class RzwSavingsCreate {
    constructor() {
        this.selectedPlan = null;
        this.availableBalance = 0;
        this.isCalculating = false;
        this.calculationTimeout = null;
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadInitialData();
        this.initializeForm();
    }

    bindEvents() {
        // Plan selection events
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('plan-radio')) {
                this.handlePlanSelection(e.target);
            }
        });

        // Plan card click events
        document.addEventListener('click', (e) => {
            const planCard = e.target.closest('.plan-card');
            if (planCard) {
                const radio = planCard.querySelector('.plan-radio');
                if (radio && !radio.checked) {
                    radio.checked = true;
                    this.handlePlanSelection(radio);
                }
            }
        });

        // Amount input events
        const amountInput = document.getElementById('rzwAmountInput');
        if (amountInput) {
            amountInput.addEventListener('input', () => this.handleAmountChange());
            amountInput.addEventListener('keyup', () => this.handleAmountChange());
            amountInput.addEventListener('change', () => this.handleAmountChange());
        }

        // Auto renew checkbox
        const autoRenewCheckbox = document.querySelector('input[name="Input.AutoRenew"]');
        if (autoRenewCheckbox) {
            autoRenewCheckbox.addEventListener('change', () => this.validateForm());
        }

        // Form submission
        const form = document.querySelector('form');
        if (form) {
            form.addEventListener('submit', (e) => this.handleFormSubmit(e));
        }
    }

    loadInitialData() {
        // Get available balance from server-side data or fallback to data attribute
        if (window.rzwSavingsData && window.rzwSavingsData.availableBalance) {
            this.availableBalance = parseFloat(window.rzwSavingsData.availableBalance) || 0;
        } else {
            const balanceElement = document.querySelector('[data-available-balance]');
            if (balanceElement) {
                this.availableBalance = parseFloat(balanceElement.dataset.availableBalance) || 0;
            }
        }

        // Check for pre-selected plan
        const preSelectedRadio = document.querySelector('.plan-radio:checked');
        if (preSelectedRadio) {
            this.handlePlanSelection(preSelectedRadio);
        }
    }

    initializeForm() {
        this.validateForm();
        this.updatePreviewPlaceholder();
    }

    handlePlanSelection(radio) {
        // Update UI
        this.updatePlanCardSelection(radio);
        
        // Load plan details
        this.loadPlanDetails(radio.value);
        
        // Validate form
        this.validateForm();
    }

    updatePlanCardSelection(selectedRadio) {
        // Remove selected class from all cards
        document.querySelectorAll('.plan-card').forEach(card => {
            card.classList.remove('selected');
        });

        // Add selected class to the selected card
        const selectedCard = selectedRadio.closest('.plan-card');
        if (selectedCard) {
            selectedCard.classList.add('selected');
            selectedCard.classList.add('fade-in');
        }
    }

    async loadPlanDetails(planId) {
        try {
            const response = await fetch(`/MyAccount/RzwSavings/Create?handler=PlanDetails&planId=${planId}`);
            if (response.ok) {
                this.selectedPlan = await response.json();
                this.updateAmountLimits();
                this.calculateInterest();
                this.validateForm();
            } else {
                console.error('Failed to load plan details');
            }
        } catch (error) {
            console.error('Error loading plan details:', error);
        }
    }

    updateAmountLimits() {
        const limitsElement = document.getElementById('amountLimits');
        if (!limitsElement || !this.selectedPlan) return;

        // Get localized strings
        const strings = window.rzwSavingsData?.localizedStrings || {
            minimumAmount: 'Minimum amount',
            maximum: 'Maximum',
            unlimited: 'Unlimited'
        };

        let limitsText = `${strings.minimumAmount}: ${this.formatMinimumAmount(this.selectedPlan.minAmount)} RZW`;

        if (this.selectedPlan.maxAmount && this.selectedPlan.maxAmount > 0) {
            limitsText += `, ${strings.maximum}: ${this.formatNumber(this.selectedPlan.maxAmount)} RZW`;
        } else {
            limitsText += `, ${strings.maximum}: ${strings.unlimited}`;
        }

        limitsElement.textContent = limitsText;
    }

    handleAmountChange() {
        // Clear previous timeout
        if (this.calculationTimeout) {
            clearTimeout(this.calculationTimeout);
        }

        // Debounce calculation
        this.calculationTimeout = setTimeout(() => {
            this.calculateInterest();
            this.validateForm();
        }, 300);
    }

    async calculateInterest() {
        const amountInput = document.getElementById('rzwAmountInput');
        const amount = parseFloat(amountInput?.value) || 0;

        if (!this.selectedPlan || amount <= 0) {
            this.hideInterestPreview();
            return;
        }

        if (this.isCalculating) return;
        this.isCalculating = true;

        try {
            const response = await fetch(
                `/MyAccount/RzwSavings/Create?handler=CalculateInterest&amount=${amount}&interestRate=${this.selectedPlan.interestRate}&termDuration=${this.selectedPlan.termDuration}`
            );

            if (response.ok) {
                const data = await response.json();
                this.updateInterestPreview(data);
            } else {
                console.error('Failed to calculate interest');
                this.hideInterestPreview();
            }
        } catch (error) {
            console.error('Error calculating interest:', error);
            this.hideInterestPreview();
        } finally {
            this.isCalculating = false;
        }
    }

    updateInterestPreview(data) {
        const previewContainer = document.getElementById('interestPreview');
        const placeholder = document.getElementById('previewPlaceholder');

        if (!previewContainer || !placeholder) return;

        // Update preview values
        this.updatePreviewElement('previewInitial', `${data.initialAmount} RZW`);
        this.updatePreviewElement('previewDailyRate', `${(this.selectedPlan.interestRate * 100).toFixed(8).replace(/\.?0+$/, '')}%`);
        this.updatePreviewElement('previewDaily', `${data.dailyInterest} RZW`);
        this.updatePreviewElement('previewTotalInterest', `${data.totalInterest} RZW`);
        this.updatePreviewElement('previewFinal', `${data.finalAmount} RZW`);
        this.updatePreviewElement('previewApy', `${data.effectiveApy}%`);

        // Show preview, hide placeholder
        placeholder.style.display = 'none';
        previewContainer.style.display = 'block';
        previewContainer.classList.add('slide-up');
    }

    hideInterestPreview() {
        const previewContainer = document.getElementById('interestPreview');
        const placeholder = document.getElementById('previewPlaceholder');

        if (previewContainer && placeholder) {
            previewContainer.style.display = 'none';
            placeholder.style.display = 'block';
        }
    }

    updatePreviewElement(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = value;
        }
    }

    updatePreviewPlaceholder() {
        const placeholder = document.getElementById('previewPlaceholder');
        if (placeholder) {
            placeholder.innerHTML = `
                <i class="fas fa-chart-line"></i>
                <p>Select a plan and enter amount to see interest preview</p>
            `;
        }
    }

    validateForm() {
        const amountInput = document.getElementById('rzwAmountInput');
        const amount = parseFloat(amountInput?.value) || 0;
        const planSelected = document.querySelector('.plan-radio:checked') !== null;

        let isValid = true;
        const validationErrors = [];

        // Check if plan is selected
        if (!planSelected) {
            isValid = false;
            validationErrors.push('No plan selected');
        }

        // Check amount
        if (amount <= 0) {
            isValid = false;
            validationErrors.push('Amount must be greater than 0');
        }

        // Check amount limits
        if (this.selectedPlan && amount > 0) {
            if (amount < this.selectedPlan.minAmount) {
                isValid = false;
                validationErrors.push(`Amount below minimum: ${this.selectedPlan.minAmount}`);
            }
            
            if (this.selectedPlan.maxAmount && this.selectedPlan.maxAmount > 0 && amount > this.selectedPlan.maxAmount) {
                isValid = false;
                validationErrors.push(`Amount above maximum: ${this.selectedPlan.maxAmount}`);
            }
        }

        // Check available balance
        if (amount > this.availableBalance) {
            isValid = false;
            validationErrors.push(`Amount exceeds available balance: ${this.availableBalance}`);
        }

        this.updateSubmitButton(isValid);

        // Debug logging
        console.log('Form validation:', {
            amount,
            planSelected,
            selectedPlan: this.selectedPlan,
            availableBalance: this.availableBalance,
            isValid,
            errors: validationErrors
        });

        return isValid;
    }

    updateSubmitButton(isValid) {
        const submitButton = document.getElementById('createButton');
        if (!submitButton) return;

        submitButton.disabled = !isValid;
        
        if (isValid) {
            submitButton.classList.remove('btn-secondary');
            submitButton.classList.add('btn-primary');
        } else {
            submitButton.classList.remove('btn-primary');
            submitButton.classList.add('btn-secondary');
        }
    }

    handleFormSubmit(event) {
        if (!this.validateForm()) {
            event.preventDefault();
            return false;
        }
        return true;
    }

    formatNumber(number) {
        return number.toLocaleString('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 8
        });
    }

    formatMinimumAmount(number) {
        return number.toLocaleString('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new RzwSavingsCreate();
});
