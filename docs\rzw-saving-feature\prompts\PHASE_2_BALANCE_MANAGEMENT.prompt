# RZW Vadeli He<PERSON> - Faz 2: Balance Management

## 🎯 AI Agent Görevi
RZW bakiye yönetimi servisleri oluştur. Mevcut WalletService'i genişlet ve RZW-specific balance operations ekle. Backward compatibility sağla.

## 📋 Detaylı Görevler

### 1. WalletService Interface Genişletme
**Dosya**: `src/Areas/Admin/Services/Interfaces/IWalletService.cs`

#### Backward Compatibility Metotları (Obsolete ile işaretle)
- GetUserBalanceAsync → GetUserAvailableBalanceAsync
- AddBalanceAsync → AddAvailableBalanceAsync  
- DeductBalanceAsync → DeductAvailableBalanceAsync
- AddToWalletAsync → AddToAvailableBalanceAsync

#### Yeni Available Balance Metotları
- GetUserAvailableBalanceAsync(userId, coinId)
- AddAvailableBalanceAsync(userId, coinId, amount)
- DeductAvailableBalanceAsync(userId, coinId, amount)
- AddToAvailableBalanceAsync(userId, coinId, amount)

#### Yeni Locked Balance Metotları
- GetUserLockedBalanceAsync(userId, coinId)
- LockBalanceAsync(userId, coinId, amount)
- UnlockBalanceAsync(userId, coinId, amount)
- AddLockedBalanceAsync(userId, coinId, amount)
- DeductLockedBalanceAsync(userId, coinId, amount)

#### Yeni Total Balance Metotları
- GetUserTotalBalanceAsync(userId, coinId)

#### Yeni Balance Info Metotları
- GetWalletBalanceInfoAsync(userId, coinId)
- GetUserAllBalanceInfoAsync(userId)

### 2. WalletService Implementation Güncelleme
**Dosya**: `src/Areas/Admin/Services/WalletService.cs`

#### Constructor Dependencies Ekle
- TradeService (audit trail için)
- ITokenPriceService (RZW token ID için)

#### Önemli İş Kuralları
- Available balance = BALANCE kolonu
- Locked balance = LOCKED_BALANCE kolonu
- Total balance = Available + Locked (hesaplanmış)
- Her wallet değişikliğinde Trade kaydı oluştur
- Database transaction'ları kullan

### 3. Balance Info Models Oluştur

#### WalletBalanceInfo.cs
**Dosya**: `src/Models/WalletBalanceInfo.cs`
- UserId, CoinId, CoinCode, CoinName, IconUrl
- AvailableBalance, LockedBalance, TotalBalance
- RZW özel alanlar: LockedInSavings, SavingsLocks

#### RzwBalanceInfo.cs
**Dosya**: `src/Models/RzwBalanceInfo.cs`
- UserId, AvailableRzw, LockedRzw, TotalRzw
- LockedInSavings, ActiveSavingsCount, SavingsLocks

#### SavingsLockInfo.cs
- SavingsAccountId, Amount, MaturityDate
- PlanName, InterestRate

### 4. RzwBalanceManagementService Oluştur
**Dosya**: `src/Areas/Admin/Services/RzwBalanceManagementService.cs`

#### Dependencies
- AppDbContext
- IWalletService
- ITokenPriceService (RZW token ID için)
- TradeService (audit trail için)

#### Ana Metotlar
- GetRzwTokenIdAsync() - TokenPriceService'den dinamik al
- HasSufficientAvailableRzwAsync(userId, amount)
- GetRzwBalanceInfoAsync(userId)
- LockRzwForSavingsAsync(userId, amount, description)
- UnlockRzwFromSavingsAsync(userId, amount, description)
- AddRzwInterestAsync(userId, amount, description)
- DeductAvailableRzwAsync(userId, amount)

#### Önemli Özellikler
- TokenPriceService kullanarak RZW token ID'sini dinamik al
- Her işlemde Trade kaydı oluştur
- Transaction safety sağla
- Comprehensive error handling

## ✅ Başarı Kriterleri
1. Backward compatibility: Eski metotlar çalışıyor
2. Balance operations: Lock/unlock işlemleri doğru çalışıyor
3. Trade records: Her wallet değişikliğinde Trade kaydı oluşuyor
4. RZW operations: RZW-specific işlemler çalışıyor
5. TokenPriceService: RZW token ID dinamik olarak alınıyor
6. Build hataları yok

## 🔧 Test Senaryoları
```csharp
// Test 1: RZW balance kontrolü
var balance = await rzwBalanceService.GetRzwBalanceInfoAsync(userId);

// Test 2: RZW kilitleme
var success = await rzwBalanceService.LockRzwForSavingsAsync(userId, 1000m, "Test");

// Test 3: Trade kaydı kontrolü
// TRADE tablosunda RzwSavingsDeposit kaydının oluştuğunu kontrol et

// Test 4: Backward compatibility
var oldBalance = await walletService.GetUserBalanceAsync(userId, coinId); // Obsolete
var newBalance = await walletService.GetUserAvailableBalanceAsync(userId, coinId); // Yeni
// İkisi aynı değeri döndürmeli
```

## 📝 Önemli Notlar
- **Mevcut kodu bozmama**: Obsolete metotlar eski kodu destekler
- **TokenPriceService kullan**: RZW token ID için sabit değer kullanma
- **Trade audit**: Her wallet değişikliğinde Trade kaydı oluştur
- **Transaction safety**: Database transaction'ları kullan
- **Error handling**: Tüm işlemlerde hata kontrolü yap
- **Naming consistency**: available/locked/total terimleri kullan

## 🎯 Sonuç Beklentisi
Bu faz sonunda:
- WalletService genişletilmiş olacak
- RZW-specific balance operations hazır olacak
- Backward compatibility sağlanmış olacak
- Trade audit trail çalışır olacak
- Sonraki fazlar için balance management hazır olacak

**Tahmini Süre**: 2-3 gün
**Bağımlılıklar**: Faz 1 tamamlanmış olmalı
**Sonraki Faz**: Savings System Core
