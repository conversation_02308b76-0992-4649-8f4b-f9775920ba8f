# RazeWin Reward Distribution System Tests

This project contains unit tests for the RazeWin reward distribution system. The tests are designed to verify that the reward distribution system works correctly in various scenarios.

## Test Categories

1. **Basic Tests** (`ReferralRewardServiceTests.cs`)
   - Tests for basic functionality of the reward distribution system
   - Tests for handling of edge cases like no referrers, no packages, etc.

2. **Complex Tests** (`ReferralRewardComplexTests.cs`)
   - Tests for complex scenarios like multiple package types, mix of active and inactive packages, etc.
   - Tests for handling of large referral chains and different RZW prices

3. **Edge Case Tests** (`ReferralRewardEdgeCaseTests.cs`)
   - Tests for extreme edge cases like zero amounts, circular referrals, etc.
   - Tests for error handling and decimal precision

4. **Summary Tests** (`ReferralRewardSummaryTests.cs`)
   - Tests for the reward summary functionality
   - Tests for user reward summaries and payment reward summaries

## Running the Tests

To run the tests, use the following command:

```
dotnet test
```

## Test Coverage

The tests cover the following aspects of the reward distribution system:

- Processing of payment rewards
- Calculation of reward amounts based on percentages
- Handling of referral chains
- Handling of different package types and reward percentages
- Error handling and edge cases
- Reward summary functionality

## Dependencies

The tests use the following dependencies:

- xUnit for the test framework
- Moq for mocking dependencies
- Entity Framework Core In-Memory for database testing
