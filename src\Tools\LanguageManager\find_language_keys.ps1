# Script to find all language keys used in the project
# Usage: .\find_language_keys.ps1 -ProjectDir "../../" -OutputFile "all_keys.txt"

param (
    [Parameter(Mandatory = $true)]
    [string]$ProjectDir,

    [Parameter(Mandatory = $true)]
    [string]$OutputFile
)

# Get the script directory to use as a reference point
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Write-Host "Script directory: $scriptDir"

# Resolve the project directory path
try {
    # If ProjectDir is a relative path, resolve it relative to the script directory
    if (-not [System.IO.Path]::IsPathRooted($ProjectDir)) {
        $ProjectDir = Join-Path -Path $scriptDir -ChildPath $ProjectDir
    }

    # Ensure the path exists
    if (-not (Test-Path $ProjectDir)) {
        Write-Error "Project directory not found: $ProjectDir"
        Write-Host "Current script directory: $scriptDir"
        exit 1
    }

    # Get the absolute path
    $ProjectDir = (Resolve-Path $ProjectDir).Path
    Write-Host "Project directory resolved to: $ProjectDir"
} catch {
    Write-Error "Error resolving project directory: $_"
    exit 1
}

# Initialize an array to store all found keys
$allKeys = @()
$nonStandardKeys = @()

# Define patterns to search for
$patterns = @(
    '@L\["([^"]+)"\]',                  # @L["key"]
    '@Localizer\["([^"]+)"\]',          # @Localizer["key"]
    '_localizer\["([^"]+)"\]',          # _localizer["key"]
    'window\.t\["([^"]+)"\]'            # window.t["key"]
)

# Define file extensions to search in
$extensions = @("*.cshtml", "*.cs", "*.js")

# Find all files with the specified extensions
$files = @()
foreach ($ext in $extensions) {
    $files += Get-ChildItem -Path $ProjectDir -Filter $ext -Recurse -File
}

Write-Host "Found $($files.Count) files to search"

# Function to check if a key follows the standard format (English, Pascal Case)
function Test-StandardKey {
    param (
        [string]$Key
    )

    # Check if the key contains non-ASCII characters
    $nonAsciiPattern = '[^\x00-\x7F]'
    if ($Key -match $nonAsciiPattern) {
        return $false
    }

    # Additional checks can be added here
    return $true
}

# Search for patterns in each file
foreach ($file in $files) {
    try {
        # Read file with UTF-8 encoding
        $content = [System.IO.File]::ReadAllText($file.FullName, [System.Text.Encoding]::UTF8)

        foreach ($pattern in $patterns) {
            $regexMatches = [regex]::Matches($content, $pattern)

            foreach ($match in $regexMatches) {
                $key = $match.Groups[1].Value
                if (-not [string]::IsNullOrWhiteSpace($key)) {
                    # Check if the key is already in the list
                    if ($key -notin $allKeys) {
                        $allKeys += $key

                        # Check if the key follows the standard format
                        if (-not (Test-StandardKey -Key $key)) {
                            $nonStandardKeys += @{
                                Key = $key
                                File = $file.FullName
                                Line = $content.Substring(0, $match.Index).Split("`n").Length
                            }
                        }
                    }
                }
            }
        }
    } catch {
        Write-Warning "Error processing file $($file.FullName): $_"
    }
}

# Sort the keys alphabetically
$allKeys = $allKeys | Sort-Object

# Write the keys to the output file
$allKeys | Out-File -FilePath $OutputFile -Encoding UTF8

# Write non-standard keys to a separate file
if ($nonStandardKeys.Count -gt 0) {
    $nonStandardKeysFile = [System.IO.Path]::Combine([System.IO.Path]::GetDirectoryName($OutputFile), "non_standard_keys.txt")
    $nonStandardKeysOutput = @()

    foreach ($item in $nonStandardKeys) {
        $nonStandardKeysOutput += "Key: $($item.Key)"
        $nonStandardKeysOutput += "File: $($item.File)"
        $nonStandardKeysOutput += "Line: $($item.Line)"
        $nonStandardKeysOutput += "---"
    }

    $nonStandardKeysOutput | Out-File -FilePath $nonStandardKeysFile -Encoding UTF8
    Write-Host "Found $($nonStandardKeys.Count) non-standard keys (written to $nonStandardKeysFile)"
}

Write-Host "Found $($allKeys.Count) unique language keys"
Write-Host "Keys written to $OutputFile"

# Return the keys
return $allKeys
