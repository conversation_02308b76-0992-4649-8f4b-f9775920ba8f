using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels;

namespace RazeWinComTr.Areas.Admin.Pages.Trade;

public class DeleteModel : PageModel
{
    private readonly ITradeService _tradeService;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public DeleteModel(
        ITradeService tradeService,
        IStringLocalizer<SharedResource> localizer)
    {
        _tradeService = tradeService;
        _localizer = localizer;
    }

    public SweetAlert2Message? AlertMessage { get; set; }

    [BindProperty] public int Id { get; set; }

    public DbModel.Trade? Entity { get; set; }
    public string? ErrorMessage { get; set; }

    public async Task<IActionResult> OnGetAsync(int id)
    {
        Id = id;
        Entity = await _tradeService.GetByIdAsync(id);

        if (Entity == null) return NotFound();

        ViewData["WarningTitle"] = _localizer["Warning"];
        ViewData["WarningRecord"] = _localizer["This trade record will be permanently deleted"];
        ViewData["WarningBalances"] = _localizer["Deleting this trade will not update user balances"];
        ViewData["WarningUnrecoverable"] = _localizer["This action is irreversible"];

        return Page();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        try
        {
            await _tradeService.DeleteAsync(Id);

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Success"],
                Text = _localizer["Deletion was successful"],
                Icon = "success",
                RedirectUrl = "/Admin/Trade"
            };

            return Page();
        }
        catch (Exception ex)
        {
            ErrorMessage = ex.Message;
            Entity = await _tradeService.GetByIdAsync(Id);
            return Page();
        }
    }
}
