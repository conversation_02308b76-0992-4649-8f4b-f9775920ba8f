using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.Constants;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Net;

namespace RazeWinComTr.Areas.Admin.Pages.Market;

public class EditModel : PageModel
{
    private readonly IMarketService _marketService;
    private readonly string _fileStoragePath;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ILogger<EditModel> _logger;

    public EditModel(
        IMarketService marketService,
        IStringLocalizer<SharedResource> localizer,
        IConfiguration configuration,
        IHttpClientFactory httpClientFactory,
        ILogger<EditModel> logger)
    {
        _marketService = marketService;
        _localizer = localizer;
        _fileStoragePath = configuration["FileStoragePath"]!;
        _httpClientFactory = httpClientFactory;
        _logger = logger;
    }

    public SweetAlert2Message? AlertMessage { get; set; }

    [BindProperty] public MarketEditViewModel Entity { get; set; } = new();

    [BindProperty] public IFormFile? ImageFile { get; set; }

    public string? ErrorMessage { get; set; }

    public async Task<IActionResult> OnGetAsync(int id)
    {
        var entity = await _marketService.GetByIdAsync(id);

        if (entity == null) return NotFound();
        bool isApiEnabled = entity.IsApi == 1;

        Entity = new MarketEditViewModel
        {
            Id = entity.Id,
            Coin = entity.Coin,
            Name = entity.Name,
            ShortName = entity.ShortName,
            PairCode = entity.PairCode,
            BuyPrice = entity.BuyPrice,
            SellPrice = entity.SellPrice,
            DecimalPlaces = entity.DecimalPlaces,
            MinimumBuy = entity.MinimumBuy,
            MaximumBuy = entity.MaximumBuy,
            MinimumSell = entity.MinimumSell,
            MaximumSell = entity.MaximumSell,
            GeneralIncrease = entity.GeneralIncrease,
            Order = entity.Order,
            IsApi = isApiEnabled,
            OriginalIsApi = isApiEnabled, // Store the original value
            ApiServiceName = entity.ApiServiceName,
            IsActive = entity.IsActive == 1,
            IconUrl = entity.IconUrl
        };

        return Page();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        try
        {
            // Ensure ApiServiceName is null when IsApi is false
            if (!Entity.IsApi)
            {
                Entity.ApiServiceName = null;
            }

            // Parse decimal values from string inputs with comma as decimal separator
            if (ModelState.IsValid)
            {
                // Convert any comma-formatted decimal values to proper decimal values
                ConvertDecimalFields();
            }

            if (!ModelState.IsValid) return Page();
            var userId = User.GetClaimUserId();
            if (!userId.HasValue) return Unauthorized();
            var entity = await _marketService.GetByIdAsync(Entity.Id);
            if (entity == null) return NotFound();

            // Create the markets directory if it doesn't exist
            var marketsDirectory = Path.Combine(_fileStoragePath, "markets");
            Directory.CreateDirectory(marketsDirectory);

            // Determine the file name based on the coin name
            var fileName = $"{Entity.Coin.ToLower()}.png";
            var filePath = Path.Combine(marketsDirectory, fileName);

            if (ImageFile != null)
            {
                // User uploaded an image, save it
                using var stream = new FileStream(filePath, FileMode.Create);
                await ImageFile.CopyToAsync(stream);

                // Delete old file if it exists and is different from the new file
                if (!string.IsNullOrEmpty(Entity.IconUrl))
                {
                    var oldFilePath = Path.Combine(_fileStoragePath, Entity.IconUrl);
                    if (System.IO.File.Exists(oldFilePath) && oldFilePath != filePath)
                    {
                        System.IO.File.Delete(oldFilePath);
                    }
                }
            }
            else if (string.IsNullOrEmpty(entity.IconUrl))
            {
                // No image uploaded and no existing image, try to get from Bitexen if applicable or use default
                if (Entity.IsApi && Entity.ApiServiceName == ApiServiceNames.Bitexen)
                {
                    // Try to get the image from Bitexen
                    var coinName = Entity.Coin.ToLower();
                    var bitexenImageUrl = $"https://statics.bitexen.com/logos/color/{coinName}.png";

                    if (await TryDownloadImageAsync(bitexenImageUrl, filePath))
                    {
                        _logger.LogInformation("Downloaded coin logo from Bitexen for {Coin}", Entity.Coin);
                    }
                    else
                    {
                        // If download fails, copy the default image
                        await CopyDefaultImageAsync(filePath);
                    }
                }
                else
                {
                    // Copy the default image
                    await CopyDefaultImageAsync(filePath);
                }
            }

            // Set the icon URL to the relative path if it was updated
            if (ImageFile != null || string.IsNullOrEmpty(entity.IconUrl))
            {
                var iconUrl = Path.GetRelativePath(_fileStoragePath, filePath).Replace("\\", "/");
                entity.IconUrl = iconUrl;
            }

            // If IsApi is true, don't update API-related fields (Coin, Name, ShortName, PairCode, BuyPrice, SellPrice)
            // This ensures that even if the client-side validation is bypassed, the server won't update these fields
            if (Entity.IsApi)
            {
                // For API markets, these fields should not be updated from the form
                // They should only be updated by the API service
                // BuyPrice and SellPrice are updated by the API service
            }
            else
            {
                entity.Coin = Entity.Coin;
                entity.Name = Entity.Name;
                entity.ShortName = Entity.ShortName;
                entity.PairCode = Entity.PairCode;
                // Only update BuyPrice and SellPrice for non-API markets
                entity.BuyPrice = Entity.BuyPrice ?? 0;
                entity.SellPrice = Entity.SellPrice ?? 0;
            }

            // Always update these fields regardless of IsApi status
            entity.GeneralIncrease = Entity.GeneralIncrease; // Always allow updating GeneralIncrease
            entity.DecimalPlaces = Entity.DecimalPlaces;
            entity.MinimumBuy = Entity.MinimumBuy ?? 0;
            entity.MaximumBuy = Entity.MaximumBuy ?? 0;
            entity.MinimumSell = Entity.MinimumSell ?? 0;
            entity.MaximumSell = Entity.MaximumSell ?? 0;
            entity.Order = Entity.Order;

            // Ensure that if IsApi was originally true, it cannot be changed to false
            // This is a server-side protection in case client-side validation is bypassed
            if (entity.IsApi == 1)
            {
                // If it was originally true, keep it true regardless of what was submitted
                entity.IsApi = 1;

                // Make sure the form value is also set to true for consistency
                Entity.IsApi = true;
            }
            else
            {
                // Only allow changing from false to true, not from true to false
                entity.IsApi = Entity.IsApi ? 1 : 0;
            }

            entity.ApiServiceName = Entity.ApiServiceName; // ApiServiceName is already set to null if IsApi is false
            entity.IsActive = Entity.IsActive ? 1 : 0;
            entity.ModDate = DateTime.UtcNow;

            await _marketService.UpdateAsync(entity);

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Success"],
                Text = _localizer["Successfully updated"],
                Icon = "success",
                RedirectUrl = "/Admin/Market"
            };

            return Page();
        }
        catch (Exception ex)
        {
            ErrorMessage = ex.Message;
            return Page();
        }
    }

    private void ConvertDecimalFields()
    {
        // This method handles conversion of string inputs with comma as decimal separator
        // to proper decimal values using invariant culture

        // Get form values directly to handle comma decimal separators
        if (Request.Form.TryGetValue("Entity.BuyPrice", out var buyPriceStr) && !string.IsNullOrEmpty(buyPriceStr))
        {
            string normalizedValue = buyPriceStr.ToString().Replace(',', '.');
            if (decimal.TryParse(normalizedValue, NumberStyles.Any, CultureInfo.InvariantCulture, out decimal result))
            {
                Entity.BuyPrice = result;
            }
        }

        if (Request.Form.TryGetValue("Entity.SellPrice", out var sellPriceStr) && !string.IsNullOrEmpty(sellPriceStr))
        {
            string normalizedValue = sellPriceStr.ToString().Replace(',', '.');
            if (decimal.TryParse(normalizedValue, NumberStyles.Any, CultureInfo.InvariantCulture, out decimal result))
            {
                Entity.SellPrice = result;
            }
        }

        if (Request.Form.TryGetValue("Entity.MinimumBuy", out var minBuyStr) && !string.IsNullOrEmpty(minBuyStr))
        {
            string normalizedValue = minBuyStr.ToString().Replace(',', '.');
            if (decimal.TryParse(normalizedValue, NumberStyles.Any, CultureInfo.InvariantCulture, out decimal result))
            {
                Entity.MinimumBuy = result;
            }
        }

        if (Request.Form.TryGetValue("Entity.MaximumBuy", out var maxBuyStr) && !string.IsNullOrEmpty(maxBuyStr))
        {
            string normalizedValue = maxBuyStr.ToString().Replace(',', '.');
            if (decimal.TryParse(normalizedValue, NumberStyles.Any, CultureInfo.InvariantCulture, out decimal result))
            {
                Entity.MaximumBuy = result;
            }
        }

        if (Request.Form.TryGetValue("Entity.MinimumSell", out var minSellStr) && !string.IsNullOrEmpty(minSellStr))
        {
            string normalizedValue = minSellStr.ToString().Replace(',', '.');
            if (decimal.TryParse(normalizedValue, NumberStyles.Any, CultureInfo.InvariantCulture, out decimal result))
            {
                Entity.MinimumSell = result;
            }
        }

        if (Request.Form.TryGetValue("Entity.MaximumSell", out var maxSellStr) && !string.IsNullOrEmpty(maxSellStr))
        {
            string normalizedValue = maxSellStr.ToString().Replace(',', '.');
            if (decimal.TryParse(normalizedValue, NumberStyles.Any, CultureInfo.InvariantCulture, out decimal result))
            {
                Entity.MaximumSell = result;
            }
        }
    }

    /// <summary>
    /// Tries to download an image from the specified URL and save it to the specified path
    /// </summary>
    /// <param name="imageUrl">The URL of the image to download</param>
    /// <param name="savePath">The path where the image should be saved</param>
    /// <returns>True if the download was successful, false otherwise</returns>
    private async Task<bool> TryDownloadImageAsync(string imageUrl, string savePath)
    {
        try
        {
            var client = _httpClientFactory.CreateClient();
            // Set a timeout to avoid hanging if the server doesn't respond
            client.Timeout = TimeSpan.FromSeconds(5);

            // First check if the image exists by sending a HEAD request
            var headResponse = await client.SendAsync(new HttpRequestMessage(HttpMethod.Head, imageUrl));
            if (!headResponse.IsSuccessStatusCode)
            {
                return false;
            }

            // Download the image
            var response = await client.GetAsync(imageUrl);
            if (response.IsSuccessStatusCode)
            {
                using var stream = await response.Content.ReadAsStreamAsync();
                using var fileStream = new FileStream(savePath, FileMode.Create);
                await stream.CopyToAsync(fileStream);
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error downloading image from {ImageUrl}", imageUrl);
            return false;
        }
    }

    /// <summary>
    /// Copies the default coin image to the specified path
    /// </summary>
    /// <param name="destinationPath">The path where the default image should be copied</param>
    private async Task CopyDefaultImageAsync(string destinationPath)
    {
        try
        {
            var defaultImagePath = Path.Combine("wwwroot", "site", "images", "noimg_coin.png");

            if (System.IO.File.Exists(defaultImagePath))
            {
                using var sourceStream = new FileStream(defaultImagePath, FileMode.Open, FileAccess.Read);
                using var destinationStream = new FileStream(destinationPath, FileMode.Create);
                await sourceStream.CopyToAsync(destinationStream);
                _logger.LogInformation("Copied default coin image to {DestinationPath}", destinationPath);
            }
            else
            {
                _logger.LogWarning("Default coin image not found at {DefaultImagePath}", defaultImagePath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error copying default coin image to {DestinationPath}", destinationPath);
        }
    }
}

public class MarketEditViewModel
{
    public int Id { get; set; }

    // This property is used to store the original IsApi value for validation
    [BindNever]
    public bool OriginalIsApi { get; set; }

    [Required(ErrorMessage = "Coin Symbol is required")]
    [StringLength(50, ErrorMessage = "Coin Symbol cannot exceed 50 characters")]
    public string Coin { get; set; } = null!;

    [Required(ErrorMessage = "Name is required")]
    [StringLength(255, ErrorMessage = "Name cannot exceed 255 characters")]
    public string Name { get; set; } = null!;

    [Required(ErrorMessage = "Short Name is required")]
    [StringLength(255, ErrorMessage = "Short Name cannot exceed 255 characters")]
    public string ShortName { get; set; } = null!;

    [Required(ErrorMessage = "Pair Code is required")]
    [StringLength(50, ErrorMessage = "Pair Code cannot exceed 50 characters")]
    public string PairCode { get; set; } = null!;

    [Required(ErrorMessage = "Buy Price is required")]
    [Range(0, double.MaxValue, ErrorMessage = "Buy Price must be a positive number")]
    public decimal? BuyPrice { get; set; }

    [Required(ErrorMessage = "Sell Price is required")]
    [Range(0, double.MaxValue, ErrorMessage = "Sell Price must be a positive number")]
    public decimal? SellPrice { get; set; }

    [Required(ErrorMessage = "Decimal Places is required")]
    [Range(0, 8, ErrorMessage = "Decimal Places must be between 0 and 8")]
    public int DecimalPlaces { get; set; }

    [Required(ErrorMessage = "Minimum Buy is required")]
    [Range(0, double.MaxValue, ErrorMessage = "Minimum Buy must be a positive number")]
    public decimal? MinimumBuy { get; set; }

    [Required(ErrorMessage = "Maximum Buy is required")]
    [Range(0, double.MaxValue, ErrorMessage = "Maximum Buy must be a positive number")]
    public decimal? MaximumBuy { get; set; }

    [Required(ErrorMessage = "Minimum Sell is required")]
    [Range(0, double.MaxValue, ErrorMessage = "Minimum Sell must be a positive number")]
    public decimal? MinimumSell { get; set; }

    [Required(ErrorMessage = "Maximum Sell is required")]
    [Range(0, double.MaxValue, ErrorMessage = "Maximum Sell must be a positive number")]
    public decimal? MaximumSell { get; set; }

    [Required(ErrorMessage = "General Increase is required")]
    [Range(-double.MaxValue, double.MaxValue, ErrorMessage = "General Increase must be a valid number")]
    public decimal GeneralIncrease { get; set; }

    [Required(ErrorMessage = "Order is required")]
    public int Order { get; set; }

    public bool IsApi { get; set; }

    [StringLength(50, ErrorMessage = "API Service Name cannot exceed 50 characters")]
    public string? ApiServiceName { get; set; }

    public bool IsActive { get; set; }

    public string? IconUrl { get; set; }
}
