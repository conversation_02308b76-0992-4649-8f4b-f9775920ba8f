/* Common Package Display Styling */
/* Packages Page Styles */
.packages-container {
    padding: 2rem 0;
}

.package-card {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
}

.package-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.package-card.current {
    border-color: #4CAF50;
}

.package-card.higher {
    border-color: #2196F3;
}

.package-card.lower {
    border-color: #9E9E9E;
    opacity: 0.8;
}

.package-header {
    padding: 2rem;
    text-align: center;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: white;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.package-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: white;
}

.package-price {
    font-size: 2.5rem;
    font-weight: 700;
    color: #fff;
    margin-bottom: 0.5rem;
}

.package-price small {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
}

.package-body {
    padding: 2rem;
    background: #fff;
}

.package-description {
    color: #666;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
    line-height: 1.6;
}

.package-features {
    list-style: none;
    padding: 0;
    margin: 0 0 2rem 0;
}

.package-features li {
    padding: 0.75rem 0;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    color: #495057;
}

.package-features li:last-child {
    border-bottom: none;
}

.package-features li i {
    color: #4CAF50;
    margin-right: 0.75rem;
}

.package-rewards {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    margin-top: 1.5rem;
}

.package-rewards h4 {
    color: #1a1a2e;
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.reward-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e9ecef;
}

.reward-item:last-child {
    border-bottom: none;
}

.reward-level {
    font-weight: 500;
    color: #2c3e50;
}

.reward-percentage {
    font-weight: 600;
    color: #2196F3;
    background: rgba(33, 150, 243, 0.1);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
}

.package-footer {
    padding: 1.5rem 2rem;
    text-align: center;
    background: #f8f9fa;
    border-top: 1px solid #eee;
}

.package-button {
    width: 100%;
    padding: 1rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.package-button.current {
    background: #4CAF50;
    color: white;
}

.package-button.higher {
    background: #2196F3;
    color: white;
}

.package-button.lower {
    background: #9E9E9E;
    color: white;
    opacity: 0.8;
}

.package-button:disabled {
    cursor: not-allowed;
    opacity: 0.7;
}

.package-status {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    z-index: 1;
}

.status-current {
    background: #4CAF50;
    color: white;
}

.status-available {
    background: #2196F3;
    color: white;
}

.status-locked {
    background: #9E9E9E;
    color: white;
}

/* Responsive Breakpoints */
@media (max-width: 1200px) {
    .package-title {
        font-size: 1.3rem;
    }
    
    .package-price {
        font-size: 2rem;
    }
}

@media (max-width: 992px) {
    .packages-container {
        padding: 1rem 0;
    }
    
    .package-card {
        margin-bottom: 2rem;
    }
}

@media (max-width: 768px) {
    .package-header {
        padding: 1.5rem;
    }
    
    .package-body {
        padding: 1.5rem;
    }
    
    .package-footer {
        padding: 1rem 1.5rem;
    }
    
    .package-title {
        font-size: 1.2rem;
    }
    
    .package-price {
        font-size: 1.8rem;
    }
}

@media (max-width: 576px) {
    .package-card {
        margin-bottom: 1.5rem;
    }
    
    .package-features li {
        font-size: 0.9rem;
    }
    
    .reward-item {
        font-size: 0.9rem;
    }
    
    .package-status {
        font-size: 0.7rem;
        padding: 0.4rem 0.8rem;
    }
}

/* Animation Classes */
.package-card {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
