# RZW Vadeli He<PERSON> - AI Agent Prompt Koleksiyonu

Bu klasör, RZW vadeli hesap sisteminin artırımlı olarak geliştirilmesi için hazırlanmış AI agent promptlarını içerir.

## 📋 Faz Listesi

### Faz 1: Database & Models
**Dosya**: `PHASE_1_DATABASE_MODELS.prompt`
**Süre**: 1-2 gün
**Bağımlılık**: Yok

**Görevler**:
- Wallet entity'sine LOCKED_BALANCE kolonu ekleme
- RZW Savings entity'leri olu<PERSON>turma (Account, Plan, InterestPayment)
- TradeType enum güncelleme
- Migration oluşturma
- AppDbContext güncelleme

**Çıktı**: Temel database yapısı hazır

---

### Faz 2: Balance Management
**Dosya**: `PHASE_2_BALANCE_MANAGEMENT.prompt`
**Süre**: 2-3 gün
**Bağımlılık**: Faz 1

**Görevler**:
- WalletService interface genişletme (backward compatibility)
- RzwBalanceManagementService oluşturma
- Balance info models (WalletBalanceInfo, RzwBalanceInfo)
- Lock/unlock balance operations
- Trade audit trail entegrasyonu

**Çıktı**: RZW bakiye yönetimi servisleri hazır

---

### Faz 3: Savings System Core
**Dosya**: `PHASE_3_SAVINGS_SYSTEM_CORE.prompt`
**Süre**: 3-4 gün
**Bağımlılık**: Faz 1, 2

**Görevler**:
- RzwSavingsPlanService (plan yönetimi)
- RzwSavingsService (vadeli hesap CRUD)
- RzwSavingsInterestService (faiz hesaplama)
- Bileşik faiz algoritmaları
- Erken çekim ve vade dolma işlemleri

**Çıktı**: Core business logic tamamlandı

---

### Faz 4: Background Services
**Dosya**: `PHASE_4_BACKGROUND_SERVICES.prompt`
**Süre**: 1-2 gün
**Bağımlılık**: Faz 1, 2, 3

**Görevler**:
- RzwSavingsBackgroundService oluşturma
- Otomatik günlük faiz ödemeleri
- Vade dolma kontrolleri
- Batch processing ve error handling
- Health check ve monitoring

**Çıktı**: Otomatik işlemler çalışır durumda

---

### Faz 5: User Interface
**Dosya**: `PHASE_5_USER_INTERFACE.prompt`
**Süre**: 3-4 gün
**Bağımlılık**: Faz 1, 2, 3, 4

**Görevler**:
- Wallet sayfası güncellemeleri (RZW özel kart)
- RZW Savings sayfaları (Index, Create, Detail)
- ViewModels ve form validasyonları
- JavaScript enhancements
- Responsive design ve localization

**Çıktı**: Kullanıcı arayüzü tamamlandı

---

### Faz 6: Admin Panel & Testing
**Dosya**: `PHASE_6_ADMIN_PANEL_TESTING.prompt`
**Süre**: 2-3 gün
**Bağımlılık**: Tüm önceki fazlar

**Görevler**:
- Admin panel (plan yönetimi, hesap yönetimi)
- Raporlama sistemi
- System monitoring
- Comprehensive testing (Unit, Integration, Performance)
- Documentation

**Çıktı**: Production-ready sistem

---

## 🚀 Kullanım Talimatları

### 1. Sıralı İlerleme
Fazları sırasıyla tamamlayın. Her faz bir öncekine bağımlıdır.

### 2. AI Agent'a Prompt Verme
```bash
# Örnek kullanım
AI Agent'a şu prompt'u verin:
"docs/rzw-saving-feature/prompts/PHASE_1_DATABASE_MODELS.prompt dosyasındaki görevleri tamamla"
```

### 3. Her Faz Sonunda Test
Her faz tamamlandıktan sonra:
- Build kontrolü yapın
- Test senaryolarını çalıştırın
- Başarı kriterlerini kontrol edin
- Commit yapın

### 4. Sorun Giderme
Eğer bir fazda sorun yaşarsanız:
- Prompt'taki "Önemli Notlar" bölümünü kontrol edin
- Bağımlılıkların tamamlandığından emin olun
- Test senaryolarını tek tek çalıştırın

## 📊 Toplam Proje Özeti

**Toplam Tahmini Süre**: 11-18 gün
**Toplam Faz Sayısı**: 6
**Ana Özellikler**:
- RZW Token için vadeli hesap sistemi
- Günlük, aylık, yıllık vade seçenekleri
- Bileşik faiz hesaplama
- Erken çekim (ceza ile)
- Otomatik faiz ödemeleri
- Admin panel ve raporlama
- Comprehensive testing

## 🎯 Başarı Kriterleri (Genel)

### Teknik
- ✅ Mevcut sistem etkilenmedi
- ✅ Backward compatibility sağlandı
- ✅ Database migration'ları çalışıyor
- ✅ Trade audit trail oluşuyor
- ✅ Background service çalışıyor
- ✅ Unit test coverage %90+

### Fonksiyonel
- ✅ Kullanıcı vadeli hesap açabiliyor
- ✅ Faizler otomatik hesaplanıp ödeniyor
- ✅ Erken çekim çalışıyor
- ✅ Vade dolma otomatik işleniyor
- ✅ Admin panel çalışıyor
- ✅ Raporlar oluşturuluyor

### Kullanıcı Deneyimi
- ✅ Responsive tasarım
- ✅ Form validasyonları
- ✅ JavaScript enhancements
- ✅ Localization (TR/EN)
- ✅ Kullanıcı dostu arayüz

## 📝 Önemli Hatırlatmalar

1. **Mevcut sistemi bozmayın**: Her değişiklik backward compatible olmalı
2. **TokenPriceService kullanın**: RZW token ID için sabit değer kullanmayın
3. **Trade audit**: Her wallet değişikliğinde Trade kaydı oluşturun
4. **Transaction safety**: Database transaction'ları kullanın
5. **Error handling**: Kapsamlı hata kontrolü yapın
6. **Testing**: Her faz sonunda test edin
7. **Documentation**: Değişiklikleri dokümante edin

## 🔗 İlgili Dosyalar

- `RZW_SAVINGS_MASTER_PLAN.md` - Ana plan dokümantasyonu
- `RZW_SAVINGS_IMPLEMENTATION_PLAN.md` - Detaylı uygulama planı
- `RZW_SAVINGS_PHASE_*.md` - Faz detay dokümantasyonları

---

**Hazırlayan**: AI Assistant
**Tarih**: 2024-12-19
**Versiyon**: 1.0
