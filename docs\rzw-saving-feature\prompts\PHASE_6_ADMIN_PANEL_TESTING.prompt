# RZW Vadeli He<PERSON> - Faz 6: Admin Panel & Testing

## 🎯 AI Agent Görevi
RZW vadeli hesap sistemi için admin panel oluştur ve kapsamlı testler yap. Sistem yönetimi, rapor<PERSON>a ve monitoring özellikleri ekle.

## 📋 Detaylı Görevler

### 1. Admin Panel - Vadeli Hesap Planları Yönetimi

#### 1.1 RzwSavingsPlans Controller
**Dosya**: `src/Areas/Admin/Pages/RzwSavingsPlans/Index.cshtml.cs`

```csharp
public class IndexModel : PageModel
{
    private readonly RzwSavingsPlanService _planService;

    public List<RzwSavingsPlan> Plans { get; set; } = new();

    public async Task OnGetAsync()
    {
        Plans = await _planService.GetAllPlansAsync();
    }

    public async Task<IActionResult> OnPostToggleStatusAsync(int id)
    {
        await _planService.ToggleStatusAsync(id);
        return RedirectToPage();
    }
}
```

#### 1.2 Plan Yönetimi Sayfaları
**Dosyalar**: 
- `src/Areas/Admin/Pages/RzwSavingsPlans/Index.cshtml`
- `src/Areas/Admin/Pages/RzwSavingsPlans/Create.cshtml`
- `src/Areas/Admin/Pages/RzwSavingsPlans/Edit.cshtml`

**Özellikler**:
- Plan listesi (aktif/pasif durumu)
- Yeni plan oluşturma
- Mevcut plan düzenleme
- Plan aktif/pasif yapma
- Plan kullanım istatistikleri

### 2. Admin Panel - Kullanıcı Vadeli Hesapları

#### 2.1 RzwSavingsAccounts Controller
**Dosya**: `src/Areas/Admin/Pages/RzwSavingsAccounts/Index.cshtml.cs`

```csharp
public class IndexModel : PageModel
{
    private readonly RzwSavingsService _savingsService;

    public List<RzwSavingsAccountViewModel> Accounts { get; set; } = new();
    public PaginationInfo Pagination { get; set; } = new();

    [BindProperty(SupportsGet = true)]
    public string? SearchEmail { get; set; }

    [BindProperty(SupportsGet = true)]
    public string? Status { get; set; }

    public async Task OnGetAsync(int pageNumber = 1, int pageSize = 50)
    {
        var result = await _savingsService.GetAccountsForAdminAsync(
            searchEmail: SearchEmail,
            status: Status,
            pageNumber: pageNumber,
            pageSize: pageSize);

        Accounts = result.Accounts;
        Pagination = result.Pagination;
    }
}
```

#### 2.2 Hesap Yönetimi Özellikleri
- Tüm vadeli hesapları listeleme
- Kullanıcı email'i ile arama
- Durum filtreleme (Active, Matured, Withdrawn)
- Hesap detayları görüntüleme
- Manuel vade dolma işlemi
- Acil durum hesap kapatma

### 3. Admin Panel - Raporlama

#### 3.1 RzwSavingsReports Controller
**Dosya**: `src/Areas/Admin/Pages/RzwSavingsReports/Index.cshtml.cs`

```csharp
public class IndexModel : PageModel
{
    private readonly RzwSavingsReportService _reportService;

    public RzwSavingsReportViewModel Report { get; set; } = new();

    public async Task OnGetAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        var start = startDate ?? DateTime.UtcNow.AddDays(-30);
        var end = endDate ?? DateTime.UtcNow;

        Report = await _reportService.GenerateReportAsync(start, end);
    }
}
```

#### 3.2 Rapor Türleri
- **Genel İstatistikler**: Toplam kilitli RZW, aktif hesap sayısı, toplam faiz
- **Plan Performansı**: Plan bazında kullanım oranları
- **Kullanıcı İstatistikleri**: En aktif kullanıcılar, ortalama hesap büyüklüğü
- **Faiz Ödemeleri**: Günlük/aylık faiz ödeme raporları
- **Erken Çekim Analizi**: Erken çekim oranları ve ceza gelirleri

### 4. RzwSavingsReportService Oluştur
**Dosya**: `src/Areas/Admin/Services/RzwSavingsReportService.cs`

```csharp
public class RzwSavingsReportService
{
    private readonly AppDbContext _context;

    public async Task<RzwSavingsReportViewModel> GenerateReportAsync(DateTime startDate, DateTime endDate)
    {
        var report = new RzwSavingsReportViewModel
        {
            StartDate = startDate,
            EndDate = endDate,
            
            // Genel istatistikler
            TotalActiveAccounts = await GetActiveAccountsCountAsync(),
            TotalLockedRzw = await GetTotalLockedRzwAsync(),
            TotalInterestPaid = await GetTotalInterestPaidAsync(startDate, endDate),
            
            // Plan istatistikleri
            PlanStatistics = await GetPlanStatisticsAsync(),
            
            // Günlük faiz ödemeleri
            DailyInterestPayments = await GetDailyInterestPaymentsAsync(startDate, endDate),
            
            // Erken çekim istatistikleri
            EarlyWithdrawalStats = await GetEarlyWithdrawalStatsAsync(startDate, endDate)
        };

        return report;
    }
}
```

### 5. System Monitoring

#### 5.1 RzwSavingsMonitoringService
**Dosya**: `src/Areas/Admin/Services/RzwSavingsMonitoringService.cs`

```csharp
public class RzwSavingsMonitoringService
{
    public async Task<SystemHealthReport> GetSystemHealthAsync()
    {
        return new SystemHealthReport
        {
            BackgroundServiceStatus = await CheckBackgroundServiceStatusAsync(),
            PendingInterestPayments = await GetPendingInterestPaymentsCountAsync(),
            OverdueMaturityAccounts = await GetOverdueMaturityAccountsCountAsync(),
            SystemErrors = await GetRecentSystemErrorsAsync(),
            PerformanceMetrics = await GetPerformanceMetricsAsync()
        };
    }
}
```

### 6. Comprehensive Testing

#### 6.1 Unit Tests
**Dosya**: `src_unittests/RazeWinComTr.RzwSavingsTests/`

**InterestCalculationTests.cs**:
```csharp
[TestClass]
public class InterestCalculationTests
{
    [TestMethod]
    public void CalculateDailyInterest_WithCompoundInterest_ReturnsCorrectAmount()
    {
        // Arrange
        var account = new RzwSavingsAccount
        {
            RzwAmount = 1000m,
            TotalEarnedRzw = 50m,
            InterestRate = 0.0003m
        };

        // Act
        var dailyInterest = CalculateDailyInterest(account);

        // Assert
        var expectedInterest = (1000m + 50m) * 0.0003m;
        Assert.AreEqual(expectedInterest, dailyInterest);
    }
}
```

**BalanceManagementTests.cs**:
```csharp
[TestClass]
public class BalanceManagementTests
{
    [TestMethod]
    public async Task LockRzwForSavings_WithSufficientBalance_SuccessfullyLocks()
    {
        // Test RZW kilitleme işlemi
    }

    [TestMethod]
    public async Task UnlockRzwFromSavings_WithValidAccount_SuccessfullyUnlocks()
    {
        // Test RZW serbest bırakma işlemi
    }
}
```

#### 6.2 Integration Tests
**SavingsAccountFlowTests.cs**:
```csharp
[TestClass]
public class SavingsAccountFlowTests
{
    [TestMethod]
    public async Task CreateSavingsAccount_FullFlow_CompletesSuccessfully()
    {
        // End-to-end vadeli hesap açma testi
        // 1. Plan seç
        // 2. Bakiye kontrol et
        // 3. Hesap oluştur
        // 4. RZW kilitle
        // 5. Trade kaydı kontrol et
    }

    [TestMethod]
    public async Task EarlyWithdrawal_FullFlow_CompletesWithPenalty()
    {
        // End-to-end erken çekim testi
    }
}
```

#### 6.3 Performance Tests
**BackgroundServicePerformanceTests.cs**:
```csharp
[TestClass]
public class BackgroundServicePerformanceTests
{
    [TestMethod]
    public async Task ProcessDailyInterest_With1000Accounts_CompletesInReasonableTime()
    {
        // 1000 hesap ile performans testi
        var stopwatch = Stopwatch.StartNew();
        
        await backgroundService.ProcessDailyInterestAsync();
        
        stopwatch.Stop();
        Assert.IsTrue(stopwatch.ElapsedMilliseconds < 30000); // 30 saniyeden az
    }
}
```

### 7. Documentation

#### 7.1 API Documentation
**Dosya**: `docs/rzw-saving-feature/API_DOCUMENTATION.md`
- Service metotları ve parametreleri
- Response formatları
- Error codes ve handling
- Usage examples

#### 7.2 User Guide
**Dosya**: `docs/rzw-saving-feature/USER_GUIDE.md`
- Vadeli hesap nasıl açılır
- Faiz hesaplama kuralları
- Erken çekim koşulları
- SSS (Sık Sorulan Sorular)

#### 7.3 Admin Guide
**Dosya**: `docs/rzw-saving-feature/ADMIN_GUIDE.md`
- Plan yönetimi
- Kullanıcı hesap yönetimi
- Raporlama
- Troubleshooting

## ✅ Başarı Kriterleri
1. Admin panel çalışıyor: Tüm admin sayfaları erişilebilir
2. Plan yönetimi: CRUD işlemleri çalışıyor
3. Hesap yönetimi: Kullanıcı hesapları yönetilebiliyor
4. Raporlama: Detaylı raporlar oluşturuluyor
5. Monitoring: Sistem sağlığı izlenebiliyor
6. Unit tests: %90+ code coverage
7. Integration tests: Ana akışlar test ediliyor
8. Performance tests: Kabul edilebilir performans
9. Documentation: Kapsamlı dokümantasyon

## 🔧 Test Komutları
```bash
# Unit tests çalıştır
dotnet test src_unittests/RazeWinComTr.RzwSavingsTests/

# Coverage raporu oluştur
dotnet test --collect:"XPlat Code Coverage"

# Performance tests
dotnet test --filter Category=Performance

# Integration tests
dotnet test --filter Category=Integration

# Build ve deployment kontrolü
dotnet build --configuration Release
```

## 📝 Önemli Notlar
- **Security**: Admin panel için yetkilendirme kontrolleri
- **Performance**: Büyük veri setleri için pagination
- **Monitoring**: Sistem sağlığı için alerting
- **Backup**: Kritik işlemler öncesi backup
- **Logging**: Detaylı audit trail
- **Testing**: Comprehensive test coverage
- **Documentation**: Güncel ve kapsamlı dokümantasyon

## 🎯 Sonuç Beklentisi
Bu faz sonunda:
- Admin panel tamamlanmış olacak
- Kapsamlı test suite hazır olacak
- Sistem monitoring ve raporlama çalışır olacak
- Production-ready kod kalitesi sağlanmış olacak
- Dokümantasyon tamamlanmış olacak

**Tahmini Süre**: 2-3 gün
**Bağımlılıklar**: Tüm önceki fazlar tamamlanmış olmalı
**Sonuç**: Proje tamamlandı ve production'a hazır!
