/* RZW Savings Create Page Styles */

/* Main Container */
.rzw-savings-create-container {
    width: 100%;
    max-width: none;
    margin: 0;
    padding: 20px;
}

/* Balance Info Section */
.balance-info-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 40px;
    margin-bottom: 30px;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.balance-info-section h5 {
    margin-bottom: 15px;
    font-weight: 600;
    opacity: 0.9;
    font-size: 1.4rem;
}

.balance-info-section h3 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.balance-info-section small {
    opacity: 0.8;
    font-size: 1.1rem;
}

.balance-icon {
    font-size: 5rem;
    opacity: 0.6;
    color: rgba(255,255,255,0.7);
}

/* Form Container */
.form-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    overflow: hidden;
}

/* Plan Selection Section */
.plan-selection-section {
    padding: 40px;
    border-bottom: 1px solid #e9ecef;
}

.section-title {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    font-size: 1.8rem;
    font-weight: 600;
    color: #2c3e50;
}

.section-title i {
    margin-right: 15px;
    color: #667eea;
    font-size: 1.6rem;
}

/* Plan Cards */
.plan-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
    margin-bottom: 25px;
}

.plan-card {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 30px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    background: white;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.plan-card:hover {
    border-color: #667eea;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
    transform: translateY(-3px);
}

.plan-card.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

.plan-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.plan-name {
    font-size: 1.4rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    flex: 1;
}

.plan-apy {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 2rem;
    font-weight: 600;
    margin-left: 15px;
}

.plan-daily-rate {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 5px;
    text-align: right;
}

.plan-description {
    color: #6c757d;
    font-size: 1.1rem;
    margin-bottom: 20px;
    line-height: 1.5;
}

.plan-details {
    margin-top: auto;
    font-size: 1.5rem;
    line-height: 1.8;
}

.plan-details strong {
    color: #2c3e50;
}

.plan-radio {
    position: absolute;
    top: 20px;
    right: 20px;
    transform: scale(1.5);
    accent-color: #667eea;
}

/* Amount Input Section */
.amount-input-section {
    padding: 40px;
    border-bottom: 1px solid #e9ecef;
}

.amount-input-group {
    position: relative;
    margin-bottom: 25px;
}

.amount-input-group .form-label {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 10px;
    display: block;
}

.amount-input {
    width: 100%;
    padding: 20px 25px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 1.4rem;
    font-weight: 500;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.amount-input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.amount-currency {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-weight: 600;
    pointer-events: none;
    font-size: 1.2rem;
}

.amount-limits {
    font-size: 1rem;
    color: #6c757d;
    margin-top: 10px;
}

/* Auto Renew Checkbox */
.auto-renew-container {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 10px;
    margin-top: 25px;
}

.auto-renew-checkbox {
    margin-top: 5px;
    transform: scale(1.4);
    accent-color: #667eea;
}

.auto-renew-label {
    flex: 1;
}

.auto-renew-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 1.1rem;
}

.auto-renew-description {
    font-size: 1rem;
    color: #6c757d;
    line-height: 1.5;
}

/* Interest Preview Section */
.interest-preview-section {
    padding: 40px;
}

.preview-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 35px;
    margin-bottom: 30px;
}

.preview-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #dee2e6;
    font-size: 1.1rem;
}

.preview-item:last-child {
    border-bottom: none;
    font-weight: 700;
    font-size: 1.3rem;
    color: #667eea;
    padding-top: 20px;
    margin-top: 15px;
    border-top: 2px solid #dee2e6;
}

.preview-label {
    color: #495057;
    font-weight: 500;
}

.preview-value {
    font-weight: 600;
    color: #2c3e50;
}

.preview-placeholder {
    text-align: center;
    color: #6c757d;
    padding: 50px 20px;
}

.preview-placeholder i {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.preview-placeholder p {
    font-size: 1.1rem;
    margin: 0;
}

/* Submit Button */
.submit-button-container {
    text-align: center;
}

.submit-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 20px 50px;
    border-radius: 10px;
    font-size: 1.3rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    max-width: 400px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.submit-button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.submit-button:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.submit-warning {
    font-size: 1rem;
    color: #6c757d;
    margin-top: 15px;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .plan-cards-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 20px;
    }
}

@media (max-width: 768px) {
    .rzw-savings-create-container {
        padding: 15px;
    }

    .balance-info-section {
        padding: 30px 20px;
        text-align: center;
    }

    .balance-info-section h5 {
        font-size: 1.2rem;
    }

    .balance-info-section h3 {
        font-size: 2.8rem;
    }

    .balance-info-section small {
        font-size: 1rem;
    }

    .balance-icon {
        font-size: 4rem;
    }

    .plan-cards-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .plan-card {
        padding: 25px;
    }

    .plan-name {
        font-size: 1.2rem;
    }

    .plan-apy {
        font-size: 1.5rem;
        padding: 6px 12px;
    }

    .plan-description {
        font-size: 1.5rem;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .section-title i {
        font-size: 1.3rem;
    }

    .plan-selection-section,
    .amount-input-section,
    .interest-preview-section {
        padding: 30px 20px;
    }

    .amount-input {
        padding: 18px 20px;
        font-size: 1.2rem;
    }

    .amount-currency {
        font-size: 1.1rem;
    }

    .preview-item {
        font-size: 1rem;
    }

    .preview-item:last-child {
        font-size: 1.2rem;
    }

    .submit-button {
        padding: 18px 40px;
        font-size: 1.2rem;
    }
}

@media (max-width: 480px) {
    .balance-info-section h5 {
        font-size: 1.1rem;
    }

    .balance-info-section h3 {
        font-size: 2.2rem;
    }

    .balance-info-section small {
        font-size: 0.95rem;
    }

    .section-title {
        font-size: 1.3rem;
    }

    .plan-name {
        font-size: 1.1rem;
    }

    .plan-description {
        font-size: 1.55rem;
    }

    .plan-details {
        font-size: 1.5rem;
    }

    .amount-input {
        padding: 16px 18px;
        font-size: 1.1rem;
    }

    .amount-input-group .form-label {
        font-size: 1.1rem;
    }

    .auto-renew-title {
        font-size: 1rem;
    }

    .auto-renew-description {
        font-size: 0.9rem;
    }

    .preview-item {
        font-size: 0.95rem;
    }

    .preview-item:last-child {
        font-size: 1.1rem;
    }

    .submit-button {
        padding: 16px 30px;
        font-size: 1.1rem;
    }

    .submit-warning {
        font-size: 0.9rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}
