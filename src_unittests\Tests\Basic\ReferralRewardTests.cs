using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;

namespace RazeWinComTr.Tests.Basic
{
    public class ReferralRewardTests
    {
        [Fact]
        public void SimpleTest()
        {
            // A simple test that doesn't depend on the main project
            Assert.True(true);
        }

        [Fact]
        public void CalculateReward_WithValidPercentage_ReturnsCorrectAmount()
        {
            // Arrange
            decimal amount = 1000m;
            decimal percentage = 20m;

            // Act
            decimal reward = amount * (percentage / 100m);

            // Assert
            Assert.Equal(expected: 200m, actual: reward);
        }

        [Fact]
        public void CalculateReward_WithZeroAmount_ReturnsZero()
        {
            // Arrange
            decimal amount = 0m;
            decimal percentage = 20m;

            // Act
            decimal reward = amount * (percentage / 100m);

            // Assert
            Assert.Equal(expected: 0m, actual: reward);
        }

        [Fact]
        public void CalculateReward_WithZeroPercentage_ReturnsZero()
        {
            // Arrange
            decimal amount = 1000m;
            decimal percentage = 0m;

            // Act
            decimal reward = amount * (percentage / 100m);

            // Assert
            Assert.Equal(expected: 0m, actual: reward);
        }

        [Fact]
        public void CalculateReward_WithLargeAmount_HandlesDecimalPrecisionCorrectly()
        {
            // Arrange
            decimal amount = 1000000000m; // 1 billion
            decimal percentage = 20m;

            // Act
            decimal reward = amount * (percentage / 100m);

            // Assert
            Assert.Equal(expected: 200000000m, actual: reward);
        }

        [Fact]
        public void CalculateReward_WithSmallAmount_HandlesDecimalPrecisionCorrectly()
        {
            // Arrange
            decimal amount = 0.00000001m;
            decimal percentage = 20m;

            // Act
            decimal reward = amount * (percentage / 100m);

            // Assert
            Assert.Equal(expected: 0.000000002m, actual: reward);
        }
    }
}
