# Adım 5.2.1: PageModel ve ViewModel Oluşturma (2-3 saat)

## 📋 Adım Özeti
RZW Savings ana sayfası için PageModel, ViewModel'ler ve DTO'ları oluşturma. Service entegrasyonu ve veri akışının hazırlanması.

## 🎯 Hedefler
- ✅ RzwSavingsIndexModel PageModel oluşturma
- ✅ Dashboard ViewModel'leri oluşturma
- ✅ Service entegrasyonu
- ✅ Error handling ve validation

## 📊 Yapılacak İşler

### ✅ YAPILACAKLAR

#### 5.2.1.1 ViewModel'ler Oluşturma

**Dosya**: `src/Areas/MyAccount/ViewModels/RzwSavings/RzwSavingsIndexViewModel.cs`
```csharp
namespace RazeWinComTr.Areas.MyAccount.ViewModels.RzwSavings
{
    public class RzwSavingsIndexViewModel
    {
        public RzwSavingsDashboard Dashboard { get; set; } = new();
        public List<RzwSavingsAccountSummary> ActiveAccounts { get; set; } = new();
        public List<RzwSavingsPlanSummary> AvailablePlans { get; set; } = new();
        public RzwBalanceInfo UserRzwBalance { get; set; } = new();
        public bool HasActiveAccounts => ActiveAccounts.Any();
        public bool HasAvailableBalance => UserRzwBalance.AvailableBalance > 0;
        public string PageTitle { get; set; } = string.Empty;
        public string WelcomeMessage { get; set; } = string.Empty;
    }

    public class RzwSavingsDashboard
    {
        public decimal TotalRzwInSavings { get; set; }
        public int ActiveAccountsCount { get; set; }
        public decimal TotalEarnedInterest { get; set; }
        public decimal EstimatedMonthlyEarnings { get; set; }
        public decimal EstimatedYearlyEarnings { get; set; }
        public DateTime? LastInterestPayment { get; set; }
        public int DaysInSavings { get; set; }
        public decimal AverageInterestRate { get; set; }

        // Hesaplanmış alanlar
        public bool HasEarnings => TotalEarnedInterest > 0;
        public bool HasActiveInvestments => TotalRzwInSavings > 0;
        public string FormattedTotalInSavings => TotalRzwInSavings.ToString("N8");
        public string FormattedTotalEarned => TotalEarnedInterest.ToString("N8");
        public string FormattedMonthlyEstimate => EstimatedMonthlyEarnings.ToString("N8");
    }

    public class RzwSavingsAccountSummary
    {
        public int Id { get; set; }
        public string PlanName { get; set; } = string.Empty;
        public string TermType { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime MaturityDate { get; set; }
        public string Status { get; set; } = string.Empty;
        public decimal InterestRate { get; set; }
        public decimal TotalEarned { get; set; }
        public DateTime? LastInterestDate { get; set; }
        public bool AutoRenew { get; set; }

        // Hesaplanmış alanlar
        public int DaysRemaining => Math.Max(0, (MaturityDate - DateTime.UtcNow).Days);
        public int TotalDays => (MaturityDate - StartDate).Days;
        public int DaysElapsed => Math.Max(0, (DateTime.UtcNow - StartDate).Days);
        public decimal ProgressPercentage => TotalDays > 0 ? Math.Min(100, (decimal)DaysElapsed / TotalDays * 100) : 0;
        public bool IsNearMaturity => DaysRemaining <= 7 && DaysRemaining > 0;
        public bool IsMatured => DaysRemaining <= 0;
        public bool IsActive => Status == RzwSavingsStatus.Active;
        
        // Formatlanmış alanlar
        public string FormattedAmount => Amount.ToString("N8");
        public string FormattedTotalEarned => TotalEarned.ToString("N8");
        public string FormattedInterestRate => (InterestRate * 100).ToString("N2") + "%";
        public string FormattedStartDate => StartDate.ToString("dd.MM.yyyy");
        public string FormattedMaturityDate => MaturityDate.ToString("dd.MM.yyyy");
        
        // Status bilgileri
        public string StatusBadgeClass => Status switch
        {
            RzwSavingsStatus.Active when IsNearMaturity => "badge-warning",
            RzwSavingsStatus.Active => "badge-success",
            RzwSavingsStatus.Matured => "badge-info",
            RzwSavingsStatus.Withdrawn => "badge-secondary",
            RzwSavingsStatus.Cancelled => "badge-danger",
            _ => "badge-secondary"
        };

        public string StatusDisplayText => Status switch
        {
            RzwSavingsStatus.Active when IsNearMaturity => "Yakın Vade",
            RzwSavingsStatus.Active => "Aktif",
            RzwSavingsStatus.Matured => "Vade Doldu",
            RzwSavingsStatus.Withdrawn => "Çekildi",
            RzwSavingsStatus.Cancelled => "İptal",
            _ => Status
        };
    }

    public class RzwSavingsPlanSummary
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string TermType { get; set; } = string.Empty;
        public int TermDuration { get; set; }
        public decimal InterestRate { get; set; }
        public decimal MinRzwAmount { get; set; }
        public decimal? MaxRzwAmount { get; set; }
        public string? Description { get; set; }
        public bool IsActive { get; set; }

        // Hesaplanmış alanlar
        public string FormattedInterestRate => (InterestRate * 100).ToString("N2") + "%";
        public string FormattedMinAmount => MinRzwAmount.ToString("N0");
        public string FormattedMaxAmount => MaxRzwAmount?.ToString("N0") ?? "Sınırsız";
        public string TermDisplayText => TermType switch
        {
            RzwSavingsTermType.Daily => $"{TermDuration} Gün",
            RzwSavingsTermType.Monthly => $"{TermDuration / 30} Ay",
            RzwSavingsTermType.Yearly => $"{TermDuration / 365} Yıl",
            _ => $"{TermDuration} Gün"
        };

        // Yıllık getiri hesaplama
        public decimal AnnualizedReturn => TermType switch
        {
            RzwSavingsTermType.Daily => InterestRate * 365,
            RzwSavingsTermType.Monthly => InterestRate * 12,
            RzwSavingsTermType.Yearly => InterestRate,
            _ => InterestRate
        };

        public string FormattedAnnualizedReturn => (AnnualizedReturn * 100).ToString("N1") + "%";
    }
}
```

#### 5.2.1.2 PageModel Oluşturma

**Dosya**: `src/Areas/MyAccount/Pages/RzwSavings/Index.cshtml.cs`
```csharp
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.MyAccount.ViewModels.RzwSavings;
using RazeWinComTr.Extensions;

namespace RazeWinComTr.Areas.MyAccount.Pages.RzwSavings
{
    [Authorize(Policy = StaticConfig.UserPolicyName)]
    public class IndexModel : PageModel
    {
        private readonly RzwSavingsService _savingsService;
        private readonly RzwSavingsInterestService _interestService;
        private readonly RzwBalanceManagementService _balanceService;
        private readonly RzwSavingsPlanService _planService;
        private readonly IStringLocalizer<SharedResource> _localizer;
        private readonly ILogger<IndexModel> _logger;

        public IndexModel(
            RzwSavingsService savingsService,
            RzwSavingsInterestService interestService,
            RzwBalanceManagementService balanceService,
            RzwSavingsPlanService planService,
            IStringLocalizer<SharedResource> localizer,
            ILogger<IndexModel> logger)
        {
            _savingsService = savingsService;
            _interestService = interestService;
            _balanceService = balanceService;
            _planService = planService;
            _localizer = localizer;
            _logger = logger;
        }

        public RzwSavingsIndexViewModel ViewModel { get; set; } = new();

        public async Task<IActionResult> OnGetAsync()
        {
            try
            {
                var userId = HttpContext.User.GetClaimUserId();
                if (!userId.HasValue)
                {
                    return RedirectToPage("/Account/Login");
                }

                await LoadViewModelAsync(userId.Value);
                return Page();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading RZW Savings index page");
                TempData["ErrorMessage"] = _localizer["An error occurred while loading your savings information"];
                return Page();
            }
        }

        private async Task LoadViewModelAsync(int userId)
        {
            // Sayfa başlığı ve mesajları
            ViewModel.PageTitle = _localizer["RZW Savings Accounts"];
            ViewModel.WelcomeMessage = _localizer["Manage your RZW savings and earn guaranteed interest"];

            // Paralel veri yükleme
            var tasks = new[]
            {
                LoadDashboardDataAsync(userId),
                LoadActiveAccountsAsync(userId),
                LoadAvailablePlansAsync(),
                LoadUserBalanceAsync(userId)
            };

            await Task.WhenAll(tasks);
        }

        private async Task LoadDashboardDataAsync(int userId)
        {
            try
            {
                var activeAccounts = await _savingsService.GetUserActiveSavingsAsync(userId);
                var totalEarnedInterest = await _interestService.GetUserTotalEarnedInterestAsync(userId);

                ViewModel.Dashboard = new RzwSavingsDashboard
                {
                    TotalRzwInSavings = activeAccounts.Sum(a => a.RzwAmount),
                    ActiveAccountsCount = activeAccounts.Count,
                    TotalEarnedInterest = totalEarnedInterest,
                    EstimatedMonthlyEarnings = CalculateEstimatedMonthlyEarnings(activeAccounts),
                    EstimatedYearlyEarnings = CalculateEstimatedYearlyEarnings(activeAccounts),
                    LastInterestPayment = activeAccounts.SelectMany(a => a.InterestPayments)
                                                      .OrderByDescending(p => p.PaymentDate)
                                                      .FirstOrDefault()?.PaymentDate,
                    DaysInSavings = CalculateAverageDaysInSavings(activeAccounts),
                    AverageInterestRate = CalculateAverageInterestRate(activeAccounts)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading dashboard data for user {UserId}", userId);
                ViewModel.Dashboard = new RzwSavingsDashboard();
            }
        }

        private async Task LoadActiveAccountsAsync(int userId)
        {
            try
            {
                var activeAccounts = await _savingsService.GetUserActiveSavingsAsync(userId);
                
                ViewModel.ActiveAccounts = activeAccounts.Select(account => new RzwSavingsAccountSummary
                {
                    Id = account.Id,
                    PlanName = GetPlanDisplayName(account.TermType),
                    TermType = account.TermType,
                    Amount = account.RzwAmount,
                    StartDate = account.StartDate,
                    MaturityDate = account.MaturityDate,
                    Status = account.Status,
                    InterestRate = account.InterestRate,
                    TotalEarned = account.TotalEarnedRzw,
                    LastInterestDate = account.LastInterestDate,
                    AutoRenew = account.AutoRenew
                }).OrderByDescending(a => a.StartDate).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading active accounts for user {UserId}", userId);
                ViewModel.ActiveAccounts = new List<RzwSavingsAccountSummary>();
            }
        }

        private async Task LoadAvailablePlansAsync()
        {
            try
            {
                var plans = await _planService.GetActivePlansAsync();
                
                ViewModel.AvailablePlans = plans.Select(plan => new RzwSavingsPlanSummary
                {
                    Id = plan.Id,
                    Name = plan.Name,
                    TermType = plan.TermType,
                    TermDuration = plan.TermDuration,
                    InterestRate = plan.InterestRate,
                    MinRzwAmount = plan.MinRzwAmount,
                    MaxRzwAmount = plan.MaxRzwAmount,
                    Description = plan.Description,
                    IsActive = plan.IsActive
                }).OrderBy(p => p.TermDuration).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading available plans");
                ViewModel.AvailablePlans = new List<RzwSavingsPlanSummary>();
            }
        }

        private async Task LoadUserBalanceAsync(int userId)
        {
            try
            {
                ViewModel.UserRzwBalance = await _balanceService.GetRzwBalanceInfoAsync(userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading user balance for user {UserId}", userId);
                ViewModel.UserRzwBalance = new RzwBalanceInfo { UserId = userId };
            }
        }

        // Helper Methods
        private decimal CalculateEstimatedMonthlyEarnings(List<RzwSavingsAccount> accounts)
        {
            return accounts.Where(a => a.Status == RzwSavingsStatus.Active)
                          .Sum(a => a.TermType switch
                          {
                              RzwSavingsTermType.Daily => a.RzwAmount * a.InterestRate * 30,
                              RzwSavingsTermType.Monthly => a.RzwAmount * a.InterestRate,
                              RzwSavingsTermType.Yearly => a.RzwAmount * a.InterestRate / 12,
                              _ => 0
                          });
        }

        private decimal CalculateEstimatedYearlyEarnings(List<RzwSavingsAccount> accounts)
        {
            return accounts.Where(a => a.Status == RzwSavingsStatus.Active)
                          .Sum(a => a.TermType switch
                          {
                              RzwSavingsTermType.Daily => a.RzwAmount * a.InterestRate * 365,
                              RzwSavingsTermType.Monthly => a.RzwAmount * a.InterestRate * 12,
                              RzwSavingsTermType.Yearly => a.RzwAmount * a.InterestRate,
                              _ => 0
                          });
        }

        private int CalculateAverageDaysInSavings(List<RzwSavingsAccount> accounts)
        {
            if (!accounts.Any()) return 0;
            
            var totalDays = accounts.Sum(a => (DateTime.UtcNow - a.StartDate).Days);
            return totalDays / accounts.Count;
        }

        private decimal CalculateAverageInterestRate(List<RzwSavingsAccount> accounts)
        {
            if (!accounts.Any()) return 0;
            
            var weightedSum = accounts.Sum(a => a.InterestRate * a.RzwAmount);
            var totalAmount = accounts.Sum(a => a.RzwAmount);
            
            return totalAmount > 0 ? weightedSum / totalAmount : 0;
        }

        private string GetPlanDisplayName(string termType)
        {
            return termType switch
            {
                RzwSavingsTermType.Daily => _localizer["RZW Daily Savings"],
                RzwSavingsTermType.Monthly => _localizer["RZW Monthly Savings"],
                RzwSavingsTermType.Yearly => _localizer["RZW Yearly Savings"],
                _ => termType
            };
        }

        // AJAX Endpoints
        public async Task<IActionResult> OnGetDashboardUpdateAsync()
        {
            try
            {
                var userId = HttpContext.User.GetClaimUserId();
                if (!userId.HasValue)
                {
                    return new JsonResult(new { success = false, message = "Unauthorized" });
                }

                await LoadDashboardDataAsync(userId.Value);
                
                return new JsonResult(new 
                { 
                    success = true, 
                    dashboard = new
                    {
                        totalInSavings = ViewModel.Dashboard.FormattedTotalInSavings,
                        activeAccountsCount = ViewModel.Dashboard.ActiveAccountsCount,
                        totalEarned = ViewModel.Dashboard.FormattedTotalEarned,
                        monthlyEstimate = ViewModel.Dashboard.FormattedMonthlyEstimate,
                        hasEarnings = ViewModel.Dashboard.HasEarnings,
                        hasActiveInvestments = ViewModel.Dashboard.HasActiveInvestments
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dashboard update");
                return new JsonResult(new { success = false, message = "Error updating dashboard" });
            }
        }

        public async Task<IActionResult> OnGetAccountsUpdateAsync()
        {
            try
            {
                var userId = HttpContext.User.GetClaimUserId();
                if (!userId.HasValue)
                {
                    return new JsonResult(new { success = false, message = "Unauthorized" });
                }

                await LoadActiveAccountsAsync(userId.Value);
                
                return new JsonResult(new 
                { 
                    success = true, 
                    accounts = ViewModel.ActiveAccounts.Select(a => new
                    {
                        id = a.Id,
                        planName = a.PlanName,
                        formattedAmount = a.FormattedAmount,
                        formattedTotalEarned = a.FormattedTotalEarned,
                        daysRemaining = a.DaysRemaining,
                        progressPercentage = a.ProgressPercentage,
                        statusBadgeClass = a.StatusBadgeClass,
                        statusDisplayText = a.StatusDisplayText,
                        isNearMaturity = a.IsNearMaturity,
                        isMatured = a.IsMatured
                    })
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting accounts update");
                return new JsonResult(new { success = false, message = "Error updating accounts" });
            }
        }
    }
}
```

## 📋 Kontrol Listesi

### ✅ YAPILACAKLAR
- [ ] RzwSavingsIndexViewModel.cs oluşturma
- [ ] RzwSavingsIndexModel PageModel oluşturma
- [ ] Helper method'ları ekleme
- [ ] AJAX endpoint'leri ekleme
- [ ] Error handling ekleme
- [ ] Unit testler yazma

### 🔄 YAPILMAKTA OLANLAR
- (Henüz başlanmadı)

### ✅ YAPILMIŞLAR
- (Henüz tamamlanmadı)

## 🧪 Test Kriterleri

### ViewModel Tests
- [ ] Dashboard hesaplamaları doğru
- [ ] Account summary mapping doğru
- [ ] Plan summary mapping doğru
- [ ] Formatlanmış alanlar doğru

### PageModel Tests
- [ ] Veri yükleme işlemleri çalışıyor
- [ ] AJAX endpoint'leri çalışıyor
- [ ] Error handling doğru
- [ ] Authorization çalışıyor

## 📝 Notlar

### Önemli Özellikler
- Paralel veri yükleme (performance)
- Comprehensive error handling
- Real-time AJAX updates
- Calculated fields for UI

### Hesaplama Algoritmaları
- **Aylık Tahmini**: Günlük × 30, Aylık × 1, Yıllık ÷ 12
- **Yıllık Tahmini**: Günlük × 365, Aylık × 12, Yıllık × 1
- **Ortalama Faiz**: Weighted average by amount

### Sonraki Adım
Bu adım tamamlandıktan sonra **Adım 5.2.2: Ana Sayfa Layout ve Header** başlayacak.

---
**Tahmini Süre**: 2-3 saat
**Öncelik**: Yüksek
**Bağımlılıklar**: Faz 1, 2, 3 tamamlanmış olmalı
