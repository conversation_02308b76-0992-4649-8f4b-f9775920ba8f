using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.Trade;
using RazeWinComTr.Areas.Admin.ViewModels.Wallet;
using RazeWinComTr.Models;

namespace RazeWinComTr.Areas.MyAccount.Pages;

public class WalletModel : PageModel
{
    private readonly AppDbContext _context;
    private readonly IWalletService _walletService;
    private readonly ITradeService _tradeService;
    private readonly RzwBalanceManagementService _rzwBalanceService;
    private readonly RzwSavingsService _rzwSavingsService;

    public WalletModel(
        AppDbContext context,
        IWalletService walletService,
        ITradeService tradeService,
        RzwBalanceManagementService rzwBalanceService,
        RzwSavingsService rzwSavingsService)
    {
        _context = context;
        _walletService = walletService;
        _tradeService = tradeService;
        _rzwBalanceService = rzwBalanceService;
        _rzwSavingsService = rzwSavingsService;
        Wallets = new List<WalletViewModel>();
        Trades = new List<TradeViewModel>();
    }

    public List<WalletViewModel> Wallets { get; set; }
    public List<TradeViewModel> Trades { get; set; }

    // RZW Balance Information
    public RzwBalanceInfo? RzwBalance { get; set; }
    public List<RzwSavingsAccount> ActiveSavingsAccounts { get; set; } = new();
    public bool HasRzwBalance => RzwBalance?.HasRzwBalance ?? false;
    public bool HasActiveSavings => ActiveSavingsAccounts.Any();

    public string UserFullName { get; set; } = string.Empty;
    public string UserEmail { get; set; } = string.Empty;
    public string UserPhone { get; set; } = string.Empty;
    public DateTime UserCreatedDate { get; set; }

    public async Task<IActionResult> OnGetAsync()
    {
        var userId = User.GetClaimUserId();
        if (!userId.HasValue)
        {
            return Unauthorized();
        }

        // Get user information
        var user = await _context.Users.FirstOrDefaultAsync(u => u.UserId == userId);
        if (user == null)
        {
            return NotFound();
        }

        UserFullName = $"{user.Name} {user.Surname}";
        UserEmail = user.Email;
        UserPhone = user.PhoneNumber;
        UserCreatedDate = user.CrDate;

        // Get wallet balances
        var wallets = await _walletService.GetByUserIdAsync(userId.Value);
        Wallets = wallets
            .Select(w => new WalletViewModel
            {
                Id = w.Id,
                UserId = w.UserId,
                UserEmail = user.Email,
                CoinId = w.CoinId,
                CoinName = w.Coin.Name,
                CoinCode = w.Coin.PairCode,
                Balance = w.Balance,
                LockedBalance = w.LockedBalance,
                CreatedDate = w.CreatedDate,
                ModifiedDate = w.ModifiedDate
            })
            .OrderByDescending(w => w.Balance)
            .ToList();

        // Get trade history
        Trades = await _tradeService.GetByUserIdAsync(userId.Value);

        // Get RZW balance information
        try
        {
            RzwBalance = await _rzwBalanceService.GetRzwBalanceInfoAsync(userId.Value);

            // Get active savings accounts
            ActiveSavingsAccounts = await _rzwSavingsService.GetUserActiveSavingsAsync(userId.Value);
        }
        catch (Exception)
        {
            // If RZW balance service fails, continue without RZW data
            RzwBalance = null;
            ActiveSavingsAccounts = new List<RzwSavingsAccount>();
        }

        return Page();
    }
}
