﻿@page
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.DbModel
@using RazeWinComTr.Areas.Admin.Helpers
@using RazeWinComTr.Areas.Admin.Services
@using RazeWinComTr.Areas.Admin.Enums
@inject SettingService _settingService
@inject IStringLocalizer<SharedResource> Localizer
@model RazeWinComTr.Areas.MyAccount.Pages.DepositModel
@{
}
<div class="pt-sm-0 pb-sm-0">
    <!-- Fiat Currency Deposit Methods -->
    <div class="card payment-card">
        <div class="card-header font-weight-bolder">@Localizer["Fiat Currency Deposit Methods"]</div>
        <div class="card-body">
            <div class="d-flex flex-wrap">
                <div class="col-6 col-sm-3 px-1">
                    <div class="card bg-light mb-2">
                        <a @(await _settingService.GetBoolSettingAsync("papara_durum") ? @Html.Raw("href=\"/MyAccount/DepositPapara\"") : @Html.Raw("href=# onclick=\"return showDepositMethodAlert()\"")) class="p-1 pb-2 text-dark text-center">
                            <div class="card-img">
                                <img width="100px" style="padding-top:15px;padding-bottom:15px" src="/site/images/deposit/papara.png" alt="">
                            </div>
                            <strong class="d-block pt-1">Papara</strong>
                            <p class="text-small mb-0">@Localizer["Instant Account Credit"]</p>
                        </a>
                    </div>
                </div>

                <div class="col-6 col-sm-3 px-1">
                    <div class="card bg-light mb-2">
                        <a href="/MyAccount/DepositBankTransfer" class="p-1 pb-2 text-dark text-center">
                            <div class="card-img">
                                <img width="50px" style="padding-top:15px;padding-bottom:15px" src="/site/images/deposit/havale.png" alt="">
                            </div>
                            <strong class="d-block pt-1">@Localizer["Bank Transfer"]</strong>
                            <p class="text-small mb-0">@Localizer["Instant Account Credit"]</p>
                        </a>
                    </div>
                </div>

                <div class="col-6 col-sm-3 px-1">
                    <div class="card bg-light mb-2">
                        <a @(await _settingService.GetBoolSettingAsync("kredi_karti_durum") ? "href=\"/MyAccount/KrediKarti\"" : @Html.Raw("href=# onclick=\"return showDepositMethodAlert()\"")) class="p-1 pb-2 text-dark text-center">
                            <div class="card-img">
                                <img src="/site/images/deposit/creditCard.png" alt="">
                            </div>
                            <strong class="d-block pt-1">@Localizer["Credit Card"]</strong>
                            <p class="text-small mb-0">@Localizer["Instant Account Credit"]</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cryptocurrency Deposit Methods -->
    <div class="card payment-card mt-4">
        <div class="card-header font-weight-bolder">@Localizer["Cryptocurrency Deposit Methods"]</div>
        <div class="card-body">
            <div class="d-flex flex-wrap">
                <div class="col-6 col-sm-3 px-1">
                    <div class="card bg-light mb-2">
                        <a href="/MyAccount/DepositCrypto?coin=BTC" class="p-1 pb-2 text-dark text-center">
                            <div class="card-img">
                                <img width="63px" style="padding-top:15px;padding-bottom:15px" src="/site/images/deposit/bitcoin.png" alt="Bitcoin">
                            </div>
                            <strong class="d-block pt-1">Bitcoin (BTC)</strong>
                            <p class="text-small mb-0">@Localizer["Cryptocurrency Deposit"]</p>
                        </a>
                    </div>
                </div>

                <div class="col-6 col-sm-3 px-1">
                    <div class="card bg-light mb-2">
                        <a href="/MyAccount/DepositCrypto?coin=USDT-TRC20" class="p-1 pb-2 text-dark text-center">
                            <div class="card-img">
                                <img width="63px" style="padding-top:15px;padding-bottom:15px" src="/site/images/deposit/tether.png" alt="USDT">
                            </div>
                            <strong class="d-block pt-1">USDT (TRC20)</strong>
                            <p class="text-small mb-0">@Localizer["Cryptocurrency Deposit"]</p>
                        </a>
                    </div>
                </div>

                <div class="col-6 col-sm-3 px-1">
                    <div class="card bg-light mb-2">
                        <a href="/MyAccount/DepositCrypto?coin=BNB-BEP20" class="p-1 pb-2 text-dark text-center">
                            <div class="card-img">
                                <img width="63px" style="padding-top:15px;padding-bottom:15px" src="/site/images/deposit/bnb.png" alt="BNB">
                            </div>
                            <strong class="d-block pt-1">BNB (BEP20)</strong>
                            <p class="text-small mb-0">@Localizer["Cryptocurrency Deposit"]</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header">
            <h3 class="card-title">@Localizer["Deposit History"]</h3>
        </div>
        <!-- /.card-header -->
        <div class="card-body">
            <style>
                /* Özel tablo stilleri */
                #depositHistory {
                    color: #333 !important;
                    background-color: #fff !important;
                }

                    #depositHistory thead th {
                        color: #fff !important;
                        background-color: #343a40 !important;
                        border-color: #454d55 !important;
                    }

                    #depositHistory tbody td {
                        color: #333 !important;
                        background-color: #fff !important;
                    }

                    #depositHistory tfoot th {
                        color: #fff !important;
                        background-color: #343a40 !important;
                        border-color: #454d55 !important;
                    }

                .dataTables_wrapper .dataTables_length,
                .dataTables_wrapper .dataTables_filter,
                .dataTables_wrapper .dataTables_info,
                .dataTables_wrapper .dataTables_processing,
                .dataTables_wrapper .dataTables_paginate {
                    color: #333 !important;
                }

                    .dataTables_wrapper .dataTables_paginate .paginate_button {
                        color: #333 !important;
                    }

                        .dataTables_wrapper .dataTables_paginate .paginate_button.current,
                        .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
                            color: #333 !important;
                            background-color: #f0f0f0 !important;
                        }
            </style>
            <table id="depositHistory" class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th>@Localizer["Date"]</th>
                        <th>@Localizer["Payment Type"]</th>
                        <th>@Localizer["Amount"]</th>
                        <th>@Localizer["Status"]</th>
                        <th>@Localizer["Process Status"]</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var deposit in Model.Deposits)
                    {
                        <tr>
                            <td>@deposit.CreatedDate.ToLocalTime().ToString("dd.MM.yyyy HH:mm")</td>
                            <td>@deposit.DepositType</td>
                            <td>@NumberFormatHelper.FormatDecimal(deposit.Amount) @Localizer["Currency_Symbol"]</td>
                            <td>
                                @if (deposit.Status == DepositStatus.Pending)
                                {
                                    <span class="badge badge-warning">@Localizer["Pending"]</span>
                                }
                                else if (deposit.Status == DepositStatus.Approved)
                                {
                                    <span class="badge badge-success">@Localizer["Approved"]</span>
                                }
                                else
                                {
                                    <span class="badge badge-danger">@Localizer["Rejected"]</span>
                                }
                            </td>
                            <td>
                                @if (!string.IsNullOrEmpty(deposit.ProcessStatus))
                                {
                                    string localizedProcessStatus = deposit.ProcessStatus;
                                    // Translate common process status values
                                    switch (deposit.ProcessStatus)
                                    {
                                        case "Submitted":
                                            localizedProcessStatus = Localizer["Submitted"];
                                            break;
                                        case "Completed":
                                            localizedProcessStatus = Localizer["Completed"];
                                            break;
                                        case "Rejected":
                                            localizedProcessStatus = Localizer["Rejected"];
                                            break;
                                        case "Pending":
                                            localizedProcessStatus = Localizer["Pending"];
                                            break;
                                    }
                                    @if (deposit.Status == DepositStatus.Pending)
                                    {
                                        <span class="badge badge-warning">@localizedProcessStatus</span>
                                    }
                                    else if (deposit.Status == DepositStatus.Approved)
                                    {
                                        <span class="badge badge-success">@localizedProcessStatus</span>
                                    }
                                    else if (deposit.Status == DepositStatus.Rejected)
                                    {
                                        <span class="badge badge-danger">@localizedProcessStatus</span>
                                    }
                                    else
                                    {
                                        <span class="badge badge-info">@localizedProcessStatus</span>
                                    }
                                }
                            </td>
                        </tr>
                    }
                </tbody>
                <tfoot>
                    <tr>
                        <th>@Localizer["Date"]</th>
                        <th>@Localizer["Payment Type"]</th>
                        <th>@Localizer["Amount"]</th>
                        <th>@Localizer["Status"]</th>
                        <th>@Localizer["Process Status"]</th>
                    </tr>
                </tfoot>
            </table>
        </div>
        <!-- /.card-body -->
    </div>
    <!-- /.card -->

    <div class="case py-4">
        <div class="text-center text-black-50">@Localizer["Secure 256-bit TLS-encryption"]</div>
    </div>
</div>

@section Scripts {
    <script>
        // Function to show payment method under construction alert
        function showDepositMethodAlert() {
            Swal.fire({
                title: '@Html.Raw(Localizer["Under Construction"])',
                text: '@Html.Raw(Localizer["This payment method is under construction. Please try again later."])',
                icon: 'warning'
            });
            return false;
        }

        $(function () {
            $("#depositHistory").DataTable({
                "responsive": true,
                "lengthChange": false,
                "autoWidth": false,
                "buttons": ["copy", "csv", "excel", "pdf", "print", "colvis"],
                "order": [[0, "desc"]],
                "columnDefs": [
                    {
                        "targets": 0,
                        "type": "date-eu"
                    }
                ],
                "initComplete": function() {
                    // Force initial sort
                    this.api().order([0, 'desc']).draw();
                },
                "language": {
                    "search": "@Localizer["Search"]:",
                    "paginate": {
                        "first": "@Localizer["First"]",
                        "last": "@Localizer["Last"]",
                        "next": "@Localizer["Next"]",
                        "previous": "@Localizer["Previous"]"
                    },
                    "info": "@Localizer["Showing _START_ to _END_ of _TOTAL_ entries"]",
                    "infoEmpty": "@Localizer["No records available"]",
                    "infoFiltered": "@Localizer["(filtered from _MAX_ total records)"]",
                    "zeroRecords": "@Localizer["No matching records found"]"
                }
            }).buttons().container().appendTo('#depositHistory_wrapper .col-md-6:eq(0)');

            // Add SweetAlert to all links with href="#" that don't already have an onclick handler
            $('a[href="#"]:not([onclick])').on('click', function(e) {
                e.preventDefault();
                showDepositMethodAlert();
                return false;
            });
        });
    </script>
}