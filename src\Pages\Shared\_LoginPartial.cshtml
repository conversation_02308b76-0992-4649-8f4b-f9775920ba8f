@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.Enums
@using RazeWinComTr.Areas.Admin.Helpers
@inject IStringLocalizer<SharedResource> Localizer

@if (User.Identity != null && User.Identity.IsAuthenticated)
{
    <div class="headerTopRight">
        <ul>
            <li>
                <a href="@(User.IsInRole(Roller.Admin.ToString()) ? "/Admin/Dashboard" : "/MyAccount/Dashboard")">
                    <i class="flaticon-user-3 textX blueX"></i>@User.Identity.Name
                </a>
            </li>
            <li>
                <a href="@(User.IsInRole(Roller.Admin.ToString()) ? "/Admin/Dashboard" : "/MyAccount/TradeHistory")">
                    <i class="flaticon-bars textX blueX"></i>@Localizer["My Trade History"]
                </a>
            </li>
            <li>
                <a href="@(User.IsInRole(Roller.Admin.ToString()) ? "/Admin/Dashboard" : "/MyAccount/Wallet")">
                    <i class="flaticon-wallet textX blueX"></i>@Localizer["My Wallet"]
                </a>
            </li>
            <li>
                <a href="@(User.IsInRole(Roller.Admin.ToString()) ? "/Admin/Dashboard" : "/MyAccount/Profile")">
                    <i class="flaticon-user-3 textX blueX"></i>@Localizer["My Profile"]
                </a>
            </li>
            <li>
                <a href="@(User.IsInRole(Roller.Admin.ToString()) ? "/Admin/Account/Logout" : "/MyAccount/Logout")">
                    <i class="flaticon-close textX pinkX"></i>@Localizer["Logout"]
                </a>
            </li>
        </ul>
    </div>
}
else
{
    <div class="headerTopRight">
        <ul>
            <li><a href="/login"><i class="flaticon-user-3 textX blueX"></i>@Localizer["Login"]</a></li>
            <li><a href="/register"><i class="flaticon-quit textX greenX"></i>@Localizer["Register"]</a></li>
        </ul>
    </div>
}
