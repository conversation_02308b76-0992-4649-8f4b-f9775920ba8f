# Sonarqube
.sonarqube/

# Build outputs
[Bb]in/
[Oo]bj/
[Dd]ebug/
[Rr]elease/
x64/
x86/
[Ll]og/
[Ll]ogs/

# Visual Studio files
.vs/
*.suo
*.user
*.userosscache
*.sln.docstates
*.userprefs

# NuGet packages
**/packages/*
*.nupkg

# Test results
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*

# Generated files
artifacts/
.fake/
.idea/
*.sln.iml

# Keys and sensitive data
[Kk]eys/
*.pfx
*.publishsettings

# Static web assets
wwwroot/lib/
wwwroot/dist/
node_modules/

# Temporary files
*.tmp
*.tmp_proj
*~
*.log
*.vspscc
*.vssscc

# Database files
*.mdf
*.ldf
*.db-shm
*.db-wal

# Third-party libraries and plugins
src/wwwroot/plugins/
src/wwwroot/cdnjs.cloudflare.com/
src/wwwroot/fonts.googleapis.com/
src/wwwroot/kit.fontawesome.com/
src/wwwroot/s3.tradingview.com/
src/wwwroot/embed.tawk.to/

# Downloaded/cached content
src/wwwroot/hts-cache/
src/wwwroot/bitvortex.io/
src/wwwroot/cdn.pixabay.com/
src/wwwroot/i.hizliresim.com/
src/wwwroot/i.seadn.io/
src/wwwroot/images.hdqwalls.com/
src/wwwroot/wallpaperaccess.com/

# User uploads and dynamic content
src/wwwroot/uploads/
src/wwwroot/public/

# Test results and coverage
src_unittests/TestResults/
**/TestResults/
coverage.cobertura.xml

# Migration files (generated code)
src/Migrations/

# Specific to your project
c:\\razewin-data/