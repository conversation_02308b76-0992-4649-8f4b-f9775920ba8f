namespace RazeWinComTr.Areas.Admin.DbModel
{
    /// <summary>
    /// RZW Savings Account Status Constants
    /// </summary>
    public static class RzwSavingsStatus
    {
        public const string Active = "Active";
        public const string Matured = "Matured";
        public const string Withdrawn = "Withdrawn";
        public const string Cancelled = "Cancelled";
    }

    /// <summary>
    /// RZW Savings Term Type Constants
    /// </summary>
    public static class RzwSavingsTermType
    {
        public const string Daily = "Daily";
        public const string Monthly = "Monthly";
        public const string Yearly = "Yearly";
    }

    /// <summary>
    /// RZW Savings System Constants
    /// </summary>
    public static class RzwSavingsConstants
    {
        /// <summary>
        /// Default early withdrawal penalty percentage (10%)
        /// </summary>
        public const decimal DEFAULT_EARLY_WITHDRAWAL_PENALTY = 0.10m;

        /// <summary>
        /// Daily term duration in days
        /// </summary>
        public const int DAILY_TERM_DURATION = 1;

        /// <summary>
        /// Monthly term duration in days
        /// </summary>
        public const int MONTHLY_TERM_DURATION = 30;

        /// <summary>
        /// Yearly term duration in days
        /// </summary>
        public const int YEARLY_TERM_DURATION = 365;

        /// <summary>
        /// Maximum allowed interest rate (for validation)
        /// </summary>
        public const decimal MAX_INTEREST_RATE = 1.0m; // 100%

        /// <summary>
        /// Minimum allowed interest rate (for validation)
        /// </summary>
        public const decimal MIN_INTEREST_RATE = 0.0001m; // 0.01%
    }
}
