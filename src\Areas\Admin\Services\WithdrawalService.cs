using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.Withdrawal;
using RazeWinComTr.Areas.Admin.ViewModels.BalanceTransaction;

namespace RazeWinComTr.Areas.Admin.Services;

public class WithdrawalService
{
    private readonly AppDbContext _context;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly IBalanceTransactionService _balanceTransactionService;

    public WithdrawalService(
        IStringLocalizer<SharedResource> localizer,
        AppDbContext context,
        IBalanceTransactionService balanceTransactionService)
    {
        _localizer = localizer;
        _context = context;
        _balanceTransactionService = balanceTransactionService;
    }

    public async Task<Withdrawal?> GetByIdAsync(int id)
    {
        return await _context.Withdrawals
            .Include(w => w.User)
            .FirstOrDefaultAsync(w => w.Id == id);
    }

    public async Task<List<WithdrawalViewModel>> GetListAsync()
    {
        return await _context.Withdrawals
            .Include(w => w.User)
            .Select(w => new WithdrawalViewModel
            {
                Id = w.Id,
                UserId = w.UserId,
                UserEmail = w.User.Email,
                FullName = w.FullName,
                Email = w.Email,
                Balance = w.Balance,
                WithdrawalAmount = w.WithdrawalAmount,
                AccountHolder = w.AccountHolder,
                Iban = w.Iban,
                Status = w.Status,
                CreatedDate = w.CreatedDate
            })
            .OrderByDescending(w => w.CreatedDate)
            .ToListAsync();
    }

    public async Task<List<WithdrawalViewModel>> GetByUserIdAsync(int userId)
    {
        return await _context.Withdrawals
            .Where(w => w.UserId == userId)
            .Select(w => new WithdrawalViewModel
            {
                Id = w.Id,
                UserId = w.UserId,
                UserEmail = w.User.Email,
                FullName = w.FullName,
                Email = w.Email,
                Balance = w.Balance,
                WithdrawalAmount = w.WithdrawalAmount,
                AccountHolder = w.AccountHolder,
                Iban = w.Iban,
                Status = w.Status,
                CreatedDate = w.CreatedDate
            })
            .OrderByDescending(w => w.CreatedDate)
            .ToListAsync();
    }

    public async Task DeleteAsync(int id)
    {
        var entity = await _context.Withdrawals.FindAsync(id);
        if (entity != null)
        {
            _context.Withdrawals.Remove(entity);
            await _context.SaveChangesAsync();
        }
    }

    public async Task<Withdrawal> CreateAsync(Withdrawal withdrawal)
    {
        _context.Withdrawals.Add(withdrawal);
        await _context.SaveChangesAsync();
        return withdrawal;
    }

    public async Task UpdateAsync(Withdrawal withdrawal)
    {
        _context.Entry(withdrawal).State = EntityState.Modified;
        await _context.SaveChangesAsync();
    }

    public async Task<bool> UpdateWithdrawalStatusAsync(int withdrawalId, WithdrawalStatus newStatus)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            var withdrawal = await _context.Withdrawals
                .Include(w => w.User)
                .FirstOrDefaultAsync(w => w.Id == withdrawalId);

            if (withdrawal == null)
                return false;

            var previousStatus = withdrawal.Status;

            // If status hasn't changed, just return true
            if (previousStatus == newStatus)
                return true;

            // Update withdrawal status
            withdrawal.Status = newStatus;

            // If changing from pending/rejected to approved
            if (newStatus == WithdrawalStatus.Approved && previousStatus != WithdrawalStatus.Approved)
            {
                // Get user and update balance
                var user = await _context.Users.FindAsync(withdrawal.UserId);
                if (user == null)
                    throw new Exception(_localizer["User not found"].Value);

                decimal previousBalance = user.Balance;
                decimal newBalance = previousBalance - withdrawal.WithdrawalAmount;

                // Check if user has enough balance
                if (newBalance < 0)
                    throw new Exception(_localizer["Insufficient balance"].Value);

                // Update user's balance
                user.Balance = newBalance;
                user.ModDate = DateTime.UtcNow;
                _context.Users.Update(user);

                // Record the balance transaction
                await _balanceTransactionService.RecordTransactionAsync(
                   userId: withdrawal.UserId,
                   transactionType: TransactionType.Withdrawal,
                   amount: withdrawal.WithdrawalAmount,
                   previousBalance: previousBalance,
                   newBalance: newBalance,
                   description: _localizer["Withdrawal: {0}", withdrawal.Iban].Value,
                   referenceId: withdrawal.Id,
                   referenceType: BalanceTransactionReferenceTypes.Withdrawal,
                   existingContext: _context
                );
            }
            // If changing from approved to pending/rejected
            else if (newStatus != WithdrawalStatus.Approved && previousStatus == WithdrawalStatus.Approved)
            {
                // Get user and update balance (add back the withdrawn amount)
                var user = await _context.Users.FindAsync(withdrawal.UserId);
                if (user == null)
                    throw new Exception(_localizer["User not found"].Value);

                decimal previousBalance = user.Balance;
                decimal newBalance = previousBalance + withdrawal.WithdrawalAmount;

                // Update user's balance
                user.Balance = newBalance;
                user.ModDate = DateTime.UtcNow;
                _context.Users.Update(user);

                // Record the balance transaction
                await _balanceTransactionService.RecordTransactionAsync(
                   userId: withdrawal.UserId,
                   transactionType: TransactionType.Deposit,
                   amount: withdrawal.WithdrawalAmount,
                   previousBalance: previousBalance,
                   newBalance: newBalance,
                   description: _localizer["Withdrawal reversal: {0}", withdrawal.Iban].Value,
                   referenceId: withdrawal.Id,
                   referenceType: BalanceTransactionReferenceTypes.Withdrawal,
                   existingContext: _context
                );
            }

            await _context.SaveChangesAsync();
            await transaction.CommitAsync();
            return true;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }
}
