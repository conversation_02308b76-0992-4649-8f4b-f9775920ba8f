using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Formats.Jpeg;
using SixLabors.ImageSharp.Processing;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;

namespace RazeWinComTr.Areas.Admin.Pages.Download;

public class ThumbnailByFileIdModel : PageModel
{
    private readonly AppDbContext _context;
    private readonly FileService _fileService;

    public ThumbnailByFileIdModel(AppDbContext context, FileService fileService)
    {
        _context = context;
        _fileService = fileService;
    }

    public async Task<IActionResult> OnGetAsync(string fileType, string id) //todo AUTH
    {
        if (string.IsNullOrEmpty(id)) return NotFound();
        var parsedIdStr = int.TryParse(id, out var genericId);
        if (!parsedIdStr) return BadRequest("Parse Error");
        var userId = User.GetClaimUserId();
        if (!userId.HasValue) return NotFound();
        string? fileRelativePath = null;
        //TODO
        //if (fileType == nameof(ProductFile))
        //{
        //    var entity = await _context.ProductFiles
        //        .Include(p => p.Product)
        //        .Where(p => p.ProductFileId == genericId)
        //        .FirstOrDefaultAsync();
        //    if (entity == null) return NotFound();

        //    fileRelativePath = entity.FileUrl;
        //}
        //else if (fileType == nameof(ProjectFile))
        //{
        //    var entity = await _context.ProjectFiles
        //        .Include(p => p.Project)
        //        .Where(p => p.ProjectFileId == genericId)
        //        .FirstOrDefaultAsync();
        //    if (entity == null) return NotFound();

        //    fileRelativePath = entity.FileUrl;
        //}
        //else if (fileType == nameof(DbModel.Reference))
        //{
        //    var entity = await _context.References
        //        .Where(p => p.ReferenceId == genericId)
        //        .FirstOrDefaultAsync();
        //    if (entity == null) return NotFound();

        //    fileRelativePath = entity.OrganizationLogoUrl;
        //}
        //else if (fileType == nameof(DbModel.Content))
        //{
        //    var entity = await _context.Contents
        //        .Where(p => p.ContentId == genericId)
        //        .FirstOrDefaultAsync();
        //    if (entity == null) return NotFound();

        //    fileRelativePath = entity.ImageUrl;
        //}
        if (fileType == nameof(DbModel.Market))
        {
            var entity = await _context.Markets
                .Where(p => p.Id == genericId)
                .FirstOrDefaultAsync();
            if (entity == null) return NotFound();

            fileRelativePath = entity.IconUrl;
        }

        //TODO �ET�N ENABLE WHEN PROJECTS TABLE IS READY
        //else if (fileType == nameof(DEVICE_GROUP_FILE))
        //{
        //    var entity = await _context.DEVICE_GROUP_FILEs
        //        .Include(p => p.DEVICE_GROUP)
        //        .Where(p => p.DEVICE_GROUP_FILE_ID == genericId).FirstOrDefaultAsync();
        //    if (entity == null) return NotFound();
        //    if (!User.IsInAdminRole() && userId != entity.DEVICE_GROUP.COMPANY_ID)
        //        return Unauthorized();
        //    fileRelativePath = entity.FILE_URL;
        //}
        if (fileRelativePath == null) return NotFound();
        if (string.IsNullOrEmpty(_fileService.FileStoragePath)) return NotFound(); // Return 404 if filename is missing
        // Resolve the full file path by combining the storage path with the relative filename
        var filePath = Path.Combine(_fileService.FileStoragePath, fileRelativePath);

        // Normalize the file path to prevent directory traversal
        var fullFilePath = Path.Combine(_fileService.FileStoragePath, filePath);
        var fileName = Path.GetFileName(fullFilePath); // Ensure only the file name is returned

        // Check if the file exists
        if (!System.IO.File.Exists(fullFilePath)) return NotFound(); // Return 404 if the file doesn't exist

        // Process the image and create a thumbnail
        try
        {
            var fileBytes = await System.IO.File.ReadAllBytesAsync(fullFilePath); // Read the file into memory

            using (var image = Image.Load(fileBytes)) // Load from byte array
            {
                // Resize the image to a thumbnail (200x200 for example)
                var thumbnail = image.Clone(ctx => ctx.Resize(64, 64));

                // Compress the thumbnail to reduce file size
                using (var thumbnailStream = new MemoryStream())
                {
                    // Save the thumbnail image to the memory stream
                    await thumbnail.SaveAsync(thumbnailStream,
                        new JpegEncoder { Quality = 50 }); // 50% quality for smaller file size

                    // Ensure the stream's position is set to the beginning before using it
                    thumbnailStream.Position = 0;
                    // Return the thumbnail with cache headers
                    Response.Headers.CacheControl = "public, max-age=86400"; // Cache for 30 days
                    Response.Headers.Expires = DateTime.UtcNow.AddDays(1).ToString("R");
                    var contentType = ContentTypesHelper.GetImageContentType(fileName);
                    // Return the thumbnail as a JPEG image
                    return File(thumbnailStream.ToArray(), contentType, fileName);
                }
            }
        }
        catch (Exception ex)
        {
            // Log the error if needed
            return StatusCode(500, "Error processing the image: " + ex.Message);
        }
    }
}