using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels;
using RazeWinComTr.Areas.Admin.ViewModels.Package;

namespace RazeWinComTr.Areas.Admin.Pages.PackageRewardPercentage;

public class CreateModel : PageModel
{
    private readonly PackageRewardPercentageService _percentageService;
    private readonly PackageService _packageService;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public CreateModel(
        PackageRewardPercentageService percentageService,
        PackageService packageService,
        IStringLocalizer<SharedResource> localizer)
    {
        _percentageService = percentageService;
        _packageService = packageService;
        _localizer = localizer;
    }

    public SweetAlert2Message? AlertMessage { get; set; }

    [BindProperty] public PackageRewardPercentageCreateViewModel ViewEntity { get; set; } = new();

    public List<PackageViewModel> Packages { get; set; } = new();



    public async Task OnGetAsync()
    {
        Packages = await _packageService.GetListAsync();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        try
        {
            var userId = User.GetClaimUserId();
            if (!userId.HasValue) return Unauthorized();

            if (!ModelState.IsValid) return Page();

            // Check if percentage already exists for this package and level
            var existingPercentage = await _percentageService.GetByPackageAndLevelAsync(ViewEntity.PackageId, ViewEntity.Level);
            if (existingPercentage != null)
            {
                AlertMessage = new SweetAlert2Message
                {
                    Title = _localizer["Warning"],
                    Text = _localizer["A percentage for this package and level already exists. Please edit the existing one."],
                    Icon = "warning"
                };
                Packages = await _packageService.GetListAsync();
                return Page();
            }

            var entity = new DbModel.PackageRewardPercentage
            {
                PackageId = ViewEntity.PackageId,
                Level = ViewEntity.Level,
                // Percentage property removed
                TlPercentage = ViewEntity.TlPercentage,
                RzwPercentage = ViewEntity.RzwPercentage,
                CreatedDate = DateTime.UtcNow
            };
            await _percentageService.CreateAsync(entity);

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Success"],
                Text = _localizer["Successfully saved"],
                Icon = "success",
                RedirectUrl = "/Admin/PackageRewardPercentage"
            };

            return Page();
        }
        catch (Exception ex)
        {
            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Error"],
                Text = ex.Message,
                Icon = "error"
            };
            Packages = await _packageService.GetListAsync();
            return Page();
        }
    }
}

public class PackageRewardPercentageCreateViewModel
{
    public int PackageId { get; set; }
    public int Level { get; set; } = 1;
    public decimal TlPercentage { get; set; }
    public decimal RzwPercentage { get; set; }
}
