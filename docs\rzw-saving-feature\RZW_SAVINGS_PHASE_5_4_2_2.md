# Alt Adım *******: Account Overview Card (30-45 dakika)

## 📋 Alt Adım Özeti
Account overview card'ının oluşturul<PERSON>. Ana hesap bilgileri, key metrics ve status indicators'ın implementasyonu.

## 🎯 Hedefler
- ✅ Main account information card
- ✅ Key metrics display
- ✅ Status indicators
- ✅ Interactive elements

## 📊 Yapılacak İşler

### ✅ YAPILACAKLAR

#### *******.1 Account Overview Card HTML

**Dosya**: `src/Areas/MyAccount/Pages/RzwSavings/Details.cshtml` (Main content area güncelleme)
```html
<!-- Main Content Area (Details.cshtml'e ekleme) -->
<div class="details-content">
    <!-- Account Overview Card -->
    <div class="account-overview-section">
        <div class="overview-card">
            <div class="card-header">
                <div class="header-content">
                    <h5 class="card-title">
                        <i class="fas fa-chart-pie text-primary"></i>
                        @Localizer["Account Overview"]
                    </h5>
                    <div class="header-actions">
                        <span class="badge @Model.ViewModel.Account.StatusBadgeClass status-badge">
                            @Model.ViewModel.Account.StatusDisplayText
                        </span>
                    </div>
                </div>
            </div>

            <div class="card-body">
                <div class="row">
                    <!-- Left Column - Investment Info -->
                    <div class="col-lg-6">
                        <div class="investment-info">
                            <div class="info-header">
                                <h6 class="info-title">@Localizer["Investment Information"]</h6>
                            </div>
                            
                            <div class="info-grid">
                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="fas fa-tag"></i>
                                        @Localizer["Plan"]
                                    </div>
                                    <div class="info-value">@Model.ViewModel.Account.PlanName</div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="fas fa-coins"></i>
                                        @Localizer["Principal Amount"]
                                    </div>
                                    <div class="info-value amount">
                                        @Model.ViewModel.Account.FormattedAmount RZW
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="fas fa-percentage"></i>
                                        @Localizer["Interest Rate"]
                                    </div>
                                    <div class="info-value rate">
                                        @Model.ViewModel.Account.FormattedInterestRate
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="fas fa-clock"></i>
                                        @Localizer["Term"]
                                    </div>
                                    <div class="info-value">@Model.ViewModel.Account.TermDisplayText</div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="fas fa-calendar-plus"></i>
                                        @Localizer["Start Date"]
                                    </div>
                                    <div class="info-value">@Model.ViewModel.Account.FormattedStartDate</div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="fas fa-calendar-check"></i>
                                        @Localizer["Maturity Date"]
                                    </div>
                                    <div class="info-value">@Model.ViewModel.Account.FormattedMaturityDate</div>
                                </div>
                            </div>

                            @if (Model.ViewModel.Account.AutoRenew)
                            {
                                <div class="auto-renew-indicator">
                                    <i class="fas fa-sync-alt text-info"></i>
                                    <span>@Localizer["Auto-renewal enabled"]</span>
                                </div>
                            }
                        </div>
                    </div>

                    <!-- Right Column - Earnings & Progress -->
                    <div class="col-lg-6">
                        <div class="earnings-progress">
                            <!-- Earnings Summary -->
                            <div class="earnings-summary">
                                <div class="summary-header">
                                    <h6 class="summary-title">@Localizer["Earnings Summary"]</h6>
                                </div>
                                
                                <div class="earnings-cards">
                                    <div class="earning-card current">
                                        <div class="earning-icon">
                                            <i class="fas fa-chart-line"></i>
                                        </div>
                                        <div class="earning-content">
                                            <div class="earning-value" data-earning="total">
                                                @Model.ViewModel.Account.FormattedTotalEarned
                                            </div>
                                            <div class="earning-label">@Localizer["Total Earned"]</div>
                                            <div class="earning-currency">RZW</div>
                                        </div>
                                    </div>

                                    <div class="earning-card projected">
                                        <div class="earning-icon">
                                            <i class="fas fa-bullseye"></i>
                                        </div>
                                        <div class="earning-content">
                                            <div class="earning-value" data-earning="projected">
                                                @Model.ViewModel.Account.FormattedProjectedTotal
                                            </div>
                                            <div class="earning-label">@Localizer["Projected Total"]</div>
                                            <div class="earning-currency">RZW</div>
                                        </div>
                                    </div>

                                    <div class="earning-card maturity">
                                        <div class="earning-icon">
                                            <i class="fas fa-trophy"></i>
                                        </div>
                                        <div class="earning-content">
                                            <div class="earning-value highlight" data-earning="maturity">
                                                @Model.ViewModel.Account.FormattedMaturityAmount
                                            </div>
                                            <div class="earning-label">@Localizer["At Maturity"]</div>
                                            <div class="earning-currency">RZW</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Progress Section -->
                            <div class="progress-section">
                                <div class="progress-header">
                                    <h6 class="progress-title">@Localizer["Investment Progress"]</h6>
                                    <span class="progress-percentage" data-progress="percentage">
                                        @Model.ViewModel.Account.ProgressPercentage.ToString("N1")%
                                    </span>
                                </div>
                                
                                <div class="progress-bar-container">
                                    <div class="progress progress-lg">
                                        <div class="progress-bar @Model.ViewModel.Account.ProgressBarClass" 
                                             role="progressbar" 
                                             style="width: @Model.ViewModel.Account.ProgressPercentage.ToString("N1")%" 
                                             data-progress="bar">
                                        </div>
                                    </div>
                                    <div class="progress-labels">
                                        <span class="progress-start">@Localizer["Start"]</span>
                                        <span class="progress-end">@Localizer["Maturity"]</span>
                                    </div>
                                </div>

                                <div class="progress-stats">
                                    <div class="stat-item">
                                        <span class="stat-label">@Localizer["Days Elapsed"]:</span>
                                        <span class="stat-value" data-progress="elapsed">@Model.ViewModel.Account.DaysElapsed</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">@Localizer["Days Remaining"]:</span>
                                        <span class="stat-value" data-progress="remaining">@Model.ViewModel.Account.DaysRemaining</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">@Localizer["Total Days"]:</span>
                                        <span class="stat-value">@Model.ViewModel.Account.DaysTotal</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Card Footer with Quick Actions -->
            @if (Model.ViewModel.CanPerformActions)
            {
                <div class="card-footer">
                    <div class="quick-actions">
                        <div class="actions-header">
                            <span class="actions-title">@Localizer["Quick Actions"]:</span>
                        </div>
                        <div class="actions-buttons">
                            @foreach (var action in Model.ViewModel.AvailableActions.Take(3))
                            {
                                @if (!string.IsNullOrEmpty(action.Url))
                                {
                                    <a href="@action.Url" class="btn btn-sm @action.ButtonClass">
                                        <i class="@action.IconClass"></i>
                                        @Localizer[action.DisplayName]
                                    </a>
                                }
                                else if (!string.IsNullOrEmpty(action.OnClickFunction))
                                {
                                    <button type="button" 
                                            class="btn btn-sm @action.ButtonClass" 
                                            onclick="@action.OnClickFunction"
                                            @(action.RequiresConfirmation ? $"data-confirm=\"{action.ConfirmationMessage}\"" : "")>
                                        <i class="@action.IconClass"></i>
                                        @Localizer[action.DisplayName]
                                    </button>
                                }
                            }
                            
                            @if (Model.ViewModel.AvailableActions.Count > 3)
                            {
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="showAllActions()">
                                    <i class="fas fa-ellipsis-h"></i>
                                    @Localizer["More"]
                                </button>
                            }
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>

    <!-- Diğer bölümler buraya gelecek -->
</div>
```

#### *******.2 Account Overview Card CSS

**Dosya**: `src/wwwroot/css/rzw-savings-details.css` (Overview card styles ekleme)
```css
/* Account Overview Card */
.account-overview-section {
    margin-bottom: 30px;
}

.overview-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.overview-card .card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 20px 25px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    margin: 0;
    color: #2c3e50;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-badge {
    font-size: 0.85rem;
    padding: 6px 12px;
    border-radius: 20px;
}

.overview-card .card-body {
    padding: 30px 25px;
}

/* Investment Info */
.investment-info {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    height: 100%;
}

.info-header {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.info-title {
    color: #495057;
    font-weight: 600;
    margin: 0;
    font-size: 1rem;
}

.info-grid {
    display: grid;
    gap: 15px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #e9ecef;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    color: #6c757d;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-label i {
    width: 16px;
    text-align: center;
    color: #667eea;
}

.info-value {
    font-weight: 600;
    color: #2c3e50;
    text-align: right;
}

.info-value.amount {
    font-family: 'Courier New', monospace;
    color: #28a745;
}

.info-value.rate {
    color: #17a2b8;
    font-family: 'Courier New', monospace;
}

.auto-renew-indicator {
    margin-top: 15px;
    padding: 10px;
    background: #e8f4fd;
    border: 1px solid #bee5eb;
    border-radius: 8px;
    color: #0c5460;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Earnings & Progress */
.earnings-progress {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 25px;
}

/* Earnings Summary */
.earnings-summary {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
}

.summary-header {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.summary-title {
    color: #495057;
    font-weight: 600;
    margin: 0;
    font-size: 1rem;
}

.earnings-cards {
    display: grid;
    gap: 15px;
}

.earning-card {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.earning-card.current {
    background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
    border: 1px solid #d4edda;
}

.earning-card.projected {
    background: linear-gradient(135deg, #e3f2fd, #f0f8ff);
    border: 1px solid #b3e5fc;
}

.earning-card.maturity {
    background: linear-gradient(135deg, #fff3e0, #fef7f0);
    border: 1px solid #ffcc02;
}

.earning-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.earning-icon {
    background: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.earning-card.current .earning-icon {
    color: #28a745;
}

.earning-card.projected .earning-icon {
    color: #17a2b8;
}

.earning-card.maturity .earning-icon {
    color: #ffc107;
}

.earning-content {
    flex: 1;
}

.earning-value {
    font-size: 1.3rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
    color: #2c3e50;
    line-height: 1.2;
}

.earning-value.highlight {
    color: #28a745;
    font-size: 1.4rem;
}

.earning-label {
    color: #6c757d;
    font-size: 0.85rem;
    margin-top: 2px;
}

.earning-currency {
    color: #495057;
    font-size: 0.8rem;
    font-weight: 500;
    margin-top: 2px;
}

/* Progress Section */
.progress-section {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.progress-title {
    color: #495057;
    font-weight: 600;
    margin: 0;
    font-size: 1rem;
}

.progress-percentage {
    font-size: 1.2rem;
    font-weight: 700;
    color: #28a745;
    font-family: 'Courier New', monospace;
}

.progress-bar-container {
    margin-bottom: 15px;
}

.progress-lg {
    height: 12px;
    border-radius: 6px;
    background-color: #e9ecef;
    margin-bottom: 8px;
}

.progress-lg .progress-bar {
    border-radius: 6px;
    transition: width 0.6s ease;
}

.progress-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #6c757d;
}

.progress-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    color: #6c757d;
    font-size: 0.85rem;
}

.stat-value {
    font-weight: 600;
    color: #2c3e50;
    font-family: 'Courier New', monospace;
}

/* Quick Actions */
.overview-card .card-footer {
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    padding: 15px 25px;
}

.quick-actions {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.actions-title {
    color: #495057;
    font-weight: 500;
    font-size: 0.9rem;
}

.actions-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.actions-buttons .btn {
    font-size: 0.85rem;
    padding: 6px 12px;
    border-radius: 6px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .overview-card .card-header,
    .overview-card .card-body,
    .overview-card .card-footer {
        padding: 20px;
    }
    
    .header-content {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .earnings-progress {
        gap: 20px;
    }
    
    .earning-card {
        padding: 12px;
    }
    
    .earning-value {
        font-size: 1.1rem;
    }
    
    .earning-value.highlight {
        font-size: 1.2rem;
    }
    
    .quick-actions {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .actions-buttons {
        width: 100%;
        justify-content: space-between;
    }
    
    .actions-buttons .btn {
        flex: 1;
        min-width: 0;
    }
}

@media (max-width: 576px) {
    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .info-value {
        text-align: left;
        align-self: flex-end;
    }
    
    .earning-card {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .progress-stats {
        grid-template-columns: 1fr;
    }
    
    .actions-buttons {
        flex-direction: column;
        width: 100%;
    }
    
    .actions-buttons .btn {
        width: 100%;
    }
}

/* Animation Classes */
.overview-card {
    animation: cardSlideIn 0.6s ease-out;
}

@keyframes cardSlideIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.earning-card {
    animation: earningCardSlideIn 0.4s ease-out;
}

@keyframes earningCardSlideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}
```

## 📋 Alt Adım Kontrol Listesi

### ✅ YAPILACAKLAR
- [ ] Account overview card HTML oluşturma
- [ ] Investment information section
- [ ] Earnings summary cards
- [ ] Progress section
- [ ] Quick actions footer
- [ ] CSS styling ekleme
- [ ] Responsive design optimizasyonu

### 🔄 YAPILMAKTA OLANLAR
- (Henüz başlanmadı)

### ✅ YAPILMIŞLAR
- (Henüz tamamlanmadı)

## 📝 Notlar

### Card Features
- **Investment info**: Plan details, amounts, dates
- **Earnings summary**: Current, projected, maturity amounts
- **Progress tracking**: Visual progress bar with stats
- **Quick actions**: Most common actions in footer

### Visual Design
- **Color coding**: Different colors for different earning types
- **Icons**: Meaningful icons for each section
- **Gradients**: Subtle gradients for visual appeal
- **Animations**: Smooth slide-in animations

### Responsive Behavior
- **Desktop**: Two-column layout
- **Tablet**: Stacked sections with optimized spacing
- **Mobile**: Single column with simplified layout

### Sonraki Alt Adım
Bu alt adım tamamlandıktan sonra **Alt Adım 5.4.2.3: Progress Indicators** başlayacak.

---
**Tahmini Süre**: 30-45 dakika
**Öncelik**: Yüksek
**Bağımlılıklar**: Alt Adım 5.4.2.1 tamamlanmış olmalı
