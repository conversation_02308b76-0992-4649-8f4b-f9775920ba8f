using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.ViewModels.User;

namespace RazeWinComTr.Areas.Admin.Pages.User;

public class IndexModel : PageModel
{
    private readonly AppDbContext _context;

    public IndexModel(AppDbContext context)
    {
        _context = context;
        Items = [];
    }

    public IList<UserViewModel> Items { get; set; }

    public async Task OnGetAsync()
    {
        var query = _context.Users.AsQueryable();

        if (!User.IsInAdminRole())
        {
            var userId = User.GetClaimUserId();
            if (!userId.HasValue)
            {
                ModelState.AddModelError("", "NameIdentifier claim NOT FOUND.");
                return;
            }

            query = query.Where(u => u.UserId == userId);
        }

        Items = await query
            .Select(u => new UserViewModel
            {
                UserId = u.UserId,
                Email = u.Email,
                FullName = u.Name + " " + u.Surname,
                Name = u.Name,
                Surname = u.Surname,
                PhoneNumber = u.PhoneNumber,
                IdentityNumber = u.IdentityNumber,
                Iban = u.Iban,
                Balance = u.Balance,
                IsActive = u.IsActive,
                CrDate = u.CrDate
            })
            .OrderByDescending(u => u.CrDate)
            .ToListAsync();
    }
}