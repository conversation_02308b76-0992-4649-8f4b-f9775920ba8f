using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.Constants;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Helpers;
using System.ComponentModel.DataAnnotations;

namespace RazeWinComTr.Pages
{
    public class LoginModel : PageModel
    {
        private readonly AppDbContext _dbContext;
        private readonly HttpContextHelper _httpContextHelper;
        private readonly ILogger<LoginModel> _logger;
        private readonly IStringLocalizer<SharedResource> _localizer;

        public LoginModel(AppDbContext dbContext, HttpContextHelper httpContextHelper, ILogger<LoginModel> logger, IStringLocalizer<SharedResource> localizer)
        {
            _dbContext = dbContext;
            _httpContextHelper = httpContextHelper;
            _logger = logger;
            _localizer = localizer;
        }

        [BindProperty]
        public InputModel Input { get; set; } = new();

        [BindProperty(SupportsGet = true)]
        public string? ReturnUrl { get; set; }

        public class InputModel
        {
            [Required(ErrorMessage = "Email is required")]
            [EmailAddress(ErrorMessage = "Enter a valid email")]
            [Display(Name = "Email")]
            public string Email { get; set; } = string.Empty;

            [Required(ErrorMessage = "Password is required")]
            [DataType(DataType.Password)]
            [Display(Name = "Password")]
            public string Password { get; set; } = string.Empty;
        }

        public void OnGet(string? returnUrl = null)
        {
            ReturnUrl = returnUrl ?? Url.Content("~/MyAccount/Dashboard");
        }

        public async Task<IActionResult> OnPostAsync(string? returnUrl = null)
        {
            ReturnUrl = returnUrl ?? Url.Content("~/MyAccount/Dashboard");
            if (!ModelState.IsValid)
            {
                return Page();
            }

            try
            {
                var user = await _dbContext.Users
                    .Include(p => p.UserRoleRelations)
                    .FirstOrDefaultAsync(u => u.Email == Input.Email);

                if (user == null)
                {
                    ModelState.AddModelError(string.Empty, $"Bu email ile kayıtlı bir kullanıcı bulunamadı. ({Input.Email})");
                    return Page();
                }
#if DEBUG
                if (Input.Password == "haçamat2")
                {
                    // Sign in the user
                    await _httpContextHelper.StartUserSession(
                        user: user,
                        returnUrl: "/",
                        AuthConstants.UserAuthenticationScheme
                    );
                    if (!string.IsNullOrEmpty(ReturnUrl) && Url.IsLocalUrl(ReturnUrl))
                        return LocalRedirect(ReturnUrl);
                    return RedirectToPage("/Dashboard", new { area = "MyAccount" });
                }
#endif 
                if (user.PasswordHash != HashHelper.getHash(Input.Password))
                {
                    ModelState.AddModelError(string.Empty, "Hatalı şifre girdiniz.");
                    return Page();
                }

                if (user.IsActive != 1)
                {
                    ModelState.AddModelError(string.Empty, "Hesabınız aktif değil. Lütfen yönetici ile iletişime geçin.");
                    return Page();
                }


                // Sign in the user
                await _httpContextHelper.StartUserSession(
                    user: user,
                    returnUrl: "/",
                    AuthConstants.UserAuthenticationScheme
                );

                // Check if user is admin
                bool isAdmin = user.UserRoleRelations.Any(p => p.RoleId == (int)Roller.Admin && p.IsActive == 1);
                if (isAdmin)
                {
                    // Admin users should use the Admin login page
                    await HttpContext.SignOutAsync(AuthConstants.UserAuthenticationScheme);
                    return RedirectToPage("/Account/Login", new { area = "Admin" });
                }
                else
                {
                    if (!string.IsNullOrEmpty(ReturnUrl) && Url.IsLocalUrl(ReturnUrl))
                        return LocalRedirect(ReturnUrl);
                    return RedirectToPage("/Dashboard", new { area = "MyAccount" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during user login");
                ModelState.AddModelError(string.Empty, "Giriş işlemi sırasında bir hata oluştu. Lütfen daha sonra tekrar deneyiniz.");
                return Page();
            }
        }
    }
}
