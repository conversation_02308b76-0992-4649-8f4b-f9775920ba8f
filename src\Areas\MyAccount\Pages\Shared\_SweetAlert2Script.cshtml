@using RazeWinComTr.Areas.Admin.ViewModels
@model SweetAlert2Message

@if (Model != null)
{
    <script id="scriptMessage" type="text/javascript">
        window.onload = function () {
            var redirectUrl = '@(Model.RedirectUrl ?? string.Empty)';
            var alertIcon = '@Html.Raw(Model.Icon)';
            
            Swal.fire({
                title: '@Html.Raw(Model.Title)',
                text: '@Html.Raw(Model.Text)',
                icon: alertIcon
            }).then((result) => {
                // For success messages, always redirect regardless of how the alert was closed
                if (alertIcon === 'success' && redirectUrl !== '') {
                    location.href = redirectUrl;
                }
                // For other types (error, warning), only redirect if user clicked OK
                else if (result.isConfirmed && redirectUrl !== '') {
                    location.href = redirectUrl;
                }
            });
            $('#scriptMessage').remove();
        }
    </script>
}
