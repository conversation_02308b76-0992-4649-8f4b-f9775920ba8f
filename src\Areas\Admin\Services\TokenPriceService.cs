using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using System;

namespace RazeWinComTr.Areas.Admin.Services
{
    public class TokenPriceService : ITokenPriceService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<TokenPriceService> _logger;
        private readonly IStringLocalizer<TokenPriceService> _localizer;

        public TokenPriceService(
            AppDbContext context,
            ILogger<TokenPriceService> logger,
            IStringLocalizer<TokenPriceService> localizer)
        {
            _context = context;
            _logger = logger;
            _localizer = localizer;
        }

        /// <summary>
        /// Gets the RZW market from the database
        /// </summary>
        /// <returns>The RZW market entity</returns>
        /// <exception cref="InvalidOperationException">Thrown when the RZW market is not found</exception>
        private async Task<Market> GetRzwMarketAsync()
        {
            var rzwMarket = await _context.Markets
                .FirstOrDefaultAsync(m => m.Coin == "RZW" && (m.PairCode == "RZWTRY" || m.PairCode == "RZW/TRY"));

            if (rzwMarket == null)
            {
                _logger.LogError("RZW token not found in the Markets table.");
                throw new InvalidOperationException(_localizer["RZW token not found in the system"].Value);
            }

            return rzwMarket;
        }

        /// <summary>
        /// Gets the RZW market from the database
        /// </summary>
        /// <returns>The RZW market entity</returns>
        /// <exception cref="InvalidOperationException">Thrown when the RZW market is not found</exception>
        private async Task<Market> GetCoinMarketAsync(int coinId)
        {
            var marketRecord = await _context.Markets
                .FirstOrDefaultAsync(m => m.Id == coinId);

            if (marketRecord == null)
            {
                _logger.LogError("coin not found in the Markets table.");
                throw new InvalidOperationException(_localizer["Not found"].Value);
            }

            return marketRecord;
        }

        /// <summary>
        /// Gets the current RZW token buy price in TRY
        /// </summary>
        /// <returns>The current RZW token buy price</returns>
        /// <exception cref="InvalidOperationException">Thrown when the price is not available or invalid</exception>
        public async Task<decimal> GetCurrentRzwBuyPriceAsync()
        {
            // RZW token'ın alış fiyatını veritabanından al
            var rzwMarket = await GetRzwMarketAsync();

            if (rzwMarket.BuyPrice > 0)
            {
                return rzwMarket.BuyPrice;
            }

            // Hata durumu - varsayılan değer kullanmak yerine hata fırlatıyoruz
            _logger.LogError("RZW token buy price is not available or invalid: {Price}", rzwMarket.BuyPrice);
            throw new InvalidOperationException(_localizer["RZW token buy price is not available or invalid"].Value);
        }

        /// <summary>
        /// Gets the RZW token ID from the Markets table
        /// </summary>
        /// <returns>The RZW token ID</returns>
        /// <exception cref="InvalidOperationException">Thrown when the RZW token is not found in the system</exception>
        public async Task<int> GetRzwTokenIdAsync()
        {
            var rzwMarket = await GetRzwMarketAsync();
            return rzwMarket.Id;
        }

        /// <summary>
        /// Gets complete RZW token information including ID and prices in a single database query
        /// </summary>
        /// <returns>RZW token information</returns>
        /// <exception cref="InvalidOperationException">Thrown when the RZW token is not found or prices are invalid</exception>
        public async Task<RzwTokenInfo> GetRzwTokenInfoAsync()
        {
            var rzwMarket = await GetRzwMarketAsync();

            if (rzwMarket.BuyPrice <= 0)
            {
                _logger.LogError("RZW token buy price is not available or invalid: {Price}", rzwMarket.BuyPrice);
                throw new InvalidOperationException(_localizer["RZW token buy price is not available or invalid"].Value);
            }

            return new RzwTokenInfo
            {
                TokenId = rzwMarket.Id,
                BuyPrice = rzwMarket.BuyPrice,
                SellPrice = rzwMarket.SellPrice
            };
        }

        /// <summary>
        /// Gets complete RZW token information including ID and prices in a single database query
        /// </summary>
        /// <returns>RZW token information</returns>
        /// <exception cref="InvalidOperationException">Thrown when the RZW token is not found or prices are invalid</exception>
        public async Task<RzwTokenInfo> GetCoinInfoAsync(int coinId)
        {
            var marketRecord = await GetCoinMarketAsync(coinId);

            if (marketRecord.BuyPrice <= 0)
            {
                _logger.LogError("RZW token buy price is not available or invalid: {Price}", marketRecord.BuyPrice);
                throw new InvalidOperationException(_localizer["RZW token buy price is not available or invalid"].Value);
            }

            return new RzwTokenInfo
            {
                TokenId = marketRecord.Id,
                BuyPrice = marketRecord.BuyPrice,
                SellPrice = marketRecord.SellPrice
            };
        }
    }
}
