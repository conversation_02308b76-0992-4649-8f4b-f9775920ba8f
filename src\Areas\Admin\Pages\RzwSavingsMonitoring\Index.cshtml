@page
@using Microsoft.Extensions.Localization
@model RazeWinComTr.Areas.Admin.Pages.RzwSavingsMonitoring.IndexModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["System Monitoring"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["System Monitoring"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="#">@L["RZW Savings"]</a></li>
                    <li class="breadcrumb-item active">@L["Monitoring"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <!-- System Health Cards -->
        <div class="row">
            <div class="col-lg-3 col-6">
                <div class="small-box bg-info">
                    <div class="inner">
                        <h3>@Model.SystemHealth.TotalActiveAccounts</h3>
                        <p>@L["Active Accounts"]</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-piggy-bank"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3>@Model.SystemHealth.TotalLockedRzw.ToString("N2")</h3>
                        <p>@L["Total Locked RZW"]</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-lock"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-warning">
                    <div class="inner">
                        <h3>@Model.SystemHealth.PendingInterestPayments</h3>
                        <p>@L["Pending Interest Payments"]</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-danger">
                    <div class="inner">
                        <h3>@Model.SystemHealth.MaturedAccounts</h3>
                        <p>@L["Matured Accounts"]</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Today's Activity -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">@L["Today's Activity"]</h3>
                    </div>
                    <div class="card-body">
                        <dl class="row">
                            <dt class="col-sm-6">@L["Interest Payments"]:</dt>
                            <dd class="col-sm-6">@Model.SystemHealth.TodayInterestCount</dd>

                            <dt class="col-sm-6">@L["Total Interest Paid"]:</dt>
                            <dd class="col-sm-6">@Model.SystemHealth.TodayInterestPayments.ToString("N8") RZW</dd>

                            <dt class="col-sm-6">@L["Last Update"]:</dt>
                            <dd class="col-sm-6">@Model.SystemHealth.LastUpdateTime.ToString("yyyy-MM-dd HH:mm:ss")</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <!-- 30-Day Performance -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">@L["Performance Metrics"] (30 @L["Days"])</h3>
                    </div>
                    <div class="card-body">
                        <dl class="row">
                            <dt class="col-sm-6">@L["New Accounts"]:</dt>
                            <dd class="col-sm-6">@Model.Performance.NewAccountsLast30Days</dd>

                            <dt class="col-sm-6">@L["Total Interest Paid"]:</dt>
                            <dd class="col-sm-6">@Model.Performance.TotalInterestPaidLast30Days.ToString("N8") RZW</dd>

                            <dt class="col-sm-6">@L["Early Withdrawals"]:</dt>
                            <dd class="col-sm-6">@Model.Performance.EarlyWithdrawalsLast30Days</dd>

                            <dt class="col-sm-6">@L["Matured Accounts"]:</dt>
                            <dd class="col-sm-6">@Model.Performance.MaturedAccountsLast30Days</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Plan Performance -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">@L["Plan Performance"]</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                <tr>
                                    <th>@L["Plan"]</th>
                                    <th>@L["Term"]</th>
                                    <th>@L["Interest Rate"]</th>
                                    <th>@L["Status"]</th>
                                    <th>@L["Total Accounts"]</th>
                                    <th>@L["Active Accounts"]</th>
                                    <th>@L["Locked RZW"]</th>
                                    <th>@L["Interest Paid"]</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach (var plan in Model.PlanPerformance)
                                {
                                    <tr>
                                        <td>@plan.PlanName</td>
                                        <td>@plan.TermDuration @plan.TermType</td>
                                        <td>@((plan.InterestRate * 100).ToString("N4"))%</td>
                                        <td>
                                            <span class="badge @(plan.IsActive ? "badge-success" : "badge-danger")">
                                                @(plan.IsActive ? L["Active"] : L["Inactive"])
                                            </span>
                                        </td>
                                        <td>@plan.TotalAccounts</td>
                                        <td>@plan.ActiveAccounts</td>
                                        <td>@plan.TotalLockedRzw.ToString("N8")</td>
                                        <td>@plan.TotalInterestPaid.ToString("N8")</td>
                                    </tr>
                                }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        // Auto-refresh every 30 seconds
        setTimeout(function() {
            location.reload();
        }, 30000);
    </script>
}
