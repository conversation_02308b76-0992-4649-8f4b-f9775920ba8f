using Microsoft.AspNetCore.Mvc.RazorPages;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels.Deposit;

namespace RazeWinComTr.Areas.Admin.Pages.TransactionReversal;

public class IndexModel : PageModel
{
    private readonly DepositService _depositService;
    private readonly TransactionReversalService _reversalService;
    
    public IndexModel(
        DepositService depositService,
        TransactionReversalService reversalService)
    {
        _depositService = depositService;
        _reversalService = reversalService;
    }
    
    public List<DepositViewModel> Deposits { get; set; } = new();
    
    public Dictionary<int, bool> CanReverseDeposits { get; set; } = new();
    
    public async Task OnGetAsync()
    {
        // Sadece onaylanmış para yatırma işlemlerini getir
        Deposits = await _depositService.GetApprovedDepositsAsync();
        
        // Her bir para yatırma işlemi için geri alınabilirlik durumunu kontrol et
        foreach (var deposit in Deposits)
        {
            var result = await _reversalService.CheckDepositReversalEligibilityAsync(deposit.Id);
            CanReverseDeposits[deposit.Id] = result.CanReverse;
        }
    }
}
