using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;

namespace RazeWinComTr.Areas.Admin.Pages.RzwSavings
{
    [Authorize(Policy = "AdminPolicy")]
    public class DebugTestModel : PageModel
    {
        private readonly IStringLocalizer<SharedResource> _localizer;
        private readonly ILogger<DebugTestModel> _logger;

        public DebugTestModel(
            IStringLocalizer<SharedResource> localizer,
            ILogger<DebugTestModel> logger)
        {
            _localizer = localizer;
            _logger = logger;
        }

        public IActionResult OnGet()
        {
            _logger.LogInformation("Admin accessed RZW Savings Debug Test page");
            return Page();
        }
    }
}
