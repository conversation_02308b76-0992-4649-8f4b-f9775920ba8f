# RZW Vadeli Hesap Sistemi - Faz 3: Savings System Core

## 🎯 AI Agent Görevi
RZW vadeli hesap sisteminin core business logic'ini oluştur. <PERSON><PERSON><PERSON> hesap a<PERSON>, faiz he<PERSON>, erken çekim ve vade dolma işlemlerini implement et.

## 📋 Detaylı Görevler

### 1. RzwSavingsPlanService Oluştur
**Dosya**: `src/Areas/Admin/Services/RzwSavingsPlanService.cs`

#### Dependencies
- AppDbContext
- IStringLocalizer<SharedResource>
- ILogger<RzwSavingsPlanService>

#### Ana Metotlar
- GetActivePlansAsync() - Aktif planları getir
- GetPlanByIdAsync(planId) - Plan detayı
- GetPlanByTermTypeAsync(termType) - Term tipine göre plan
- ValidatePlanAsync(planId, amount) - Plan ve miktar validasyonu
- InitializeDefaultPlansAsync() - <PERSON><PERSON><PERSON><PERSON><PERSON> planları oluştur

#### Default Planlar
- **<PERSON><PERSON><PERSON><PERSON><PERSON>k**: %0.03/gün, Min: 100 RZW, Max: 1,000,000 RZW
- **Aylık**: %1.0/ay, Min: 500 RZW, Max: 5,000,000 RZW  
- **Yıllık**: %15/yıl, Min: 1,000 RZW, Max: 10,000,000 RZW

### 2. RzwSavingsService Oluştur
**Dosya**: `src/Areas/Admin/Services/RzwSavingsService.cs`

#### Dependencies
- AppDbContext
- RzwBalanceManagementService
- RzwSavingsPlanService
- RzwSavingsInterestService
- IStringLocalizer<SharedResource>
- ILogger<RzwSavingsService>

#### Ana Metotlar

##### Vadeli Hesap Açma
```csharp
Task<(bool Success, string Message, RzwSavingsAccount? Account)> CreateSavingsAccountAsync(
    int userId, int planId, decimal rzwAmount, bool autoRenew = false);
```
**İş Akışı**:
1. Plan kontrolü ve validasyonu
2. Kullanıcı bakiye kontrolü
3. RZW kilitleme (RzwBalanceManagementService)
4. Vadeli hesap kaydı oluşturma
5. Vade tarihi hesaplama

##### Erken Çekim
```csharp
Task<(bool Success, string Message, decimal WithdrawnAmount)> EarlyWithdrawAsync(
    int accountId, int userId);
```
**İş Akışı**:
1. Hesap kontrolü ve validasyonu
2. Erken çekim cezası hesaplama (%10)
3. Tutulma süresine göre faiz hesaplama
4. RZW serbest bırakma
5. Kazanılan faizi available balance'a ekleme
6. Ceza düşme (eğer varsa)

##### Vade Dolma
```csharp
Task<(bool Success, string Message)> ProcessMaturityAsync(int accountId);
```
**İş Akışı**:
1. Vade kontrolü
2. RZW serbest bırakma
3. Hesap durumu güncelleme (Matured)

#### Diğer Metotlar
- GetUserActiveSavingsAsync(userId) - Kullanıcının aktif hesapları
- GetSavingsAccountAsync(accountId, userId) - Hesap detayı
- GetMaturedAccountsAsync() - Vadesi dolan hesaplar
- GetAccountsForInterestPaymentAsync() - Faiz ödemesi gereken hesaplar

### 3. RzwSavingsInterestService Oluştur
**Dosya**: `src/Areas/Admin/Services/RzwSavingsInterestService.cs`

#### Dependencies
- AppDbContext
- RzwBalanceManagementService
- RzwSavingsPlanService
- TradeService
- ITokenPriceService
- IStringLocalizer<SharedResource>
- ILogger<RzwSavingsInterestService>

#### Ana Metotlar

##### Günlük Faiz İşleme
```csharp
Task<(int ProcessedAccounts, decimal TotalInterestPaid)> ProcessDailyInterestAsync();
```

##### Bileşik Faiz Hesaplama
```csharp
Task<decimal> CalculateDailyInterestAsync(RzwSavingsAccount account);
```
**Formül**: `(Ana Para + Kazanılan Faizler) × Günlük Oran`

##### Erken Çekim Faiz Hesaplama
```csharp
Task<decimal> CalculateEarlyWithdrawalInterestAsync(RzwSavingsAccount account);
```
**Kurallar**:
1. Tutulma süresini hesapla
2. Uygun daha düşük periyotlu plan bul
3. Uygun plan varsa o planın oranı ile faiz hesapla
4. Uygun plan yoksa hiç faiz ödeme

##### Faiz Ödeme
```csharp
Task<bool> PayInterestAsync(RzwSavingsAccount account, decimal interestAmount);
```
**İş Akışı**:
1. Faiz miktarını available balance'a ekle
2. TotalEarnedRzw güncelle
3. LastInterestDate güncelle
4. InterestPayment kaydı oluştur
5. Trade kaydı oluştur

#### Faiz Hesaplama Kuralları
- **Bileşik Faiz**: Günlük kapitalizasyon
- **Günlük Oran**: Plan tipine göre yıllık oranın günlüğe bölümü
- **Daily**: Direkt oran kullan
- **Monthly**: Oran / 30
- **Yearly**: Oran / 365

### 4. Service Registration
**Dosya**: `src/Program.cs`

```csharp
// RZW Savings Services
builder.Services.AddScoped<RzwSavingsPlanService>();
builder.Services.AddScoped<RzwSavingsService>();
builder.Services.AddScoped<RzwSavingsInterestService>();
builder.Services.AddScoped<RzwBalanceManagementService>();
```

## ✅ Başarı Kriterleri
1. Vadeli hesap açma: Kullanıcı vadeli hesap açabiliyor
2. Faiz hesaplama: Bileşik faiz doğru hesaplanıyor
3. Erken çekim: Ceza ve faiz hesaplamaları doğru
4. Vade dolma: Otomatik vade dolma işlemi çalışıyor
5. Balance operations: RZW lock/unlock işlemleri doğru
6. Trade records: Tüm işlemlerde audit trail oluşuyor
7. Default planlar: Sistem başlangıcında planlar oluşuyor

## 🔧 Test Senaryoları

### Test 1: Vadeli Hesap Açma
```csharp
var result = await savingsService.CreateSavingsAccountAsync(
    userId: 1, planId: 1, rzwAmount: 1000m);
// Beklenen: Success = true, RZW kilitlendi, Trade kaydı oluştu
```

### Test 2: Günlük Faiz Hesaplama
```csharp
var account = new RzwSavingsAccount 
{ 
    RzwAmount = 1000m, 
    TotalEarnedRzw = 50m, 
    InterestRate = 0.0003m 
};
var interest = await interestService.CalculateDailyInterestAsync(account);
// Beklenen: (1000 + 50) * 0.0003 = 0.315 RZW
```

### Test 3: Erken Çekim
```csharp
var withdrawal = await savingsService.EarlyWithdrawAsync(accountId: 1, userId: 1);
// Beklenen: Ceza düşüldü, uygun plan faizi ödendi, RZW serbest bırakıldı
```

### Test 4: Vade Dolma
```csharp
var maturity = await savingsService.ProcessMaturityAsync(accountId: 1);
// Beklenen: RZW serbest bırakıldı, hesap kapatıldı
```

## 📝 Önemli Notlar
- **Bileşik faiz**: Günlük kapitalizasyon ile hesapla
- **Erken çekim faizi**: Tutulma süresine göre uygun plan bul
- **Transaction safety**: Tüm işlemlerde database transaction kullan
- **Audit trail**: Her işlemde Trade kaydı oluştur
- **Error handling**: Kapsamlı hata kontrolü ve logging
- **Validation**: Tüm input'ları validate et

## 🎯 Sonuç Beklentisi
Bu faz sonunda:
- Core business logic tamamlanmış olacak
- Vadeli hesap CRUD işlemleri çalışır olacak
- Faiz hesaplama algoritmaları hazır olacak
- Erken çekim ve vade dolma işlemleri çalışır olacak
- Background service için altyapı hazır olacak

**Tahmini Süre**: 3-4 gün
**Bağımlılıklar**: Faz 1 ve Faz 2 tamamlanmış olmalı
**Sonraki Faz**: Background Services
