@page
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.Services
@using RazeWinComTr.Areas.Admin.DbModel
@model EditModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["Edit Market"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

@if (Model.AlertMessage != null)
{
    <script id="scriptMessage" type="text/javascript">
        window.onload = function () {
            Swal.fire({
                title: '@Html.Raw(Model.AlertMessage.Title)',
                text: '@Html.Raw(Model.AlertMessage.Text)',
                icon: '@Html.Raw(Model.AlertMessage.Icon)'
            }).then((result) => {
                var redirectUrl = '@(Model.AlertMessage.RedirectUrl ?? string.Empty)';
                if (result.isConfirmed && redirectUrl !== '') {
                    location.href = redirectUrl;
                }
            });
            $('#scriptMessage').remove();
        }
    </script>
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["Edit Market"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="/admin/market">@L["Markets"]</a></li>
                    <li class="breadcrumb-item active">@L["Edit"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>
<div asp-validation-summary="ModelOnly" class="text-danger"></div>
<section class="content">
    <div class="container-fluid">
        <div class="card">
            <form method="post" enctype="multipart/form-data">
                <input type="hidden" asp-for="Entity.Id" />
                <input type="hidden" asp-for="Entity.IconUrl" />
                <input type="hidden" asp-for="Entity.OriginalIsApi" />
                <input type="hidden" id="originalIsApi" value="@(Model.Entity.OriginalIsApi ? "true" : "false")" />
                <div class="card-body">
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input asp-for="Entity.IsActive" class="custom-control-input" />
                            <label asp-for="Entity.IsActive" class="custom-control-label">@L["Active"]</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input asp-for="Entity.IsApi" class="custom-control-input" id="isApiCheckbox" />
                            <label asp-for="Entity.IsApi" class="custom-control-label">@L["Is API"]</label>
                            @if (Model.Entity.IsApi)
                            {
                                <small class="form-text text-muted api-readonly-note">@L["This setting cannot be changed once enabled"]</small>
                            }
                        </div>
                    </div>
                    <div class="form-group" id="apiServiceNameGroup" style="display: none;">
                        <label asp-for="Entity.ApiServiceName">@L["API Service"]</label>
                        <select asp-for="Entity.ApiServiceName" class="form-control" id="apiServiceSelect">
                            <option value="">@L["Select API Service"]</option>
                            <option value="Bitexen">Bitexen</option>
                            <option value="BTCTurk">BTCTurk</option>
                        </select>
                    </div>
                    <div class="form-group" id="apiPairGroup" style="display: none;">
                        <label for="apiPairSelect">@L["API Pair"]</label>
                        <select id="apiPairSelect" class="form-control">
                            <option value="">@L["Select Pair"]</option>
                        </select>
                        <small class="form-text text-muted">@L["Select a pair from the API to automatically fill the Pair Code field"]</small>
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.Coin">@L["Coin Symbol"]</label>
                        <input asp-for="Entity.Coin" class="form-control" required />
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.Name">@L["Name"]</label>
                        <input asp-for="Entity.Name" class="form-control" required />
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.ShortName">@L["Short Name"]</label>
                        <input asp-for="Entity.ShortName" class="form-control" required />
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.PairCode">@L["Pair Code"]</label>
                        <input asp-for="Entity.PairCode" class="form-control" required />
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.BuyPrice">@L["Buy Price"]</label>
                        <input asp-for="Entity.BuyPrice" type="text" class="form-control decimal-input" data-decimal-places="8" data-val="true" data-val-decimalComma="@L["Please enter a valid number"]" />
                        <span asp-validation-for="Entity.BuyPrice" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.SellPrice">@L["Sell Price"]</label>
                        <input asp-for="Entity.SellPrice" type="text" class="form-control decimal-input" data-decimal-places="8" data-val="true" data-val-decimalComma="@L["Please enter a valid number"]" />
                        <span asp-validation-for="Entity.SellPrice" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.DecimalPlaces">@L["Decimal Places"]</label>
                        <input asp-for="Entity.DecimalPlaces" class="form-control" type="number" required />
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.MinimumBuy">@L["Minimum Buy"]</label>
                        <input asp-for="Entity.MinimumBuy" type="text" class="form-control decimal-input" data-decimal-places="8" data-val="true" data-val-decimalComma="@L["Please enter a valid number"]" />
                        <span asp-validation-for="Entity.MinimumBuy" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.MaximumBuy">@L["Maximum Buy"]</label>
                        <input asp-for="Entity.MaximumBuy" type="text" class="form-control decimal-input" data-decimal-places="8" data-val="true" data-val-decimalComma="@L["Please enter a valid number"]" />
                        <span asp-validation-for="Entity.MaximumBuy" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.MinimumSell">@L["Minimum Sell"]</label>
                        <input asp-for="Entity.MinimumSell" type="text" class="form-control decimal-input" data-decimal-places="8" data-val="true" data-val-decimalComma="@L["Please enter a valid number"]" />
                        <span asp-validation-for="Entity.MinimumSell" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.MaximumSell">@L["Maximum Sell"]</label>
                        <input asp-for="Entity.MaximumSell" type="text" class="form-control decimal-input" data-decimal-places="8" data-val="true" data-val-decimalComma="@L["Please enter a valid number"]" />
                        <span asp-validation-for="Entity.MaximumSell" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.GeneralIncrease">@L["General Increase"]</label>
                        <input asp-for="Entity.GeneralIncrease" type="text" class="form-control decimal-input" data-decimal-places="8" data-val="true" data-val-decimalComma="@L["Please enter a valid number"]" />
                        <span asp-validation-for="Entity.GeneralIncrease" class="text-danger"></span>
                        <small class="form-text text-muted">@L["This value will be added to the buy and sell prices. Can be negative."]</small>
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.Order">@L["Order"]</label>
                        <input asp-for="Entity.Order" class="form-control" type="number" required />
                    </div>

                    <div class="form-group">
                        <label asp-for="ImageFile">@L["Icon Image"]</label>
                        <input type="file" asp-for="ImageFile" class="form-control" accept="image/*" />
                        @if (!string.IsNullOrEmpty(Model.Entity.IconUrl))
                        {
                            <img src="@(FileService.getGenericDownloadUrl(nameof(Market), Model.Entity.Id.ToString()))" class="mt-2" style="max-height: 100px" alt="@Model.Entity.Name" />
                        }
                    </div>
                </div>
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-1"></i> @L["Save"]
                    </button>
                    <a href="/Admin/Market" class="btn btn-default">
                        <i class="fas fa-times mr-1"></i> @L["Cancel"]
                    </a>
                </div>
            </form>
        </div>
    </div>
</section>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
    <script src="/js/decimal-input-handler.js"></script>
    <script>
        $(document).ready(function() {
            // Check if IsApi was originally true and make the checkbox read-only if it was
            var originalIsApi = $('#originalIsApi').val() === 'true';
            if (originalIsApi) {
                $('#isApiCheckbox').prop('disabled', true);

                // Also make BuyPrice and SellPrice fields read-only immediately
                $('#Entity_BuyPrice').prop('readonly', true).addClass('bg-light')
                    .after('<small class="form-text text-muted api-readonly-note">@L["This field is read-only when using API"]</small>');
                $('#Entity_SellPrice').prop('readonly', true).addClass('bg-light')
                    .after('<small class="form-text text-muted api-readonly-note">@L["This field is read-only when using API"]</small>');

                // Make ApiServiceName and Pair select fields read-only as well
                $('#apiServiceSelect').prop('disabled', true).addClass('bg-light')
                    .after('<small class="form-text text-muted api-readonly-note">@L["This field is read-only once API is enabled"]</small>');
                $('#apiPairSelect').prop('disabled', true).addClass('bg-light')
                    .after('<small class="form-text text-muted api-readonly-note">@L["This field is read-only once API is enabled"]</small>');
            }

            // Function to toggle API service name field visibility and read-only state of fields
            function toggleApiServiceNameField() {
                var isApiChecked = $('#isApiCheckbox').is(':checked');

                if (isApiChecked) {
                    $('#apiServiceNameGroup').show();
                    // Show pair dropdown only if an API service is selected
                    if ($('#apiServiceSelect').val()) {
                        $('#apiPairGroup').show();
                        loadApiPairs($('#apiServiceSelect').val());
                    }

                    // Make API-related fields read-only
                    toggleApiFieldsReadOnly(true);
                } else {
                    $('#apiServiceNameGroup').hide();
                    $('#apiPairGroup').hide();
                    $('#Entity_ApiServiceName').val('');
                    $('#apiPairSelect').empty().append('<option value="">@L["Select Pair"]</option>');

                    // Make API-related fields editable
                    toggleApiFieldsReadOnly(false);
                }
            }

            // Function to toggle read-only state of API-related fields
            function toggleApiFieldsReadOnly(makeReadOnly) {
                // Fields that should be read-only when IsApi is checked
                var apiFields = [
                    '#Entity_Coin',
                    '#Entity_Name',
                    '#Entity_ShortName',
                    '#Entity_PairCode',
                    '#Entity_BuyPrice',
                    '#Entity_SellPrice'
                ];

                apiFields.forEach(function(selector) {
                    var $field = $(selector);
                    $field.prop('readonly', makeReadOnly);

                    // Add or remove visual indication that field is read-only
                    if (makeReadOnly) {
                        $field.addClass('bg-light');
                        // Add a note that the field is read-only when IsApi is checked
                        if (!$field.next('.api-readonly-note').length) {
                            $field.after('<small class="form-text text-muted api-readonly-note">@L["This field is read-only when using API"]</small>');
                        }
                    } else {
                        $field.removeClass('bg-light');
                        $field.next('.api-readonly-note').remove();
                    }
                });

                // Make ApiServiceName and Pair select fields read-only when IsApi is true
                if (makeReadOnly) {
                    // If this is an existing API market (originalIsApi is true), make the dropdowns read-only
                    if ($('#originalIsApi').val() === 'true') {
                        // Make API Service dropdown read-only
                        $('#apiServiceSelect').prop('disabled', true).addClass('bg-light');
                        if (!$('#apiServiceSelect').next('.api-readonly-note').length) {
                            $('#apiServiceSelect').after('<small class="form-text text-muted api-readonly-note">@L["This field is read-only once API is enabled"]</small>');
                        }

                        // Make API Pair dropdown read-only
                        $('#apiPairSelect').prop('disabled', true).addClass('bg-light');
                        if (!$('#apiPairSelect').next('.api-readonly-note').length) {
                            $('#apiPairSelect').after('<small class="form-text text-muted api-readonly-note">@L["This field is read-only once API is enabled"]</small>');
                        }
                    }
                } else {
                    // If we're making fields editable and it's not an original API market, remove the read-only state
                    if ($('#originalIsApi').val() !== 'true') {
                        $('#apiServiceSelect').prop('disabled', false).removeClass('bg-light');
                        $('#apiServiceSelect').next('.api-readonly-note').remove();

                        $('#apiPairSelect').prop('disabled', false).removeClass('bg-light');
                        $('#apiPairSelect').next('.api-readonly-note').remove();
                    }
                }
            }

            // Function to load pairs from the selected API service
            function loadApiPairs(apiServiceName) {
                if (!apiServiceName) {
                    $('#apiPairGroup').hide();
                    return;
                }

                // Show loading indicator
                $('#apiPairSelect').empty().append('<option value="">@L["Loading..."]</option>');
                $('#apiPairGroup').show();

                // Call the API to get the pairs
                $.ajax({
                    url: `/api/ajax/pairs/${apiServiceName}`,
                    type: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        // Clear the dropdown
                        $('#apiPairSelect').empty();
                        $('#apiPairSelect').append('<option value="">@L["Select Pair"]</option>');

                        // Add the pairs to the dropdown
                        if (data && data.length > 0) {
                            $.each(data, function(index, pair) {
                                $('#apiPairSelect').append(`<option value="${pair.pairCode}" data-base="${pair.baseCurrency}" data-quote="${pair.quoteCurrency}">${pair.displayName}</option>`);
                            });

                            // If we have a current pair code, select it in the dropdown
                            var currentPairCode = $('#Entity_PairCode').val();
                            if (currentPairCode) {
                                $('#apiPairSelect option[value="' + currentPairCode + '"]').prop('selected', true);
                            }
                        } else {
                            $('#apiPairSelect').append('<option value="">@L["No pairs found"]</option>');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error loading pairs:', error);
                        $('#apiPairSelect').empty().append('<option value="">@L["Error loading pairs"]</option>');
                    }
                });
            }

            // Initial state
            toggleApiServiceNameField();

            // On change event for IsApi checkbox
            $('#isApiCheckbox').change(function() {
                toggleApiServiceNameField();
            });

            // On change event for API service select
            $('#apiServiceSelect').change(function() {
                var apiServiceName = $(this).val();
                if (apiServiceName) {
                    loadApiPairs(apiServiceName);
                } else {
                    $('#apiPairGroup').hide();
                    $('#apiPairSelect').empty().append('<option value="">@L["Select Pair"]</option>');
                }
            });

            // On change event for API pair select
            $('#apiPairSelect').change(function() {
                var selectedPairCode = $(this).val();
                var selectedOption = $(this).find('option:selected');

                if (selectedPairCode) {
                    // Set the pair code field
                    $('#Entity_PairCode').val(selectedPairCode);

                    // If we have data attributes for base and quote currencies, use them to set coin name fields
                    var baseCurrency = selectedOption.data('base');
                    if (baseCurrency) {
                        $('#Entity_Coin').val(baseCurrency);
                    }

                    // Set the name field based on the display name
                    var displayName = selectedOption.text();
                    if (displayName && displayName !== '@L["Select Pair"]') {
                        $('#Entity_Name').val(displayName);
                        $('#Entity_ShortName').val(displayName);
                    }
                }
            });

            // Add custom validation method for decimal inputs with comma
            $.validator.addMethod('decimalComma', function (value, element) {
                // Allow empty values for optional fields
                if (this.optional(element)) {
                    return true;
                }

                // Check if the value matches the pattern for decimal with comma
                return /^-?\d+(?:.\d+)?$/.test(value);
            }, '@L["Please enter a valid number with comma as decimal separator (e.g., 123,45)"]');

            // Apply the validation method to the form
            $.validator.unobtrusive.adapters.add('decimalComma', [], function (options) {
                options.rules['decimalComma'] = true;
                options.messages['decimalComma'] = options.message;
            });

            // Format decimal values on page load
            $('.decimal-input').each(function() {
                var $input = $(this);
                var value = $input.val();

                // Convert dot to comma for display
                if (value) {
                    value = value.toString().replace(',', '.');
                    $input.val(value);
                }
            });
             $('.decimal-input').keyup(function() {
                var $input = $(this);
                var value = $input.val();

                // Convert dot to comma for display
                if (value) {
                    value = value.toString().replace(',', '.');
                    $input.val(value);
                }
            });
        });
    </script>
}
