using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace SimpleTests
{
    public class RewardDistributionEdgeCaseTests
    {
        [Fact]
        public void DistributeRewards_WithZeroAmount_DistributesZeroRewards()
        {
            // Arrange
            var referrers = new List<(int UserId, int Level, decimal Percentage)>
            {
                (1, 1, 20m), // User 1, Level 1, 20%
                (2, 2, 15m), // User 2, Level 2, 15%
                (3, 3, 10m)  // User 3, Level 3, 10%
            };

            decimal depositAmount = 0m;
            var rewardsDistributed = new List<(int UserId, int Level, decimal Amount, decimal Percentage)>();

            // Act
            foreach (var (userId, level, percentage) in referrers)
            {
                decimal rewardAmount = depositAmount * (percentage / 100m);
                rewardsDistributed.Add((userId, level, rewardAmount, percentage));
            }

            // Assert
            Assert.Equal(expected: 3, actual: rewardsDistributed.Count);

            // Check that all rewards are zero
            foreach (var reward in rewardsDistributed)
            {
                Assert.Equal(expected: 0m, actual: reward.Amount);
            }

            // Check total rewards
            decimal totalRewards = rewardsDistributed.Sum(selector: r => r.Amount);
            Assert.Equal(expected: 0m, actual: totalRewards);
        }

        [Fact]
        public void DistributeRewards_WithCircularReferrals_HandlesCorrectly()
        {
            // Arrange
            // Create a circular referral chain: User1 -> User2 -> User3 -> User1
            var users = new List<(int UserId, int? ReferrerId)>
            {
                (1, 3), // User 1 is referred by User 3
                (2, 1), // User 2 is referred by User 1
                (3, 2), // User 3 is referred by User 2
                (4, 3)  // User 4 is referred by User 3 (will make the deposit)
            };

            var percentages = new Dictionary<int, decimal>
            {
                { 1, 20m }, // Level 1: 20%
                { 2, 15m }, // Level 2: 15%
                { 3, 10m }  // Level 3: 10%
            };

            decimal depositAmount = 1000m;
            var rewardsDistributed = new List<(int UserId, int Level, decimal Amount, decimal Percentage)>();

            // Act
            // Find the referral chain for User 4
            var referralChain = new List<(int UserId, int Level)>();
            int currentUserId = 4;
            int level = 0;
            var processedUsers = new HashSet<int>(); // To detect circular references

            while (true)
            {
                var user = users.FirstOrDefault(predicate: u => u.UserId == currentUserId);
                if (user.ReferrerId == null)
                    break;

                currentUserId = user.ReferrerId.Value;
                level++;

                // If we've already processed this user or reached level 10, stop
                if (processedUsers.Contains(currentUserId) || level > 10)
                    break;

                processedUsers.Add(currentUserId);
                referralChain.Add((currentUserId, level));
            }

            // Distribute rewards
            foreach (var (userId, referrerLevel) in referralChain)
            {
                if (percentages.TryGetValue(referrerLevel, out decimal percentage))
                {
                    decimal rewardAmount = depositAmount * (percentage / 100m);
                    rewardsDistributed.Add((userId, referrerLevel, rewardAmount, percentage));
                }
            }

            // Assert
            Assert.Equal(expected: 3, actual: rewardsDistributed.Count);

            // Check Level 1 reward (20%)
            var level1Reward = rewardsDistributed.FirstOrDefault(predicate: r => r.Level == 1);
            Assert.Equal(expected: 3, actual: level1Reward.UserId);
            Assert.Equal(expected: 20m, actual: level1Reward.Percentage);
            Assert.Equal(expected: 200m, actual: level1Reward.Amount); // 1000 * 0.20 = 200

            // Check Level 2 reward (15%)
            var level2Reward = rewardsDistributed.FirstOrDefault(predicate: r => r.Level == 2);
            Assert.Equal(expected: 2, actual: level2Reward.UserId);
            Assert.Equal(expected: 15m, actual: level2Reward.Percentage);
            Assert.Equal(expected: 150m, actual: level2Reward.Amount); // 1000 * 0.15 = 150

            // Check Level 3 reward (10%)
            var level3Reward = rewardsDistributed.FirstOrDefault(predicate: r => r.Level == 3);
            Assert.Equal(expected: 1, actual: level3Reward.UserId);
            Assert.Equal(expected: 10m, actual: level3Reward.Percentage);
            Assert.Equal(expected: 100m, actual: level3Reward.Amount); // 1000 * 0.10 = 100

            // Check total rewards
            decimal totalRewards = rewardsDistributed.Sum(r => r.Amount);
            Assert.Equal(expected: 450m, actual: totalRewards); // 200 + 150 + 100 = 450
        }

        [Fact]
        public void DistributeRewards_WithVeryLargeAmount_HandlesDecimalPrecisionCorrectly()
        {
            // Arrange
            decimal depositAmount = 1000000000m; // 1 billion
            decimal percentage = 20m;

            // Act
            decimal rewardAmount = depositAmount * (percentage / 100m);

            // Assert
            Assert.Equal(expected: 200000000m, actual: rewardAmount); // 1,000,000,000 * 0.20 = 200,000,000
        }

        [Fact]
        public void DistributeRewards_WithVerySmallAmount_HandlesDecimalPrecisionCorrectly()
        {
            // Arrange
            decimal depositAmount = 0.00000001m; // Very small amount
            decimal percentage = 20m;

            // Act
            decimal rewardAmount = depositAmount * (percentage / 100m);

            // Assert
            Assert.Equal(expected: 0.000000002m, actual: rewardAmount); // 0.00000001 * 0.20 = 0.000000002
        }

        [Fact]
        public void DistributeRewards_WithNoReferrers_ReturnsNoRewards()
        {
            // Arrange
            var referrers = new List<(int UserId, int Level, decimal Percentage)>();
            decimal depositAmount = 1000m;
            var rewardsDistributed = new List<(int UserId, int Level, decimal Amount, decimal Percentage)>();

            // Act
            foreach (var (userId, level, percentage) in referrers)
            {
                decimal rewardAmount = depositAmount * (percentage / 100m);
                rewardsDistributed.Add((userId, level, rewardAmount, percentage));
            }

            // Assert
            Assert.Empty(rewardsDistributed);

            // Check total rewards
            decimal totalRewards = rewardsDistributed.Sum(r => r.Amount);
            Assert.Equal(expected: 0m, actual: totalRewards);
        }
    }
}
