/* RZW Savings Details Page Styles */

.account-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.account-header h2 {
    color: white;
    font-weight: 600;
}

.account-header h3 {
    color: #f8f9fa;
    font-weight: 500;
}

.progress-custom {
    height: 8px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.3);
}

.progress-custom .progress-bar {
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 4px;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    height: 100%;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.stat-card .icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.stat-card .value {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    word-break: break-all;
}

.stat-card .label {
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 500;
}

.projection-item {
    text-align: center;
    padding: 1rem;
}

.projection-label {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.projection-value {
    font-size: 1.1rem;
    font-weight: 600;
    word-break: break-all;
}

.early-withdraw-warning {
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    padding: 1.5rem;
    border-radius: 10px;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.early-withdraw-warning h5 {
    color: #d63031;
    font-weight: 600;
}

.early-withdraw-warning p {
    color: #2d3436;
}

.interest-table .card {
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.interest-table .card-header {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    color: white;
    border: none;
}

.interest-table .table {
    margin-bottom: 0;
}

.interest-table .table th {
    border-top: none;
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.interest-table .table td {
    vertical-align: middle;
}

.badge-lg {
    font-size: 1rem;
    padding: 0.5rem 1rem;
}

/* Plan Details Card */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
}

.card-header {
    background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
    color: white;
    border: none;
    border-radius: 10px 10px 0 0 !important;
}

.card-header h5 {
    font-weight: 600;
}

.card-body dl.row dt {
    font-weight: 600;
    color: #495057;
}

.card-body dl.row dd {
    color: #6c757d;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .account-header {
        padding: 1.5rem;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .stat-card .value {
        font-size: 1rem;
    }
    
    .projection-value {
        font-size: 1rem;
    }
    
    .early-withdraw-warning {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .account-header h2 {
        font-size: 1.5rem;
    }
    
    .account-header h3 {
        font-size: 1.25rem;
    }
    
    .stat-card .icon {
        font-size: 1.5rem;
    }
    
    .stat-card .value {
        font-size: 0.9rem;
    }
    
    .stat-card .label {
        font-size: 0.75rem;
    }
}

/* Animation for cards */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card, .stat-card {
    animation: fadeInUp 0.5s ease-out;
}

/* Badge styles */
.badge-success {
    background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
}

.badge-secondary {
    background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
}

.badge-info {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
}

.badge-warning {
    background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
}
