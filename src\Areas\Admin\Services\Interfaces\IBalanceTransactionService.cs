using RazeWinComTr.Areas.Admin.Data;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.ViewModels.BalanceTransaction;

namespace RazeWinComTr.Areas.Admin.Services.Interfaces;

public interface IBalanceTransactionService
{
    Task<BalanceTransaction?> GetByIdAsync(int id);
    Task<List<BalanceTransactionViewModel>> GetListAsync(int? userId = null, string? transactionType = null);
    Task<List<BalanceTransactionViewModel>> GetByUserIdAsync(int userId);
    Task<BalanceTransaction> RecordTransactionAsync(
        int userId,
        string transactionType,
        decimal amount,
        decimal previousBalance,
        decimal newBalance,
        string? description = null,
        int? referenceId = null,
        string? referenceType = null,
        AppDbContext? existingContext = null);
    Task DeleteAsync(int id);
    Task UpdateAsync(BalanceTransaction transaction);
}
