using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels;

namespace RazeWinComTr.Areas.Admin.Pages.PackageRewardPercentage;

public class DeleteModel : PageModel
{
    private readonly PackageRewardPercentageService _percentageService;
    private readonly PackageService _packageService;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public DeleteModel(
        PackageRewardPercentageService percentageService,
        PackageService packageService,
        IStringLocalizer<SharedResource> localizer)
    {
        _percentageService = percentageService;
        _packageService = packageService;
        _localizer = localizer;
    }

    public SweetAlert2Message? AlertMessage { get; set; }

    [BindProperty] public int Id { get; set; }

    public DbModel.PackageRewardPercentage? Entity { get; set; }
    public string PackageName { get; set; } = string.Empty;
    public string? ErrorMessage { get; set; }

    public async Task<IActionResult> OnGetAsync(int id)
    {
        Id = id;
        Entity = await _percentageService.GetByIdAsync(id);

        if (Entity == null) return NotFound();

        var package = await _packageService.GetByIdAsync(Entity.PackageId);
        if (package != null)
        {
            PackageName = package.Name;
        }

        ViewData["WarningTitle"] = _localizer["Warning"];
        ViewData["WarningRecord"] = _localizer["This record will be permanently deleted"];
        ViewData["WarningUnrecoverable"] = _localizer["This action is irreversible"];

        return Page();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        try
        {
            await _percentageService.DeleteAsync(Id);

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Success"],
                Text = _localizer["Deletion was successful"],
                Icon = "success",
                RedirectUrl = "/Admin/PackageRewardPercentage"
            };

            return Page();
        }
        catch (Exception ex)
        {
            ErrorMessage = ex.Message;
            Entity = await _percentageService.GetByIdAsync(Id);
            
            if (Entity != null)
            {
                var package = await _packageService.GetByIdAsync(Entity.PackageId);
                if (package != null)
                {
                    PackageName = package.Name;
                }
            }
            
            return Page();
        }
    }
}
