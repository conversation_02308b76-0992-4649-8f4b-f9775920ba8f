@page
@using Microsoft.Extensions.Localization
@model RazeWinComTr.Areas.Admin.Pages.RzwSavingsPlans.IndexModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["Savings Plans"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["Savings Plans"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="#">@L["RZW Savings"]</a></li>
                    <li class="breadcrumb-item active">@L["Savings Plans"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <a id="btnCreateNew" asp-page="Create" class="btn btn-success" role="button"
                   aria-label="@($"{L["Create a New"]} {L["Plan"]}")" title="@($"{L["Create a New"]} {L["Plan"]}")">
                    <i class="fas fa-plus fa-2x"></i>
                </a>
                <h3 class="card-title float-right">@L["Count"]: @(Model.Plans.Count)</h3>
                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle"></i> @L["Note"]: @L["RZW Savings plans define the terms and interest rates for savings accounts."]
                </div>
            </div>
            <div class="card-body">
                <table class="table table-bordered table-striped datatable">
                    <thead>
                    <tr>
                        <th>@L["Name"]</th>
                        <th>@L["Term Duration"]</th>
                        <th>@L["Interest Rate"]</th>
                        <th>@L["Min Amount"]</th>
                        <th>@L["Max Amount"]</th>
                        <th>@L["Total Accounts"]</th>
                        <th>@L["Total Locked RZW"]</th>
                        <th>@L["Status"]</th>
                        <th>@L["Display Order"]</th>
                        <th style="width: 150px">@L["Actions"]</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach (var item in Model.Plans)
                    {
                        <tr>
                            <td>@item.Name</td>
                            <td>@item.GetLocalizedTermDisplayText(Model.Localizer)</td>
                            <td>@item.InterestRateDisplayText</td>
                            <td>@item.MinRzwAmount.ToString("N8")</td>
                            <td>@item.MaxAmountDisplayText</td>
                            <td>@item.TotalAccounts</td>
                            <td>@item.TotalLockedRzw.ToString("N8")</td>
                            <td>
                                <span class="badge @(item.IsActive ? "badge-success" : "badge-danger")">
                                    @item.StatusDisplayText
                                </span>
                            </td>
                            <td>@item.DisplayOrder</td>
                            <td>
                                <a href="/Admin/RzwSavingsPlans/Edit?id=@item.Id" class="btn btn-info btn-sm">
                                    <i class="fas fa-pencil-alt"></i>
                                </a>
                                <a href="/Admin/RzwSavingsPlans/Delete?id=@item.Id" class="btn btn-danger btn-sm">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </td>
                        </tr>
                    }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>
